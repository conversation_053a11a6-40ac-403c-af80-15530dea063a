<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Augment HMR Index</title>
    <script nonce="nonce-L7Ri0GZ3gVLOYF2aIYF7Mw==">
/**
 * Monaco bootstrap script
 *
 * This script is included directly in HTML files to load Monaco editor.
 * It's kept as a simple JS file to avoid any build/transpilation requirements.
 */

// Define the Monaco CDN version
const MONACO_VERSION = "0.52.2";
const MONACO_CDN_BASE = `https://cdnjs.cloudflare.com/ajax/libs/monaco-editor/${MONACO_VERSION}/min`;

// Initialize augmentDeps if it doesn't exist
window.augmentDeps = window.augmentDeps || {};

// Create a promise that will resolve when Monaco is ready
let monacoResolve;
window.augmentDeps.monaco = new Promise((resolve) => {
  monacoResolve = resolve;
});

// If Monaco is already loaded, don't load it again
if (window.monaco) {
  console.log("Monaco already loaded, skipping bootstrap");
  initializeMonacoDeps();
} else {
  // Load the Monaco loader script
  const loaderScript = document.createElement("script");
  loaderScript.src = `${MONACO_CDN_BASE}/vs/loader.min.js`;
  loaderScript.onload = initializeMonaco;
  document.head.appendChild(loaderScript);
}

// Initialize Monaco after the loader script has loaded
function initializeMonaco() {
  // require is provided by loader.min.js
  require.config({
    paths: { vs: `${MONACO_CDN_BASE}/vs` },
  });

  require(["vs/editor/editor.main"], () => {
    initializeMonacoDeps();
  });
}

// Initialize Monaco dependencies after Monaco has loaded
function initializeMonacoDeps() {
  // Resolve the monaco promise
  if (monacoResolve) {
    monacoResolve(window.monaco);
  }
}

</script>
    <style nonce="nonce-L7Ri0GZ3gVLOYF2aIYF7Mw==">
      code {
        display:flex;
        flex:1;
        white-space: pre;
        border:1px solid red;
        background:rgba(255, 0, 0, 0.1);
        padding:10px;
      }
      code:empty {
        display:none;
      }

    </style>
    <meta property="csp-nonce" nonce="nonce-L7Ri0GZ3gVLOYF2aIYF7Mw==">
  </head>
  <body>
   <h2>Augment HMR Index</h2>
   <p>If you are seeing this in your browser congratulations, the HMR server, is up and running.</p>
   <p>If you are seeing this in your vscode webview, then something is wrong, and keep reading</p>
   <div>
    <p>If it is not working here are a few things to check:</p>
    <ul>
        <li>Try cmd-R to reload the extension</li>
        <li>Check the output of "Start HMR Server" task for errors</li>
        <li>Check the output of "Watch Extension (Bazel) Internal" tasks to ensure its complete, without errors</li>
        <li>Try deleting ./.augment-hmr-env and launching the extension again.</li>
        <li>If you can't get HMR to work you can skip it and use the "Run Extension (Bazel) No HMR" launcher in vscode.</li>
        <li>Likely you need to fix port forwarding, you can set an open port in ./.augment-hmr-env, both AUGMENT_HMR, AUGMENT_HMR_PORT need to be set.</li>
    </ul>

    <!--this is where the error gets replaced-->
    <code></code>
   </div>
  </body>
</html>
