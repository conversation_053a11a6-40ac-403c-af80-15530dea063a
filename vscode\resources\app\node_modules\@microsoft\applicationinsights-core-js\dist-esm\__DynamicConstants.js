/*
 * Application Insights JavaScript SDK - Core, 2.8.15
 * Copyright (c) Microsoft and contributors. All rights reserved.
 */


// @skip-file-minify
// ##############################################################
// AUTO GENERATED FILE: This file is Auto Generated during build.
// ##############################################################
// !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
// Note: DON'T Export these const from the package as we are still targeting ES3 this will export a mutable variables that someone could change!!!
// !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
export var _DYN_INITIALIZE = "initialize"; // Count: 8
export var _DYN_NAME = "name"; // Count: 11
export var _DYN_GET_NOTIFY_MGR = "getNotifyMgr"; // Count: 3
export var _DYN_IDENTIFIER = "identifier"; // Count: 8
export var _DYN_PUSH = "push"; // Count: 30
export var _DYN_IS_INITIALIZED = "isInitialized"; // Count: 10
export var _DYN_CONFIG = "config"; // Count: 7
export var _DYN_INSTRUMENTATION_KEY = "instrumentationKey"; // Count: 3
export var _DYN_LOGGER = "logger"; // Count: 10
export var _DYN_LENGTH = "length"; // Count: 44
export var _DYN_TIME = "time"; // Count: 5
export var _DYN_PROCESS_NEXT = "processNext"; // Count: 21
export var _DYN_GET_PROCESS_TEL_CONT0 = "getProcessTelContext"; // Count: 2
export var _DYN_ADD_NOTIFICATION_LIS1 = "addNotificationListener"; // Count: 5
export var _DYN_REMOVE_NOTIFICATION_2 = "removeNotificationListener"; // Count: 5
export var _DYN_STOP_POLLING_INTERNA3 = "stopPollingInternalLogs"; // Count: 2
export var _DYN_ON_COMPLETE = "onComplete"; // Count: 6
export var _DYN_GET_PLUGIN = "getPlugin"; // Count: 5
export var _DYN_FLUSH = "flush"; // Count: 5
export var _DYN__EXTENSIONS = "_extensions"; // Count: 4
export var _DYN_SPLICE = "splice"; // Count: 6
export var _DYN_TEARDOWN = "teardown"; // Count: 10
export var _DYN_MESSAGE_ID = "messageId"; // Count: 4
export var _DYN_MESSAGE = "message"; // Count: 7
export var _DYN_IS_ASYNC = "isAsync"; // Count: 7
export var _DYN__DO_TEARDOWN = "_doTeardown"; // Count: 4
export var _DYN_UPDATE = "update"; // Count: 7
export var _DYN_GET_NEXT = "getNext"; // Count: 12
export var _DYN_DIAG_LOG = "diagLog"; // Count: 8
export var _DYN_SET_NEXT_PLUGIN = "setNextPlugin"; // Count: 5
export var _DYN_CREATE_NEW = "createNew"; // Count: 6
export var _DYN_COOKIE_CFG = "cookieCfg"; // Count: 3
export var _DYN_INDEX_OF = "indexOf"; // Count: 6
export var _DYN_SUBSTRING = "substring"; // Count: 10
export var _DYN_USER_AGENT = "userAgent"; // Count: 5
export var _DYN_SPLIT = "split"; // Count: 5
export var _DYN_SET_ENABLED = "setEnabled"; // Count: 5
export var _DYN_SUBSTR = "substr"; // Count: 6
export var _DYN_NODE_TYPE = "nodeType"; // Count: 3
export var _DYN_APPLY = "apply"; // Count: 6
export var _DYN_REPLACE = "replace"; // Count: 10
export var _DYN_ENABLE_DEBUG_EXCEPTI4 = "enableDebugExceptions"; // Count: 2
export var _DYN_LOG_INTERNAL_MESSAGE = "logInternalMessage"; // Count: 2
export var _DYN_TO_LOWER_CASE = "toLowerCase"; // Count: 5
export var _DYN_CALL = "call"; // Count: 18
export var _DYN_TYPE = "type"; // Count: 14
export var _DYN_HANDLER = "handler"; // Count: 5
export var _DYN_LISTENERS = "listeners"; // Count: 6
export var _DYN_IS_CHILD_EVT = "isChildEvt"; // Count: 3
export var _DYN_GET_CTX = "getCtx"; // Count: 6
export var _DYN_SET_CTX = "setCtx"; // Count: 10
export var _DYN_COMPLETE = "complete"; // Count: 3
export var _DYN_TRACE_ID = "traceId"; // Count: 5
export var _DYN_SPAN_ID = "spanId"; // Count: 5
export var _DYN_TRACE_FLAGS = "traceFlags"; // Count: 6
export var _DYN_VERSION = "version"; // Count: 4
//# sourceMappingURL=https://main.vscode-cdn.net/sourcemaps/488a1f239235055e34e673291fb8d8c810886f81/node_modules/@microsoft/applicationinsights-core-js/dist-esm/__DynamicConstants.js.map