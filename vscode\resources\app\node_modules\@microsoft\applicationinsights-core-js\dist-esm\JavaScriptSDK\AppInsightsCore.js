/*
 * Application Insights JavaScript SDK - Core, 2.8.15
 * Copyright (c) Microsoft and contributors. All rights reserved.
 */
import { __extendsFn as __extends } from "@microsoft/applicationinsights-shims";


import dynamicProto from "@microsoft/dynamicproto-js";
import { _DYN_GET_NOTIFY_MGR, _DYN_INITIALIZE, _DYN_NAME } from "../__DynamicConstants";
import { BaseCore } from "./BaseCore";
import { DiagnosticLogger } from "./DiagnosticLogger";
import { isNullOrUndefined, throwError } from "./HelperFuncs";
import { STR_EVENTS_DISCARDED, STR_GET_PERF_MGR } from "./InternalConstants";
import { NotificationManager } from "./NotificationManager";
import { doPerf } from "./PerfManager";
var AppInsightsCore = /** @class */ (function (_super) {
    __extends(AppInsightsCore, _super);
    function AppInsightsCore() {
        var _this = _super.call(this) || this;
        dynamicProto(AppInsightsCore, _this, function (_self, _base) {
            _self[_DYN_INITIALIZE /* @min:%2einitialize */] = function (config, extensions, logger, notificationManager) {
                _base[_DYN_INITIALIZE /* @min:%2einitialize */](config, extensions, logger || new DiagnosticLogger(config), notificationManager || new NotificationManager(config));
            };
            _self.track = function (telemetryItem) {
                doPerf(_self[STR_GET_PERF_MGR /* @min:%2egetPerfMgr */](), function () { return "AppInsightsCore:track"; }, function () {
                    if (telemetryItem === null) {
                        _notifyInvalidEvent(telemetryItem);
                        // throw error
                        throwError("Invalid telemetry item");
                    }
                    // do basic validation before sending it through the pipeline
                    _validateTelemetryItem(telemetryItem);
                    _base.track(telemetryItem);
                }, function () { return ({ item: telemetryItem }); }, !(telemetryItem.sync));
            };
            function _validateTelemetryItem(telemetryItem) {
                if (isNullOrUndefined(telemetryItem[_DYN_NAME /* @min:%2ename */])) {
                    _notifyInvalidEvent(telemetryItem);
                    throwError("telemetry name required");
                }
            }
            function _notifyInvalidEvent(telemetryItem) {
                var manager = _self[_DYN_GET_NOTIFY_MGR /* @min:%2egetNotifyMgr */]();
                if (manager) {
                    manager[STR_EVENTS_DISCARDED /* @min:%2eeventsDiscarded */]([telemetryItem], 2 /* eEventsDiscardedReason.InvalidEvent */);
                }
            }
        });
        return _this;
    }
// Removed Stub for AppInsightsCore.prototype.initialize.
// Removed Stub for AppInsightsCore.prototype.track.
    // This is a workaround for an IE8 bug when using dynamicProto() with classes that don't have any
    // non-dynamic functions or static properties/functions when using uglify-js to minify the resulting code.
    // this will be removed when ES3 support is dropped.
    AppInsightsCore.__ieDyn=1;

    return AppInsightsCore;
}(BaseCore));
export { AppInsightsCore };
//# sourceMappingURL=https://main.vscode-cdn.net/sourcemaps/488a1f239235055e34e673291fb8d8c810886f81/node_modules/@microsoft/applicationinsights-core-js/dist-esm/JavaScriptSDK/AppInsightsCore.js.map