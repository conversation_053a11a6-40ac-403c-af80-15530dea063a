import{S as P,i as S,s as T,d as k,D as v,t as u,q as $,c as N,E as w,F as D,G as z,h as j,I as A,J as b,K as h,L as q,M as L,o as E,p as F,N as G,n as I}from"./SpinnerAugment-VfHtkDdv.js";import{C as J}from"./copy-MzH1hy8q.js";import{S as K}from"./TextAreaAugment-BnS2cUNC.js";const M=i=>({}),g=i=>({}),V=i=>({}),x=i=>({});function B(i){let t;const e=i[10].text,n=b(e,i,i[11],g);return{c(){n&&n.c()},m(o,s){n&&n.m(o,s),t=!0},p(o,s){n&&n.p&&(!t||2048&s)&&h(n,e,o,o[11],t?L(e,o[11],s,M):q(o[11]),g)},i(o){t||($(n,o),t=!0)},o(o){u(n,o),t=!1},d(o){n&&n.d(o)}}}function H(i){let t,e;return t=new J({}),{c(){z(t.$$.fragment)},m(n,o){w(t,n,o),e=!0},p:I,i(n){e||($(t.$$.fragment,n),e=!0)},o(n){u(t.$$.fragment,n),e=!1},d(n){v(t,n)}}}function O(i){let t;const e=i[10].icon,n=b(e,i,i[11],x);return{c(){n&&n.c()},m(o,s){n&&n.m(o,s),t=!0},p(o,s){n&&n.p&&(!t||2048&s)&&h(n,e,o,o[11],t?L(e,o[11],s,V):q(o[11]),x)},i(o){t||($(n,o),t=!0)},o(o){u(n,o),t=!1},d(o){n&&n.d(o)}}}function Q(i){let t,e,n,o;const s=[O,H],r=[];function d(c,l){return c[8].icon?0:1}return t=d(i),e=r[t]=s[t](i),{c(){e.c(),n=G()},m(c,l){r[t].m(c,l),N(c,n,l),o=!0},p(c,l){let p=t;t=d(c),t===p?r[t].p(c,l):(E(),u(r[p],1,1,()=>{r[p]=null}),F(),e=r[t],e?e.p(c,l):(e=r[t]=s[t](c),e.c()),$(e,1),e.m(n.parentNode,n))},i(c){o||($(e),o=!0)},o(c){u(e),o=!1},d(c){c&&k(n),r[t].d(c)}}}function R(i){let t,e,n;return e=new K({props:{defaultColor:i[2],size:i[0],variant:i[1],loading:i[7],stickyColor:i[4],tooltip:{neutral:i[3],success:"Copied!"},stateVariant:{success:"soft"},onClick:i[5],icon:!i[8].text,tooltipNested:i[6],$$slots:{iconLeft:[Q],default:[B]},$$scope:{ctx:i}}}),{c(){t=D("span"),z(e.$$.fragment),j(t,"class","c-copy-button svelte-tq93gm")},m(o,s){N(o,t,s),w(e,t,null),n=!0},p(o,[s]){const r={};4&s&&(r.defaultColor=o[2]),1&s&&(r.size=o[0]),2&s&&(r.variant=o[1]),128&s&&(r.loading=o[7]),16&s&&(r.stickyColor=o[4]),8&s&&(r.tooltip={neutral:o[3],success:"Copied!"}),32&s&&(r.onClick=o[5]),256&s&&(r.icon=!o[8].text),64&s&&(r.tooltipNested=o[6]),2304&s&&(r.$$scope={dirty:s,ctx:o}),e.$set(r)},i(o){n||($(e.$$.fragment,o),n=!0)},o(o){u(e.$$.fragment,o),n=!1},d(o){o&&k(t),v(e)}}}function U(i,t){return new Promise(e=>setTimeout(e,i,t))}function W(i,t,e){let{$$slots:n={},$$scope:o}=t;const s=A(n);let{size:r=1}=t,{variant:d="ghost-block"}=t,{color:c="neutral"}=t,{text:l}=t,{tooltip:p="Copy"}=t,{stickyColor:m=!1}=t,{onCopy:C=async()=>{if(l!==void 0){e(7,f=!0);try{await Promise.all([navigator.clipboard.writeText(typeof l=="string"?l:await l()),U(250)])}finally{e(7,f=!1)}return"success"}}}=t,{tooltipNested:y}=t,f=!1;return i.$$set=a=>{"size"in a&&e(0,r=a.size),"variant"in a&&e(1,d=a.variant),"color"in a&&e(2,c=a.color),"text"in a&&e(9,l=a.text),"tooltip"in a&&e(3,p=a.tooltip),"stickyColor"in a&&e(4,m=a.stickyColor),"onCopy"in a&&e(5,C=a.onCopy),"tooltipNested"in a&&e(6,y=a.tooltipNested),"$$scope"in a&&e(11,o=a.$$scope)},[r,d,c,p,m,C,y,f,s,l,n,o]}class _ extends P{constructor(t){super(),S(this,t,W,R,T,{size:0,variant:1,color:2,text:9,tooltip:3,stickyColor:4,onCopy:5,tooltipNested:6})}}export{_ as C};
