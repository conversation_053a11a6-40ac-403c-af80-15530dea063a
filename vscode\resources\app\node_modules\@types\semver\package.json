{"name": "@types/semver", "version": "7.5.8", "description": "TypeScript definitions for semver", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/semver", "license": "MIT", "contributors": [{"name": "<PERSON>", "githubUsername": "Bartvds", "url": "https://github.com/Bartvds"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/BendingBender"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/Lucian<PERSON>zo"}, {"name": "<PERSON>", "githubUsername": "a<PERSON><PERSON><PERSON>", "url": "https://github.com/ajafff"}, {"name": "ExE Boss", "githubUsername": "ExE-Boss", "url": "https://github.com/ExE-Boss"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/semver"}, "scripts": {}, "dependencies": {}, "typesPublisherContentHash": "eeb34e966b621b4e47b2b11e63847d881e897b4ef9ec7a909c8d3730f7f3d6f8", "typeScriptVersion": "4.6"}