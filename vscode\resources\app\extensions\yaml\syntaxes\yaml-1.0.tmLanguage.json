{"information_for_contributors": ["This file has been converted from https://github.com/RedCMD/YAML-Syntax-Highlighter/blob/master/syntaxes/yaml-1.0.tmLanguage.json", "If you want to provide a fix or improvement, please create a pull request against the original repository.", "Once accepted there, we are happy to receive an update request."], "version": "https://github.com/RedCMD/YAML-Syntax-Highlighter/commit/0b50d9c47145df62c4461c4407698a24e8f1f3c2", "name": "YAML 1.0", "scopeName": "source.yaml.1.0", "comment": "https://yaml.org/spec/1.0/", "patterns": [{"include": "#stream"}], "repository": {"stream": {"patterns": [{"comment": "allows me to just use `\\G` instead of the performance heavy `(^|\\G)`", "begin": "^(?!\\G)", "while": "^", "name": "meta.stream.yaml", "patterns": [{"include": "source.yaml.1.1#byte-order-mark"}, {"include": "#directives"}, {"include": "#document"}, {"include": "source.yaml.1.1#presentation-detail"}]}, {"begin": "\\G", "while": "\\G", "name": "meta.stream.yaml", "patterns": [{"include": "source.yaml.1.1#byte-order-mark"}, {"include": "#directives"}, {"include": "#document"}, {"include": "source.yaml.1.1#presentation-detail"}]}]}, "directive-YAML": {"comment": "https://yaml.org/spec/1.2.2/#681-yaml-directives", "begin": "(?=%YAML:1\\.0(?=[\\x{85 2028 2029}\r\n\t ]))", "end": "\\G(?=%(?!YAML:1\\.0))", "name": "meta.1.0.yaml", "patterns": [{"comment": "https://yaml.org/spec/1.2.2/#681-yaml-directives", "begin": "\\G(%)(YAML)(:)(1\\.0)", "while": "\\G(?!---[\\x{85 2028 2029}\r\n\t ])", "beginCaptures": {"1": {"name": "punctuation.definition.directive.begin.yaml"}, "2": {"name": "keyword.other.directive.yaml.yaml"}, "3": {"name": "punctuation.whitespace.separator.yaml"}, "4": {"name": "constant.numeric.yaml-version.yaml"}}, "name": "meta.directives.yaml", "patterns": [{"include": "source.yaml.1.1#directive-invalid"}, {"include": "#directives"}, {"include": "source.yaml.1.1#presentation-detail"}]}, {"begin": "\\G(?=---[\\x{85 2028 2029}\r\n\t ])", "while": "\\G(?!%)", "patterns": [{"include": "#document"}, {"include": "source.yaml.1.1#presentation-detail"}]}, {"include": "source.yaml.1.1#presentation-detail"}]}, "directives": {"comment": "https://yaml.org/spec/1.2.2/#68-directives", "patterns": [{"include": "source.yaml.1.3#directive-YAML"}, {"include": "source.yaml.1.2#directive-YAML"}, {"include": "source.yaml.1.1#directive-YAML"}, {"include": "source.yaml.1.0#directive-YAML"}, {"begin": "(?=%)", "while": "\\G(?!%|---[\\x{85 2028 2029}\r\n\t ])", "name": "meta.directives.yaml", "patterns": [{"comment": "https://yaml.org/spec/1.2.2/#rule-ns-reserved-directive", "begin": "(%)([^: \\p{Cntrl}\\p{Surrogate}\\x{2028 2029 FFFE FFFF}]++)", "end": "$", "beginCaptures": {"1": {"name": "punctuation.definition.directive.begin.yaml"}, "2": {"name": "keyword.other.directive.other.yaml"}}, "patterns": [{"match": "\\G(:)([^ \\p{Cntrl}\\p{Surrogate}\\x{2028 2029 FFFE FFFF}]++)", "captures": {"1": {"name": "punctuation.whitespace.separator.yaml"}, "2": {"name": "string.unquoted.directive-name.yaml"}}}, {"include": "source.yaml.1.1#presentation-detail"}]}, {"match": "\\G\\.{3}(?=[\\x{85 2028 2029}\r\n\t ])", "name": "invalid.illegal.entity.other.document.end.yaml"}, {"include": "source.yaml.1.1#presentation-detail"}]}]}, "document": {"comment": "https://yaml.org/spec/1.2.2/#91-documents", "patterns": [{"begin": "---(?=[\\x{85 2028 2029}\r\n\t ])", "while": "\\G(?!(?>\\.{3}|---)[\\x{85 2028 2029}\r\n\t ])", "beginCaptures": {"0": {"name": "entity.other.document.begin.yaml"}}, "name": "meta.document.yaml", "patterns": [{"include": "#block-node"}]}, {"begin": "(?=\\.{3}[\\x{85 2028 2029}\r\n\t ])", "while": "\\G(?=[\t \\x{FEFF}]*+(?>#|$))", "patterns": [{"begin": "\\G\\.{3}", "end": "$", "beginCaptures": {"0": {"name": "entity.other.document.end.yaml"}}, "patterns": [{"include": "source.yaml.1.1#presentation-detail"}]}, {"include": "source.yaml.1.1#byte-order-mark"}, {"include": "source.yaml.1.1#presentation-detail"}]}, {"begin": "\\G(?!%|[\t \\x{FEFF}]*+(?>#|$))", "while": "\\G(?!(?>\\.{3}|---)[\\x{85 2028 2029}\r\n\t ])", "name": "meta.document.yaml", "patterns": [{"include": "#block-node"}]}]}, "block-node": {"patterns": [{"include": "#block-sequence"}, {"include": "#block-mapping"}, {"include": "#block-scalar"}, {"include": "source.yaml.1.1#anchor-property"}, {"include": "#tag-property"}, {"include": "source.yaml.1.1#alias"}, {"begin": "(?=\"|')", "while": "\\G", "patterns": [{"begin": "(?!\\G)", "while": "\\G", "patterns": [{"include": "source.yaml.1.1#presentation-detail"}]}, {"include": "#double"}, {"include": "source.yaml.1.1#single"}]}, {"begin": "(?={)", "end": "$", "patterns": [{"include": "#flow-mapping"}, {"include": "source.yaml.1.1#presentation-detail"}]}, {"begin": "(?=\\[)", "end": "$", "patterns": [{"include": "#flow-sequence"}, {"include": "source.yaml.1.1#presentation-detail"}]}, {"include": "source.yaml.1.1#block-plain-out"}, {"include": "source.yaml.1.1#presentation-detail"}]}, "block-mapping": {"//": "The check for plain keys is expensive", "begin": "(?=((?<=[-?:]) )?+)(?<![^\t ][\t ]*+:|---)\\G( *+)([\t ]*+)((?>[!&*][^\\x{85 2028 2029}\r\n\t ]*+[\t ]++)*+)(?=(?>(?#Double Quote)\"(?>[^\\\\\"]++|\\\\.)*+\"|(?#Single Quote)'(?>[^']++|'')*+'|(?#Flow-Map){(?>[^\\x{85 2028 2029}}]++|}[ \t]*+(?!:[\\x{85 2028 2029}\r\n\t ]))++}|(?#Flow-Seq)\\[(?>[^\\x{85 2028 2029}\\]]++|][ \t]*+(?!:[\\x{85 2028 2029}\r\n\t ]))++]|(?#Plain)(?>[^-?:,\\[\\]{}#&*!|>'\"%@` \\p{Cntrl}\\p{Surrogate}\\x{FFFE FFFF}]|[?:-](?![\\x{85 2028 2029}\r\n\t ]))(?>[^:#]++|:(?![\\x{85 2028 2029}\r\n\t ])|(?<! |\t)#++)*+)?+(?#Map Value)[\t ]*+:[\\x{85 2028 2029}\r\n\t ]|(?#Explicit)\\?[\\x{85 2028 2029}\r\n\t ])", "while": "\\G(?>(\\1\\2)((?>[!&*][^\\x{85 2028 2029}\r\n\t ]*+[\t ]++)*+)((?>\t[\t ]*+)?+[^\\x{85 2028 2029}\r\n\t ?:\\-#!&*\"'\\[\\]{}0-9A-Za-z$()+./;<=\\\\^_~\\x{A0}-\\x{D7FF}\\x{E000}-\\x{FFFD}\\x{010000}-\\x{10FFFF}])?+|( *+)([\t ]*+[^\\x{85 2028 2029}\r\n#])?+)", "beginCaptures": {"2": {"name": "punctuation.whitespace.indentation.yaml"}, "3": {"name": "punctuation.whitespace.separator.yaml"}, "4": {"comment": "May cause lag on long lines starting with a tag, anchor or alias", "patterns": [{"include": "#tag-property"}, {"include": "source.yaml.1.1#anchor-property"}, {"include": "source.yaml.1.1#alias"}, {"include": "source.yaml.1.1#presentation-detail"}]}}, "whileCaptures": {"1": {"name": "punctuation.whitespace.indentation.yaml"}, "2": {"comment": "May cause lag on long lines starting with a tag, anchor or alias", "patterns": [{"include": "#tag-property"}, {"include": "source.yaml.1.1#anchor-property"}, {"include": "source.yaml.1.1#alias"}, {"include": "source.yaml.1.1#presentation-detail"}]}, "3": {"name": "invalid.illegal.expected-indentation.yaml"}, "4": {"name": "punctuation.whitespace.indentation.yaml"}, "5": {"name": "invalid.illegal.expected-indentation.yaml"}}, "name": "meta.mapping.yaml", "patterns": [{"include": "#block-map-key-double"}, {"include": "source.yaml#block-map-key-single"}, {"include": "source.yaml.1.1#block-map-key-plain"}, {"include": "#block-map-key-explicit"}, {"include": "#block-map-value"}, {"include": "#flow-mapping"}, {"include": "#flow-sequence"}, {"include": "source.yaml.1.1#presentation-detail"}]}, "block-sequence": {"comment": "https://yaml.org/spec/1.2.2/#rule-l+block-sequence", "begin": "(?=((?<=[-?:]) )?+)(?<![^\t ][\t ]*+:|---)\\G( *+)(-)(?=[\\x{85 2028 2029}\r\n\t ])", "while": "\\G(?>(\\1\\2)(?!-[\\x{85 2028 2029}\r\n\t ])((?>\t[\t ]*+)?+[^\\x{85 2028 2029}\r\n\t #\\]}])?+|(?!\\1\\2)( *+)([\t ]*+[^\\x{85 2028 2029}\r\n#])?+)", "beginCaptures": {"2": {"name": "punctuation.whitespace.indentation.yaml"}, "3": {"name": "punctuation.definition.block.sequence.item.yaml"}}, "whileCaptures": {"1": {"name": "punctuation.whitespace.indentation.yaml"}, "2": {"name": "invalid.illegal.expected-indentation.yaml"}, "3": {"name": "punctuation.whitespace.indentation.yaml"}, "4": {"name": "invalid.illegal.expected-indentation.yaml"}}, "name": "meta.block.sequence.yaml", "patterns": [{"include": "#block-node"}]}, "block-map-key-explicit": {"comment": "https://yaml.org/spec/1.2.2/#rule-c-l-block-map-explicit-key", "begin": "(?=((?<=[-?:]) )?+)\\G( *+)(\\?)(?=[\\x{85 2028 2029}\r\n\t ])", "while": "\\G(?>(\\1\\2)(?![?:0-9A-Za-z$()+./;<=\\\\^_~\\x{A0}-\\x{D7FF}\\x{E000}-\\x{FFFD}\\x{010000}-\\x{10FFFF}&&[^\\x{2028 2029}]])((?>\t[\t ]*+)?+[^\\x{85 2028 2029}\r\n\t #\\-\\[\\]{}])?+|(?!\\1\\2)( *+)([\t ]*+[^\\x{85 2028 2029}\r\n#])?+)", "beginCaptures": {"2": {"name": "punctuation.whitespace.indentation.yaml"}, "3": {"name": "punctuation.definition.map.key.yaml"}, "4": {"name": "punctuation.whitespace.separator.yaml"}}, "whileCaptures": {"1": {"name": "punctuation.whitespace.indentation.yaml"}, "2": {"name": "invalid.illegal.expected-indentation.yaml"}, "3": {"name": "punctuation.whitespace.indentation.yaml"}, "4": {"name": "invalid.illegal.expected-indentation.yaml"}}, "name": "meta.map.explicit.yaml", "patterns": [{"include": "#key-double"}, {"include": "source.yaml#key-single"}, {"include": "source.yaml.1.1#flow-key-plain-out"}, {"include": "#block-map-value"}, {"include": "#block-node"}]}, "block-map-key-double": {"comment": "https://yaml.org/spec/1.2.2/#double-quoted-style (BLOCK-KEY)", "begin": "\\G\"", "end": "\"", "beginCaptures": {"0": {"name": "punctuation.definition.string.begin.yaml"}}, "endCaptures": {"0": {"name": "punctuation.definition.string.end.yaml"}}, "name": "meta.map.key.yaml string.quoted.double.yaml entity.name.tag.yaml", "patterns": [{"match": ".[\t ]*+$", "name": "invalid.illegal.multiline-key.yaml"}, {"match": "[^\t -\\x{10FFFF}]++", "name": "invalid.illegal.character.yaml"}, {"include": "#double-escape"}]}, "block-map-value": {"comment": "https://yaml.org/spec/1.2.2/#rule-c-l-block-map-implicit-value", "//": "Assumming 3rd party preprocessing variables `{{...}}` turn into valid map-keys when inside a block-mapping", "begin": ":(?=[\\x{85 2028 2029}\r\n\t ])|(?<=}})(?=[\t ]++#|[\t ]*+$)", "while": "\\G(?![?:!\"'0-9A-Za-z$()+./;<=\\\\^_~\\[{\\x{A0}-\\x{D7FF}\\x{E000}-\\x{FFFD}\\x{010000}-\\x{10FFFF}&&[^\\x{2028 2029}]]|-[^\\x{85 2028 2029}\r\n\t ])", "beginCaptures": {"0": {"name": "punctuation.separator.map.value.yaml"}}, "name": "meta.map.value.yaml", "patterns": [{"include": "#block-node"}]}, "block-scalar": {"comment": "https://yaml.org/spec/1.2.2/#81-block-scalar-styles", "patterns": [{"comment": "https://yaml.org/spec/1.2.2/#8111-block-indentation-indicator", "begin": "(?>(\\|)|(>))(?<chomp>[+-])?+([0-9])(?(<chomp>)|\\g<chomp>)?+", "while": "\\G(?> {\\4}| *+($|[^#]))", "beginCaptures": {"1": {"name": "keyword.control.flow.block-scalar.literal.yaml"}, "2": {"name": "keyword.control.flow.block-scalar.folded.yaml"}, "3": {"name": "storage.modifier.chomping-indicator.yaml"}, "4": {"name": "constant.numeric.indentation-indicator.yaml"}}, "whileCaptures": {"0": {"name": "punctuation.whitespace.indentation.yaml"}, "1": {"name": "invalid.illegal.expected-indentation.yaml"}}, "name": "meta.scalar.yaml", "patterns": [{"begin": "$", "while": "\\G", "contentName": "string.unquoted.block.yaml", "patterns": [{"include": "source.yaml#non-printable"}]}, {"begin": "\\G", "end": "$", "patterns": [{"include": "source.yaml.1.1#presentation-detail"}]}]}, {"//": "Soooooooo many edge cases", "begin": "(?>(\\|)|(>))([+-]?+)", "while": "\\G", "beginCaptures": {"1": {"name": "keyword.control.flow.block-scalar.literal.yaml"}, "2": {"name": "keyword.control.flow.block-scalar.folded.yaml"}, "3": {"name": "storage.modifier.chomping-indicator.yaml"}}, "name": "meta.scalar.yaml", "patterns": [{"comment": "https://yaml.org/spec/1.2.2/#rule-l-literal-content", "begin": "$", "while": "\\G", "patterns": [{"comment": "https://yaml.org/spec/1.2.2/#rule-l-nb-literal-text", "//": "Find the highest indented line", "begin": "\\G( ++)$", "while": "\\G(?>(\\1)$|(?!\\1)( *+)($|.))", "captures": {"1": {"name": "punctuation.whitespace.indentation.yaml"}, "2": {"name": "punctuation.whitespace.indentation.yaml"}, "3": {"name": "invalid.illegal.expected-indentation.yaml"}}, "contentName": "string.unquoted.block.yaml", "patterns": [{"include": "source.yaml#non-printable"}]}, {"comment": "https://yaml.org/spec/1.2.2/#rule-b-nb-literal-next", "//": ["Funky wrapper function", "The `end` pattern clears the parent `\\G` anchor", "Affectively forcing this rule to only match at most once", "https://github.com/microsoft/vscode-textmate/issues/114"], "begin": "\\G(?!$)(?=( *+))", "end": "\\G(?!\\1)(?=[\t ]*+#)", "patterns": [{"comment": "https://yaml.org/spec/1.2.2/#rule-l-nb-literal-text", "begin": "\\G( *+)", "while": "\\G(?>(\\1)|( *+)($|[^\t#]|[\t ]++[^#]))", "captures": {"1": {"name": "punctuation.whitespace.indentation.yaml"}, "2": {"name": "punctuation.whitespace.indentation.yaml"}, "3": {"name": "invalid.illegal.expected-indentation.yaml"}}, "contentName": "string.unquoted.block.yaml", "patterns": [{"include": "source.yaml#non-printable"}]}]}, {"comment": "https://yaml.org/spec/1.2.2/#rule-l-chomped-empty", "begin": "(?!\\G)(?=[\t ]*+#)", "while": "\\G", "patterns": [{"include": "source.yaml.1.1#presentation-detail"}]}]}, {"comment": "Header Comment", "begin": "\\G", "end": "$", "patterns": [{"include": "source.yaml.1.1#presentation-detail"}]}]}]}, "flow-node": {"comment": "https://yaml.org/spec/1.2.2/#rule-ns-flow-seq-entry (FLOW-IN)", "patterns": [{"begin": "(?=\\[|{)", "end": "(?=[:,\\]}])", "patterns": [{"begin": "(?!\\G)", "end": "(?=[:,\\]}])", "patterns": [{"include": "source.yaml.1.1#presentation-detail"}]}, {"include": "#flow-mapping"}, {"include": "#flow-sequence"}]}, {"include": "source.yaml.1.1#anchor-property"}, {"include": "#tag-property"}, {"include": "source.yaml.1.1#alias"}, {"begin": "(?=\"|')", "end": "(?=[:,\\]}])", "patterns": [{"begin": "(?!\\G)", "end": "(?=[:,\\]}])", "patterns": [{"include": "source.yaml.1.1#presentation-detail"}]}, {"include": "#double"}, {"include": "source.yaml.1.1#single"}]}, {"include": "source.yaml.1.1#flow-plain-in"}, {"include": "source.yaml.1.1#presentation-detail"}]}, "flow-mapping": {"comment": "https://yaml.org/spec/1.2.2/#742-flow-mappings", "begin": "{", "end": "}", "beginCaptures": {"0": {"name": "punctuation.definition.mapping.begin.yaml"}}, "endCaptures": {"0": {"name": "punctuation.definition.mapping.end.yaml"}}, "name": "meta.flow.mapping.yaml", "patterns": [{"comment": "https://yaml.org/spec/1.2.2/#rule-ns-s-flow-map-entries", "begin": "(?<={)\\G(?=[\\x{85 2028 2029}\r\n\t ,#])|,", "end": "(?=[^\\x{85 2028 2029}\r\n\t ,#])", "beginCaptures": {"0": {"name": "punctuation.separator.mapping.yaml"}}, "patterns": [{"match": ",++", "name": "invalid.illegal.separator.sequence.yaml"}, {"include": "source.yaml.1.1#presentation-detail"}]}, {"include": "#flow-mapping-map-key"}, {"include": "#flow-map-value-yaml"}, {"include": "#flow-map-value-json"}, {"include": "#flow-node"}]}, "flow-sequence": {"comment": "https://yaml.org/spec/1.2.2/#741-flow-sequences", "begin": "\\[", "end": "]", "beginCaptures": {"0": {"name": "punctuation.definition.sequence.begin.yaml"}}, "endCaptures": {"0": {"name": "punctuation.definition.sequence.end.yaml"}}, "name": "meta.flow.sequence.yaml", "patterns": [{"comment": "https://yaml.org/spec/1.2.2/#rule-ns-s-flow-seq-entries", "begin": "(?<=\\[)\\G(?=[\\x{85 2028 2029}\r\n\t ,#])|,", "end": "(?=[^\\x{85 2028 2029}\r\n\t ,#])", "beginCaptures": {"0": {"name": "punctuation.separator.sequence.yaml"}}, "patterns": [{"match": ",++", "name": "invalid.illegal.separator.sequence.yaml"}, {"include": "source.yaml.1.1#presentation-detail"}]}, {"include": "#flow-sequence-map-key"}, {"include": "#flow-map-value-yaml"}, {"include": "#flow-map-value-json"}, {"include": "#flow-node"}]}, "flow-mapping-map-key": {"comment": "https://yaml.org/spec/1.2.2/#rule-ns-flow-map-entry (FLOW-IN)", "patterns": [{"begin": "\\?(?=[\\x{85 2028 2029}\r\n\t ,\\[\\]{}])", "end": "(?=[,\\[\\]{}])", "beginCaptures": {"0": {"name": "punctuation.definition.map.key.yaml"}}, "name": "meta.flow.map.explicit.yaml", "patterns": [{"include": "#flow-mapping-map-key"}, {"include": "#flow-map-value-yaml"}, {"include": "#flow-map-value-json"}, {"include": "#flow-node"}]}, {"comment": "https://yaml.org/spec/1.2.2/#rule-ns-flow-map-implicit-entry (FLOW-IN)", "begin": "(?=(?>[^-?:,\\[\\]{}#&*!|>'\"%@` \\p{Cntrl}\\p{Surrogate}\\x{FFFE FFFF}]|[?:-](?![\\x{85 2028 2029}\r\n\t ,\\[\\]{}])))", "end": "(?=[,\\[\\]{}])", "name": "meta.flow.map.implicit.yaml", "patterns": [{"include": "source.yaml.1.1#flow-key-plain-in"}, {"match": ":(?=\\[|{)", "name": "invalid.illegal.separator.map.yaml"}, {"include": "#flow-map-value-yaml"}, {"include": "source.yaml.1.1#presentation-detail"}]}, {"comment": "https://yaml.org/spec/1.2.2/#rule-ns-flow-map-implicit-entry (FLOW-IN)", "begin": "(?=\"|')", "end": "(?=[,\\[\\]{}])", "name": "meta.flow.map.implicit.yaml", "patterns": [{"include": "#key-double"}, {"include": "source.yaml#key-single"}, {"include": "#flow-map-value-json"}, {"include": "source.yaml.1.1#presentation-detail"}]}]}, "flow-sequence-map-key": {"comment": "https://yaml.org/spec/1.2.2/#rule-ns-flow-map-entry (FLOW-IN)", "patterns": [{"begin": "\\?(?=[\\x{85 2028 2029}\r\n\t ,\\[\\]{}])", "end": "(?=[,\\[\\]{}])", "beginCaptures": {"0": {"name": "punctuation.definition.map.key.yaml"}}, "name": "meta.flow.map.explicit.yaml", "patterns": [{"include": "#flow-mapping-map-key"}, {"include": "#flow-map-value-yaml"}, {"include": "#flow-map-value-json"}, {"include": "#flow-node"}]}, {"comment": "https://yaml.org/spec/1.2.2/#rule-ns-flow-map-implicit-entry (FLOW-IN)", "begin": "(?<=[\t ,\\[{]|^)(?=(?>[^-?:,\\[\\]{}#&*!|>'\"%@` \\p{Cntrl}\\p{Surrogate}\\x{FFFE FFFF}]|[?:-](?![\\x{85 2028 2029}\r\n\t ,\\[\\]{}]))(?>[^:#,\\[\\]{}]++|:(?![\\x{85 2028 2029}\r\n\t ,\\[\\]{}])|(?<! |\t)#++)*+:[\\x{85 2028 2029}\r\n\t ,\\[\\]{}])", "end": "(?=[,\\[\\]{}])", "name": "meta.flow.map.implicit.yaml", "patterns": [{"include": "source.yaml.1.1#flow-key-plain-in"}, {"match": ":(?=\\[|{)", "name": "invalid.illegal.separator.map.yaml"}, {"include": "#flow-map-value-yaml"}, {"include": "source.yaml.1.1#presentation-detail"}]}, {"comment": "https://yaml.org/spec/1.2.2/#rule-ns-flow-map-implicit-entry (FLOW-IN)", "begin": "(?=(?>\"(?>[^\\\\\"]++|\\\\.)*+\"|'(?>[^']++|'')*+')[\t ]*+:)", "end": "(?=[,\\[\\]{}])", "name": "meta.flow.map.implicit.yaml", "patterns": [{"include": "#key-double"}, {"include": "source.yaml#key-single"}, {"include": "#flow-map-value-json"}, {"include": "source.yaml.1.1#presentation-detail"}]}]}, "flow-map-value-yaml": {"comment": "https://yaml.org/spec/1.2.2/#rule-c-ns-flow-map-separate-value (FLOW-IN)", "begin": ":(?=[\\x{85 2028 2029}\r\n\t ,\\[\\]{}])", "end": "(?=[,\\]}])", "beginCaptures": {"0": {"name": "punctuation.separator.map.value.yaml"}}, "name": "meta.flow.pair.value.yaml", "patterns": [{"include": "#flow-node"}]}, "flow-map-value-json": {"comment": "https://yaml.org/spec/1.2.2/#rule-c-ns-flow-map-separate-value (FLOW-IN)", "begin": "(?<=(?>[\"'\\]}]|^)[\t ]*+):", "end": "(?=[,\\]}])", "beginCaptures": {"0": {"name": "punctuation.separator.map.value.yaml"}}, "name": "meta.flow.pair.value.yaml", "patterns": [{"include": "#flow-node"}]}, "key-double": {"comment": "https://yaml.org/spec/1.2.2/#double-quoted-style (FLOW-OUT)", "begin": "\"", "end": "\"", "beginCaptures": {"0": {"name": "punctuation.definition.string.begin.yaml"}}, "endCaptures": {"0": {"name": "punctuation.definition.string.end.yaml"}}, "name": "meta.map.key.yaml string.quoted.double.yaml entity.name.tag.yaml", "patterns": [{"match": "[^\t -\\x{10FFFF}]++", "name": "invalid.illegal.character.yaml"}, {"include": "#double-escape"}]}, "double": {"comment": "https://yaml.org/spec/1.2.2/#double-quoted-style", "begin": "\"", "end": "\"", "beginCaptures": {"0": {"name": "punctuation.definition.string.begin.yaml"}}, "endCaptures": {"0": {"name": "punctuation.definition.string.end.yaml"}}, "name": "string.quoted.double.yaml", "patterns": [{"match": "(?<!\")\\G[\t ]++", "name": "punctuation.whitespace.separator.yaml"}, {"match": "[\t ]++$", "name": "punctuation.whitespace.separator.yaml"}, {"match": "[^\\x{85 2028 2029}\r\n\t -\\x{10FFFF}]++", "name": "invalid.illegal.character.yaml"}, {"include": "#double-escape"}]}, "double-escape": {"comment": "https://yaml.org/spec/1.2.2/#rule-c-ns-esc-char", "patterns": [{"match": "\\\\[\\x{85 2028 2029}\r\n0abtnvfre \"\\\\N_LP^]", "name": "constant.character.escape.yaml"}, {"match": "\\\\x[0-9a-fA-F]{2}", "name": "constant.character.escape.unicode.8-bit.yaml"}, {"match": "\\\\u[0-9a-fA-F]{4}", "name": "constant.character.escape.unicode.16-bit.yaml"}, {"match": "\\\\U[0-9a-fA-F]{8}", "name": "constant.character.escape.unicode.32-bit.yaml"}, {"match": "\\\\(?>x[^\"]{2,0}|u[^\"]{4,0}|U[^\"]{8,0}|.)", "name": "invalid.illegal.constant.character.escape.yaml"}]}, "tag-property": {"comment": "https://yaml.org/spec/1.0/#c-ns-tag-property", "//": ["!^", "!!private_ns-tag-char+", "!global_core_ns-tag-char+_no-:/!", "!global_vocabulary_az09-_/ns-tag-char", "!global_domain_ns-tag-char+.ns-tag-char+,1234(-12(-12)?)?/ns-tag-char*"], "begin": "(?=!)", "end": "(?=[\\x{2028 2029}\r\n\t ])", "name": "storage.type.tag.yaml", "patterns": [{"match": "\\G!(?=[\\x{85 2028 2029}\r\n\t ])", "name": "punctuation.definition.tag.non-specific.yaml"}, {"comment": "https://yaml.org/spec/1.0/#c-ns-private-tag", "match": "\\G!!", "name": "punctuation.definition.tag.private.yaml"}, {"comment": "https://yaml.org/spec/1.0/#ns-ns-global-tag", "match": "\\G!", "name": "punctuation.definition.tag.global.yaml"}, {"comment": "https://yaml.org/spec/1.0/#c-prefix", "match": "\\^", "name": "punctuation.definition.tag.prefix.yaml"}, {"match": "%[0-9a-fA-F]{2}", "name": "constant.character.escape.unicode.8-bit.yaml"}, {"match": "%[^\\x{85 2028 2029}\r\n\t ]{2,0}", "name": "invalid.illegal.constant.character.escape.unicode.8-bit.yaml"}, {"include": "#double-escape"}, {"include": "source.yaml#non-printable"}]}}}