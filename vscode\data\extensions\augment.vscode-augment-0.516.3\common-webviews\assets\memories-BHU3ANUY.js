import{S as j,i as X,s as Y,a as K,n as G,d as R,b as ot,g as Rt,u as Ct,v as Mt,w as bt,x as Lt,f as St,H as Ft,j as rt,t as p,q as d,o as O,p as k,c as b,N as dt,al as _,A as U,ah as ft,a6 as Gt,a3 as E,a4 as A,D as h,a5 as B,E as v,G as w,V,a7 as Et,e as T,F as N,h as F,Y as H,X as pt,W as mt,R as Nt,af as zt}from"./SpinnerAugment-VfHtkDdv.js";import"./design-system-init-BQpWKoxZ.js";import{h as q,W as Q,e as at}from"./IconButtonAugment-BlRCK7lJ.js";import{O as Tt}from"./OpenFileButton-fgZNybO2.js";import{S as At}from"./TextAreaAugment-BnS2cUNC.js";import{C as Bt}from"./check-ChePEq3H.js";import{C as gt,E as ht,D,M as J,f as It,g as Ot,h as kt}from"./index-C5qylk65.js";import{M as Z}from"./message-broker-DxXjuHCW.js";import{M as qt,R as Dt}from"./rules-model-BLO-SAZS.js";import{B as vt}from"./ButtonAugment-CRJIYorH.js";import{C as wt}from"./chevron-down-DQi0HUpw.js";import{F as Ht}from"./Filespan-UFj4_Gis.js";import{a as xt,T as yt}from"./CardAugment-CMpdst0l.js";import"./chat-context-DhGlDJgc.js";import"./index-B528snJk.js";import"./index-6WVCg-U8.js";import"./remote-agents-client-zf3VV9pT.js";import"./types-CGlLNakm.js";import"./ra-diff-ops-model-DMR40nRt.js";import"./BaseTextInput-C9A3t790.js";import"./async-messaging-Cm1y2LK7.js";import"./file-paths-CXmnYUii.js";import"./lodash-Cs_Exhqr.js";function Wt(r){let t,n,e=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 448 512"},r[0]],s={};for(let o=0;o<e.length;o+=1)s=K(s,e[o]);return{c(){t=St("svg"),n=new Ft(!0),this.h()},l(o){t=Mt(o,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var i=bt(t);n=Lt(i,!0),i.forEach(R),this.h()},h(){n.a=null,ot(t,s)},m(o,i){Ct(o,t,i),n.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M440.6 273.4c4.7-4.5 7.4-10.8 7.4-17.4s-2.7-12.8-7.4-17.4l-176-168c-9.6-9.2-24.8-8.8-33.9.8s-8.8 24.8.8 33.9L364.1 232H24c-13.3 0-24 10.7-24 24s10.7 24 24 24h340.1L231.4 406.6c-9.6 9.2-9.9 24.3-.8 33.9s24.3 9.9 33.9.8l176-168z"/>',t)},p(o,[i]){ot(t,s=Rt(e,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 448 512"},1&i&&o[0]]))},i:G,o:G,d(o){o&&R(t)}}}function _t(r,t,n){return r.$$set=e=>{n(0,t=K(K({},t),rt(e)))},[t=rt(t)]}class tt extends j{constructor(t){super(),X(this,t,_t,Wt,Y,{})}}function it(r,t,n){const e=r.slice();return e[27]=t[n],e[29]=n,e}function ct(r){let t,n,e,s;const o=[Vt,Ut],i=[];function l(a,$){return a[0]?0:1}return t=l(r),n=i[t]=o[t](r),{c(){n.c(),e=dt()},m(a,$){i[t].m(a,$),b(a,e,$),s=!0},p(a,$){let c=t;t=l(a),t===c?i[t].p(a,$):(O(),p(i[c],1,1,()=>{i[c]=null}),k(),n=i[t],n?n.p(a,$):(n=i[t]=o[t](a),n.c()),d(n,1),n.m(e.parentNode,e))},i(a){s||(d(n),s=!0)},o(a){p(n),s=!1},d(a){a&&R(e),i[t].d(a)}}}function Ut(r){let t,n,e={content:r[6],triggerOn:[xt.Hover],side:"top",$$slots:{default:[Yt]},$$scope:{ctx:r}};return t=new yt({props:e}),r[22](t),{c(){w(t.$$.fragment)},m(s,o){v(t,s,o),n=!0},p(s,o){const i={};64&o&&(i.content=s[6]),1073741824&o&&(i.$$scope={dirty:o,ctx:s}),t.$set(i)},i(s){n||(d(t.$$.fragment,s),n=!0)},o(s){p(t.$$.fragment,s),n=!1},d(s){r[22](null),h(t,s)}}}function Vt(r){let t,n,e,s;function o(a){r[20](a)}function i(a){r[21](a)}let l={onOpenChange:r[15],$$slots:{default:[re]},$$scope:{ctx:r}};return r[3]!==void 0&&(l.requestClose=r[3]),r[2]!==void 0&&(l.focusedIndex=r[2]),t=new D.Root({props:l}),E.push(()=>A(t,"requestClose",o)),E.push(()=>A(t,"focusedIndex",i)),{c(){w(t.$$.fragment)},m(a,$){v(t,a,$),s=!0},p(a,$){const c={};1073743602&$&&(c.$$scope={dirty:$,ctx:a}),!n&&8&$&&(n=!0,c.requestClose=a[3],B(()=>n=!1)),!e&&4&$&&(e=!0,c.focusedIndex=a[2],B(()=>e=!1)),t.$set(c)},i(a){s||(d(t.$$.fragment,a),s=!0)},o(a){p(t.$$.fragment,a),s=!1},d(a){h(t,a)}}}function Pt(r){let t;return{c(){t=H("Rules")},m(n,e){b(n,t,e)},d(n){n&&R(t)}}}function jt(r){let t,n;return t=new tt({props:{slot:"iconLeft"}}),{c(){w(t.$$.fragment)},m(e,s){v(t,e,s),n=!0},p:G,i(e){n||(d(t.$$.fragment,e),n=!0)},o(e){p(t.$$.fragment,e),n=!1},d(e){h(t,e)}}}function Xt(r){let t,n;return t=new wt({props:{slot:"iconRight"}}),{c(){w(t.$$.fragment)},m(e,s){v(t,e,s),n=!0},p:G,i(e){n||(d(t.$$.fragment,e),n=!0)},o(e){p(t.$$.fragment,e),n=!1},d(e){h(t,e)}}}function Yt(r){let t,n;return t=new vt({props:{color:"neutral",variant:"soft",size:1,disabled:!0,$$slots:{iconRight:[Xt],iconLeft:[jt],default:[Pt]},$$scope:{ctx:r}}}),{c(){w(t.$$.fragment)},m(e,s){v(t,e,s),n=!0},p(e,s){const o={};1073741824&s&&(o.$$scope={dirty:s,ctx:e}),t.$set(o)},i(e){n||(d(t.$$.fragment,e),n=!0)},o(e){p(t.$$.fragment,e),n=!1},d(e){h(t,e)}}}function Jt(r){let t,n=(r[9]?r[9].path:"Rules")+"";return{c(){t=H(n)},m(e,s){b(e,t,s)},p(e,s){512&s&&n!==(n=(e[9]?e[9].path:"Rules")+"")&&pt(t,n)},d(e){e&&R(t)}}}function Kt(r){let t,n;return t=new tt({props:{slot:"iconLeft"}}),{c(){w(t.$$.fragment)},m(e,s){v(t,e,s),n=!0},p:G,i(e){n||(d(t.$$.fragment,e),n=!0)},o(e){p(t.$$.fragment,e),n=!1},d(e){h(t,e)}}}function Qt(r){let t,n;return t=new wt({props:{slot:"iconRight"}}),{c(){w(t.$$.fragment)},m(e,s){v(t,e,s),n=!0},p:G,i(e){n||(d(t.$$.fragment,e),n=!0)},o(e){p(t.$$.fragment,e),n=!1},d(e){h(t,e)}}}function Zt(r){let t,n;return t=new vt({props:{color:"neutral",variant:"soft",size:1,disabled:r[7],$$slots:{iconRight:[Qt],iconLeft:[Kt],default:[Jt]},$$scope:{ctx:r}}}),{c(){w(t.$$.fragment)},m(e,s){v(t,e,s),n=!0},p(e,s){const o={};128&s&&(o.disabled=e[7]),1073742336&s&&(o.$$scope={dirty:s,ctx:e}),t.$set(o)},i(e){n||(d(t.$$.fragment,e),n=!0)},o(e){p(t.$$.fragment,e),n=!1},d(e){h(t,e)}}}function te(r){let t,n,e={content:r[6],triggerOn:[xt.Hover],side:"top",open:!r[5]&&void 0,$$slots:{default:[Zt]},$$scope:{ctx:r}};return t=new yt({props:e}),r[18](t),{c(){w(t.$$.fragment)},m(s,o){v(t,s,o),n=!0},p(s,o){const i={};64&o&&(i.content=s[6]),32&o&&(i.open=!s[5]&&void 0),1073742464&o&&(i.$$scope={dirty:o,ctx:s}),t.$set(i)},i(s){n||(d(t.$$.fragment,s),n=!0)},o(s){p(t.$$.fragment,s),n=!1},d(s){r[18](null),h(t,s)}}}function ee(r){let t,n;return t=new Ht({props:{filepath:r[27].path}}),{c(){w(t.$$.fragment)},m(e,s){v(t,e,s),n=!0},p(e,s){const o={};2&s&&(o.filepath=e[27].path),t.$set(o)},i(e){n||(d(t.$$.fragment,e),n=!0)},o(e){p(t.$$.fragment,e),n=!1},d(e){h(t,e)}}}function lt(r){let t,n;function e(){return r[19](r[27])}return t=new D.Item({props:{onSelect:e,highlight:r[10]===r[29],$$slots:{default:[ee]},$$scope:{ctx:r}}}),{c(){w(t.$$.fragment)},m(s,o){v(t,s,o),n=!0},p(s,o){r=s;const i={};2&o&&(i.onSelect=e),1024&o&&(i.highlight=r[10]===r[29]),1073741826&o&&(i.$$scope={dirty:o,ctx:r}),t.$set(i)},i(s){n||(d(t.$$.fragment,s),n=!0)},o(s){p(t.$$.fragment,s),n=!1},d(s){h(t,s)}}}function $t(r){let t,n,e,s;return t=new D.Separator({}),e=new D.Label({props:{$$slots:{default:[se]},$$scope:{ctx:r}}}),{c(){w(t.$$.fragment),n=V(),w(e.$$.fragment)},m(o,i){v(t,o,i),b(o,n,i),v(e,o,i),s=!0},p(o,i){const l={};1073742850&i&&(l.$$scope={dirty:i,ctx:o}),e.$set(l)},i(o){s||(d(t.$$.fragment,o),d(e.$$.fragment,o),s=!0)},o(o){p(t.$$.fragment,o),p(e.$$.fragment,o),s=!1},d(o){o&&R(n),h(t,o),h(e,o)}}}function ne(r){let t,n=ut(r[1][r[10]])+"";return{c(){t=H(n)},m(e,s){b(e,t,s)},p(e,s){1026&s&&n!==(n=ut(e[1][e[10]])+"")&&pt(t,n)},d(e){e&&R(t)}}}function se(r){let t,n;return t=new mt({props:{size:1,color:"neutral",$$slots:{default:[ne]},$$scope:{ctx:r}}}),{c(){w(t.$$.fragment)},m(e,s){v(t,e,s),n=!0},p(e,s){const o={};1073742850&s&&(o.$$scope={dirty:s,ctx:e}),t.$set(o)},i(e){n||(d(t.$$.fragment,e),n=!0)},o(e){p(t.$$.fragment,e),n=!1},d(e){h(t,e)}}}function oe(r){let t,n,e,s=at(r[1]),o=[];for(let a=0;a<s.length;a+=1)o[a]=lt(it(r,s,a));const i=a=>p(o[a],1,1,()=>{o[a]=null});let l=r[10]!==void 0&&r[1][r[10]]&&$t(r);return{c(){t=N("div");for(let a=0;a<o.length;a+=1)o[a].c();n=V(),l&&l.c(),F(t,"class","rules-dropdown-content svelte-18wohv")},m(a,$){b(a,t,$);for(let c=0;c<o.length;c+=1)o[c]&&o[c].m(t,null);T(t,n),l&&l.m(t,null),e=!0},p(a,$){if(17410&$){let c;for(s=at(a[1]),c=0;c<s.length;c+=1){const m=it(a,s,c);o[c]?(o[c].p(m,$),d(o[c],1)):(o[c]=lt(m),o[c].c(),d(o[c],1),o[c].m(t,n))}for(O(),c=s.length;c<o.length;c+=1)i(c);k()}a[10]!==void 0&&a[1][a[10]]?l?(l.p(a,$),1026&$&&d(l,1)):(l=$t(a),l.c(),d(l,1),l.m(t,null)):l&&(O(),p(l,1,1,()=>{l=null}),k())},i(a){if(!e){for(let $=0;$<s.length;$+=1)d(o[$]);d(l),e=!0}},o(a){o=o.filter(Boolean);for(let $=0;$<o.length;$+=1)p(o[$]);p(l),e=!1},d(a){a&&R(t),Et(o,a),l&&l.d()}}}function re(r){let t,n,e,s;return t=new D.Trigger({props:{$$slots:{default:[te]},$$scope:{ctx:r}}}),e=new D.Content({props:{side:"bottom",align:"start",$$slots:{default:[oe]},$$scope:{ctx:r}}}),{c(){w(t.$$.fragment),n=V(),w(e.$$.fragment)},m(o,i){v(t,o,i),b(o,n,i),v(e,o,i),s=!0},p(o,i){const l={};1073742576&i&&(l.$$scope={dirty:i,ctx:o}),t.$set(l);const a={};1073742850&i&&(a.$$scope={dirty:i,ctx:o}),e.$set(a)},i(o){s||(d(t.$$.fragment,o),d(e.$$.fragment,o),s=!0)},o(o){p(t.$$.fragment,o),p(e.$$.fragment,o),s=!1},d(o){o&&R(n),h(t,o),h(e,o)}}}function ae(r){let t,n,e=!r[8]&&ct(r);return{c(){e&&e.c(),t=dt()},m(s,o){e&&e.m(s,o),b(s,t,o),n=!0},p(s,[o]){s[8]?e&&(O(),p(e,1,1,()=>{e=null}),k()):e?(e.p(s,o),256&o&&d(e,1)):(e=ct(s),e.c(),d(e,1),e.m(t.parentNode,t))},i(s){n||(d(e),n=!0)},o(s){p(e),n=!1},d(s){s&&R(t),e&&e.d(s)}}}function ut(r){return`Move to ${r.path}`}function ie(r,t,n){let e,s,o,i,l,a,$,c=G,m=()=>(c(),c=Gt(z,g=>n(10,$=g)),z);r.$$.on_destroy.push(()=>c());let{onRuleSelected:f}=t,{disabled:y=!1}=t;const C=new Z(q),u=new gt,x=new ht(q,C,u),M=U([]);_(r,M,g=>n(1,i=g));const L=U(!0);_(r,L,g=>n(8,l=g));const S=U(void 0);let z;_(r,S,g=>n(9,a=g)),m();let W,P=()=>{};function et(g){S.set(g),f(g),P()}ft(()=>{(async function(){try{L.set(!0);const I=await x.findRules("",100);M.set(I)}catch(I){console.error("Failed to load rules:",I),M.set([])}finally{L.set(!1)}})();const g=I=>{var st;((st=I.data)==null?void 0:st.type)===Q.getRulesListResponse&&(M.set(I.data.data||[]),L.set(!1))};return window.addEventListener("message",g),()=>{window.removeEventListener("message",g)}});let nt=!1;return r.$$set=g=>{"onRuleSelected"in g&&n(16,f=g.onRuleSelected),"disabled"in g&&n(17,y=g.disabled)},r.$$.update=()=>{2&r.$$.dirty&&n(0,e=i.length>0),131073&r.$$.dirty&&n(7,s=y||!e),1&r.$$.dirty&&n(6,o=e?"Move highlighted text to a .augment/rules file":"Please add at least 1 file to .augment/rules and reload VSCode")},[e,i,z,P,W,nt,o,s,l,a,$,M,L,S,et,function(g){n(5,nt=g)},f,y,function(g){E[g?"unshift":"push"](()=>{W=g,n(4,W)})},g=>et(g),function(g){P=g,n(3,P)},function(g){z=g,m(n(2,z))},function(g){E[g?"unshift":"push"](()=>{W=g,n(4,W)})}]}class ce extends j{constructor(t){super(),X(this,t,ie,ae,Y,{onRuleSelected:16,disabled:17})}}function le(r){let t;return{c(){t=H("User Guidelines")},m(n,e){b(n,t,e)},d(n){n&&R(t)}}}function $e(r){let t,n;return t=new tt({}),{c(){w(t.$$.fragment)},m(e,s){v(t,e,s),n=!0},i(e){n||(d(t.$$.fragment,e),n=!0)},o(e){p(t.$$.fragment,e),n=!1},d(e){h(t,e)}}}function ue(r){let t,n;return t=new Bt({}),{c(){w(t.$$.fragment)},m(e,s){v(t,e,s),n=!0},i(e){n||(d(t.$$.fragment,e),n=!0)},o(e){p(t.$$.fragment,e),n=!1},d(e){h(t,e)}}}function de(r){let t,n,e,s;const o=[ue,$e],i=[];function l(a,$){return a[5]==="success"?0:1}return n=l(r),e=i[n]=o[n](r),{c(){t=N("div"),e.c(),F(t,"slot","iconLeft"),F(t,"class","c-move-text-btn__left_icon svelte-1yddhs6")},m(a,$){b(a,t,$),i[n].m(t,null),s=!0},p(a,$){let c=n;n=l(a),n!==c&&(O(),p(i[c],1,1,()=>{i[c]=null}),k(),e=i[n],e||(e=i[n]=o[n](a),e.c()),d(e,1),e.m(t,null))},i(a){s||(d(e),s=!0)},o(a){p(e),s=!1},d(a){a&&R(t),i[n].d()}}}function fe(r){let t;return{c(){t=H("Augment-Memories.md")},m(n,e){b(n,t,e)},d(n){n&&R(t)}}}function pe(r){let t,n;return t=new mt({props:{slot:"text",size:1,$$slots:{default:[fe]},$$scope:{ctx:r}}}),{c(){w(t.$$.fragment)},m(e,s){v(t,e,s),n=!0},p(e,s){const o={};1048576&s&&(o.$$scope={dirty:s,ctx:e}),t.$set(o)},i(e){n||(d(t.$$.fragment,e),n=!0)},o(e){p(t.$$.fragment,e),n=!1},d(e){h(t,e)}}}function me(r){let t,n,e,s,o,i,l,a,$,c,m,f;function y(u){r[11](u)}let C={tooltip:{neutral:"Move highlighted text to user guidelines",success:"Text moved to user guidelines"},stateVariant:{success:"solid",neutral:"soft"},defaultColor:"neutral",onClick:r[10],disabled:!r[2],stickyColor:!1,persistOnTooltipClose:!0,replaceIconOnSuccess:!0,size:1,$$slots:{iconLeft:[de],default:[le]},$$scope:{ctx:r}};return r[5]!==void 0&&(C.state=r[5]),s=new At({props:C}),E.push(()=>A(s,"state",y)),a=new ce({props:{onRuleSelected:r[9],disabled:!r[2]}}),m=new Tt({props:{size:1,path:r[1],variant:"soft",onOpenLocalFile:r[12],$$slots:{text:[pe]},$$scope:{ctx:r}}}),{c(){t=N("div"),n=N("div"),e=N("div"),w(s.$$.fragment),i=V(),l=N("div"),w(a.$$.fragment),$=V(),c=N("div"),w(m.$$.fragment),F(e,"class","c-move-text-btn svelte-1yddhs6"),F(l,"class","c-move-text-btn svelte-1yddhs6"),F(n,"class","l-file-controls-left svelte-1yddhs6"),F(c,"class","l-file-controls-right svelte-1yddhs6"),F(t,"class","l-file-controls svelte-1yddhs6"),F(t,"slot","header")},m(u,x){b(u,t,x),T(t,n),T(n,e),v(s,e,null),T(n,i),T(n,l),v(a,l,null),T(t,$),T(t,c),v(m,c,null),f=!0},p(u,x){const M={};4&x&&(M.disabled=!u[2]),1048608&x&&(M.$$scope={dirty:x,ctx:u}),!o&&32&x&&(o=!0,M.state=u[5],B(()=>o=!1)),s.$set(M);const L={};4&x&&(L.disabled=!u[2]),a.$set(L);const S={};2&x&&(S.path=u[1]),2&x&&(S.onOpenLocalFile=u[12]),1048576&x&&(S.$$scope={dirty:x,ctx:u}),m.$set(S)},i(u){f||(d(s.$$.fragment,u),d(a.$$.fragment,u),d(m.$$.fragment,u),f=!0)},o(u){p(s.$$.fragment,u),p(a.$$.fragment,u),p(m.$$.fragment,u),f=!1},d(u){u&&R(t),h(s),h(a),h(m)}}}function ge(r){let t,n,e,s,o,i;function l(f){r[13](f)}function a(f){r[14](f)}function $(f){r[15](f)}function c(f){r[16](f)}let m={saveFunction:r[7],variant:"surface",size:2,resize:"vertical",class:"markdown-editor",$$slots:{header:[me]},$$scope:{ctx:r}};return r[2]!==void 0&&(m.selectedText=r[2]),r[3]!==void 0&&(m.selectionStart=r[3]),r[4]!==void 0&&(m.selectionEnd=r[4]),r[0]!==void 0&&(m.value=r[0]),t=new qt({props:m}),E.push(()=>A(t,"selectedText",l)),E.push(()=>A(t,"selectionStart",a)),E.push(()=>A(t,"selectionEnd",$)),E.push(()=>A(t,"value",c)),{c(){w(t.$$.fragment)},m(f,y){v(t,f,y),i=!0},p(f,[y]){const C={};1048614&y&&(C.$$scope={dirty:y,ctx:f}),!n&&4&y&&(n=!0,C.selectedText=f[2],B(()=>n=!1)),!e&&8&y&&(e=!0,C.selectionStart=f[3],B(()=>e=!1)),!s&&16&y&&(s=!0,C.selectionEnd=f[4],B(()=>s=!1)),!o&&1&y&&(o=!0,C.value=f[0],B(()=>o=!1)),t.$set(C)},i(f){i||(d(t.$$.fragment,f),i=!0)},o(f){p(t.$$.fragment,f),i=!1},d(f){h(t,f)}}}function he(r,t,n){let{text:e}=t,{path:s}=t;const o=new Z(q),i=new gt,l=new ht(q,o,i),a=new Dt(o);let $="",c=0,m=0,f="neutral";const y=async()=>{s&&l.saveFile({repoRoot:"",pathName:s,content:e})};async function C(u){if(!$)return;let x,M,L;const S=$.slice(0,20);if(u==="userGuidelines"?(x="Move Content to User Guidelines",M=`Are you sure you want to move the selected content "${S}" to your user guidelines?`,L=J.userGuidelines):u==="augmentGuidelines"?(x="Move Content to Workspace Guidelines",M=`Are you sure you want to move the selected content "${S}" to workspace guidelines?`,L=J.augmentGuidelines):(x="Move Content to Rule",M=`Are you sure you want to move the selected content "${S}" to rule file "${u.rule.path}"?`,L=J.rules),!await l.openConfirmationModal({title:x,message:M,confirmButtonText:"Move",cancelButtonText:"Cancel"}))return;u==="userGuidelines"?l.updateUserGuidelines($):u==="augmentGuidelines"?l.updateWorkspaceGuidelines($):(await a.updateRuleContent({type:u.rule.type,path:u.rule.path,content:u.rule.content+`

`+$,description:u.rule.description}),l.showNotification({message:`Moved content "${S}" to rule file "${u.rule.path}"`,type:"info",openFileMessage:{repoRoot:"",pathName:`${It}/${Ot}/${u.rule.path}`}}));const z=e.substring(0,c)+e.substring(m);return n(0,e=z),await y(),l.reportAgentSessionEvent({eventName:kt.memoriesMove,conversationId:"",eventData:{memoriesMoveData:{target:L}}}),"success"}return r.$$set=u=>{"text"in u&&n(0,e=u.text),"path"in u&&n(1,s=u.path)},[e,s,$,c,m,f,l,y,C,async function(u){await C({rule:u})},()=>C("userGuidelines"),function(u){f=u,n(5,f)},async()=>(l.openFile({repoRoot:"",pathName:s}),"success"),function(u){$=u,n(2,$)},function(u){c=u,n(3,c)},function(u){m=u,n(4,m)},function(u){e=u,n(0,e)}]}class ve extends j{constructor(t){super(),X(this,t,he,ge,Y,{text:0,path:1})}}function we(r){let t;return{c(){t=H("Loading memories...")},m(n,e){b(n,t,e)},p:G,i:G,o:G,d(n){n&&R(t)}}}function xe(r){let t,n;return t=new ve({props:{text:r[0],path:r[1]}}),{c(){w(t.$$.fragment)},m(e,s){v(t,e,s),n=!0},p(e,s){const o={};1&s&&(o.text=e[0]),2&s&&(o.path=e[1]),t.$set(o)},i(e){n||(d(t.$$.fragment,e),n=!0)},o(e){p(t.$$.fragment,e),n=!1},d(e){h(t,e)}}}function ye(r){let t,n,e,s,o,i;const l=[xe,we],a=[];function $(c,m){return c[0]!==null&&c[1]!==null?0:1}return n=$(r),e=a[n]=l[n](r),{c(){t=N("div"),e.c(),F(t,"class","c-memories-container svelte-1vchs21")},m(c,m){b(c,t,m),a[n].m(t,null),s=!0,o||(i=Nt(window,"message",r[2].onMessageFromExtension),o=!0)},p(c,[m]){let f=n;n=$(c),n===f?a[n].p(c,m):(O(),p(a[f],1,1,()=>{a[f]=null}),k(),e=a[n],e?e.p(c,m):(e=a[n]=l[n](c),e.c()),d(e,1),e.m(t,null))},i(c){s||(d(e),s=!0)},o(c){p(e),s=!1},d(c){c&&R(t),a[n].d(),o=!1,i()}}}function Re(r,t,n){let e,s;const o=new Z(q),i=U(null);_(r,i,$=>n(0,e=$));const l=U(null);_(r,l,$=>n(1,s=$));const a={handleMessageFromExtension($){const c=$.data;if(c&&c.type===Q.loadFile){if(c.data.content!==void 0){const m=c.data.content.replace(/^\n+/,"");i.set(m)}c.data.pathName&&l.set(c.data.pathName)}return!0}};return ft(()=>{o.registerConsumer(a),q.postMessage({type:Q.memoriesLoaded})}),zt(()=>{o.dispose()}),[e,s,o,i,l]}new class extends j{constructor(r){super(),X(this,r,Re,ye,Y,{})}}({target:document.getElementById("app")});
