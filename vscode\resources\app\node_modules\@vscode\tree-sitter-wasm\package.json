{"name": "@vscode/tree-sitter-wasm", "version": "0.1.4", "description": "Pre-built WASM files for Tree-Sitter and Tree-Sitter languages that VS Code uses", "scripts": {"build-wasm": "ts-node ./build/main.ts", "install-emsdk": "sudo rm -rf /opt/dev/emsdk && sudo ./build/setup-emsdk.sh"}, "author": "Visual Studio Code Team", "license": "MIT", "files": ["wasm", "LICENSE", "README.md", "SECURITY.md", "cgmanifest.json"], "main": "wasm/tree-sitter.js", "types": "wasm/web-tree-sitter.d.ts", "devDependencies": {"@types/node": "^20.14.6", "tree-sitter-css": "^0.23.0", "tree-sitter-c-sharp": "^0.23.0", "tree-sitter-cli": "^0.23.0", "tree-sitter-cpp": "^0.23.1", "tree-sitter-go": "^0.23.1", "tree-sitter-java": "^0.23.2", "tree-sitter-javascript": "^0.23.0", "tree-sitter-python": "^0.23.2", "tree-sitter-regex": "^0.23.0", "tree-sitter-ruby": "^0.23.0", "tree-sitter-rust": "^0.23.0", "tree-sitter-typescript": "^0.23.0", "ts-node": "^10.9.2"}, "repository": {"type": "git", "url": "https://github.com/Microsoft/vscode-tree-sitter-wasm.git"}, "bugs": {"url": "https://github.com/Microsoft/vscode-tree-sitter-wasm/issues"}}