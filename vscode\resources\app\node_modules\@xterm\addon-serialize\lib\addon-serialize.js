!function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e():"function"==typeof define&&define.amd?define([],e):"object"==typeof exports?exports.SerializeAddon=e():t.SerializeAddon=e()}(globalThis,(()=>(()=>{"use strict";var t={992:(t,e,s)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.DEFAULT_ANSI_COLORS=void 0;const r=s(993);e.DEFAULT_ANSI_COLORS=Object.freeze((()=>{const t=[r.css.toColor("#2e3436"),r.css.toColor("#cc0000"),r.css.toColor("#4e9a06"),r.css.toColor("#c4a000"),r.css.toColor("#3465a4"),r.css.toColor("#75507b"),r.css.toColor("#06989a"),r.css.toColor("#d3d7cf"),r.css.toColor("#555753"),r.css.toColor("#ef2929"),r.css.toColor("#8ae234"),r.css.toColor("#fce94f"),r.css.toColor("#729fcf"),r.css.toColor("#ad7fa8"),r.css.toColor("#34e2e2"),r.css.toColor("#eeeeec")],e=[0,95,135,175,215,255];for(let s=0;s<216;s++){const i=e[s/36%6|0],o=e[s/6%6|0],n=e[s%6];t.push({css:r.channels.toCss(i,o,n),rgba:r.channels.toRgba(i,o,n)})}for(let e=0;e<24;e++){const s=8+10*e;t.push({css:r.channels.toCss(s,s,s),rgba:r.channels.toRgba(s,s,s)})}return t})())},993:(t,e)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.rgba=e.rgb=e.css=e.color=e.channels=e.NULL_COLOR=void 0,e.toPaddedHex=c,e.contrastRatio=_;let s=0,r=0,i=0,o=0;var n,l,a,u,h;function c(t){const e=t.toString(16);return e.length<2?"0"+e:e}function _(t,e){return t<e?(e+.05)/(t+.05):(t+.05)/(e+.05)}e.NULL_COLOR={css:"#00000000",rgba:0},function(t){t.toCss=function(t,e,s,r){return void 0!==r?`#${c(t)}${c(e)}${c(s)}${c(r)}`:`#${c(t)}${c(e)}${c(s)}`},t.toRgba=function(t,e,s,r=255){return(t<<24|e<<16|s<<8|r)>>>0},t.toColor=function(e,s,r,i){return{css:t.toCss(e,s,r,i),rgba:t.toRgba(e,s,r,i)}}}(n||(e.channels=n={})),function(t){function e(t,e){return o=Math.round(255*e),[s,r,i]=h.toChannels(t.rgba),{css:n.toCss(s,r,i,o),rgba:n.toRgba(s,r,i,o)}}t.blend=function(t,e){if(o=(255&e.rgba)/255,1===o)return{css:e.css,rgba:e.rgba};const l=e.rgba>>24&255,a=e.rgba>>16&255,u=e.rgba>>8&255,h=t.rgba>>24&255,c=t.rgba>>16&255,_=t.rgba>>8&255;return s=h+Math.round((l-h)*o),r=c+Math.round((a-c)*o),i=_+Math.round((u-_)*o),{css:n.toCss(s,r,i),rgba:n.toRgba(s,r,i)}},t.isOpaque=function(t){return!(255&~t.rgba)},t.ensureContrastRatio=function(t,e,s){const r=h.ensureContrastRatio(t.rgba,e.rgba,s);if(r)return n.toColor(r>>24&255,r>>16&255,r>>8&255)},t.opaque=function(t){const e=(255|t.rgba)>>>0;return[s,r,i]=h.toChannels(e),{css:n.toCss(s,r,i),rgba:e}},t.opacity=e,t.multiplyOpacity=function(t,s){return o=255&t.rgba,e(t,o*s/255)},t.toColorRGB=function(t){return[t.rgba>>24&255,t.rgba>>16&255,t.rgba>>8&255]}}(l||(e.color=l={})),function(t){let e,l;try{const t=document.createElement("canvas");t.width=1,t.height=1;const s=t.getContext("2d",{willReadFrequently:!0});s&&(e=s,e.globalCompositeOperation="copy",l=e.createLinearGradient(0,0,1,1))}catch{}t.toColor=function(t){if(t.match(/#[\da-f]{3,8}/i))switch(t.length){case 4:return s=parseInt(t.slice(1,2).repeat(2),16),r=parseInt(t.slice(2,3).repeat(2),16),i=parseInt(t.slice(3,4).repeat(2),16),n.toColor(s,r,i);case 5:return s=parseInt(t.slice(1,2).repeat(2),16),r=parseInt(t.slice(2,3).repeat(2),16),i=parseInt(t.slice(3,4).repeat(2),16),o=parseInt(t.slice(4,5).repeat(2),16),n.toColor(s,r,i,o);case 7:return{css:t,rgba:(parseInt(t.slice(1),16)<<8|255)>>>0};case 9:return{css:t,rgba:parseInt(t.slice(1),16)>>>0}}const a=t.match(/rgba?\(\s*(\d{1,3})\s*,\s*(\d{1,3})\s*,\s*(\d{1,3})\s*(,\s*(0|1|\d?\.(\d+))\s*)?\)/);if(a)return s=parseInt(a[1]),r=parseInt(a[2]),i=parseInt(a[3]),o=Math.round(255*(void 0===a[5]?1:parseFloat(a[5]))),n.toColor(s,r,i,o);if(!e||!l)throw new Error("css.toColor: Unsupported css format");if(e.fillStyle=l,e.fillStyle=t,"string"!=typeof e.fillStyle)throw new Error("css.toColor: Unsupported css format");if(e.fillRect(0,0,1,1),[s,r,i,o]=e.getImageData(0,0,1,1).data,255!==o)throw new Error("css.toColor: Unsupported css format");return{rgba:n.toRgba(s,r,i,o),css:t}}}(a||(e.css=a={})),function(t){function e(t,e,s){const r=t/255,i=e/255,o=s/255;return.2126*(r<=.03928?r/12.92:Math.pow((r+.055)/1.055,2.4))+.7152*(i<=.03928?i/12.92:Math.pow((i+.055)/1.055,2.4))+.0722*(o<=.03928?o/12.92:Math.pow((o+.055)/1.055,2.4))}t.relativeLuminance=function(t){return e(t>>16&255,t>>8&255,255&t)},t.relativeLuminance2=e}(u||(e.rgb=u={})),function(t){function e(t,e,s){const r=t>>24&255,i=t>>16&255,o=t>>8&255;let n=e>>24&255,l=e>>16&255,a=e>>8&255,h=_(u.relativeLuminance2(n,l,a),u.relativeLuminance2(r,i,o));for(;h<s&&(n>0||l>0||a>0);)n-=Math.max(0,Math.ceil(.1*n)),l-=Math.max(0,Math.ceil(.1*l)),a-=Math.max(0,Math.ceil(.1*a)),h=_(u.relativeLuminance2(n,l,a),u.relativeLuminance2(r,i,o));return(n<<24|l<<16|a<<8|255)>>>0}function l(t,e,s){const r=t>>24&255,i=t>>16&255,o=t>>8&255;let n=e>>24&255,l=e>>16&255,a=e>>8&255,h=_(u.relativeLuminance2(n,l,a),u.relativeLuminance2(r,i,o));for(;h<s&&(n<255||l<255||a<255);)n=Math.min(255,n+Math.ceil(.1*(255-n))),l=Math.min(255,l+Math.ceil(.1*(255-l))),a=Math.min(255,a+Math.ceil(.1*(255-a))),h=_(u.relativeLuminance2(n,l,a),u.relativeLuminance2(r,i,o));return(n<<24|l<<16|a<<8|255)>>>0}t.blend=function(t,e){if(o=(255&e)/255,1===o)return e;const l=e>>24&255,a=e>>16&255,u=e>>8&255,h=t>>24&255,c=t>>16&255,_=t>>8&255;return s=h+Math.round((l-h)*o),r=c+Math.round((a-c)*o),i=_+Math.round((u-_)*o),n.toRgba(s,r,i)},t.ensureContrastRatio=function(t,s,r){const i=u.relativeLuminance(t>>8),o=u.relativeLuminance(s>>8);if(_(i,o)<r){if(o<i){const o=e(t,s,r),n=_(i,u.relativeLuminance(o>>8));if(n<r){const e=l(t,s,r);return n>_(i,u.relativeLuminance(e>>8))?o:e}return o}const n=l(t,s,r),a=_(i,u.relativeLuminance(n>>8));if(a<r){const o=e(t,s,r);return a>_(i,u.relativeLuminance(o>>8))?n:o}return n}},t.reduceLuminance=e,t.increaseLuminance=l,t.toChannels=function(t){return[t>>24&255,t>>16&255,t>>8&255,255&t]}}(h||(e.rgba=h={}))}},e={};function s(r){var i=e[r];if(void 0!==i)return i.exports;var o=e[r]={exports:{}};return t[r](o,o.exports,s),o.exports}var r={};return(()=>{var t=r;Object.defineProperty(t,"__esModule",{value:!0}),t.HTMLSerializeHandler=t.SerializeAddon=void 0;const e=s(992);function i(t,e,s){return Math.max(e,Math.min(t,s))}class o{constructor(t){this._buffer=t}serialize(t,e){const s=this._buffer.getNullCell(),r=this._buffer.getNullCell();let i=s;const o=t.start.y,n=t.end.y,l=t.start.x,a=t.end.x;this._beforeSerialize(n-o,o,n);for(let e=o;e<=n;e++){const o=this._buffer.getLine(e);if(o){const n=e===t.start.y?l:0,u=e===t.end.y?a:o.length;for(let t=n;t<u;t++){const n=o.getCell(t,i===s?r:s);n?(this._nextCell(n,i,e,t),i=n):console.warn(`Can't get cell at row=${e}, col=${t}`)}}this._rowEnd(e,e===n)}return this._afterSerialize(),this._serializeString(e)}_nextCell(t,e,s,r){}_rowEnd(t,e){}_beforeSerialize(t,e,s){}_afterSerialize(){}_serializeString(t){return""}}function n(t,e){return t.getFgColorMode()===e.getFgColorMode()&&t.getFgColor()===e.getFgColor()}function l(t,e){return t.getBgColorMode()===e.getBgColorMode()&&t.getBgColor()===e.getBgColor()}function a(t,e){return t.isInverse()===e.isInverse()&&t.isBold()===e.isBold()&&t.isUnderline()===e.isUnderline()&&t.isOverline()===e.isOverline()&&t.isBlink()===e.isBlink()&&t.isInvisible()===e.isInvisible()&&t.isItalic()===e.isItalic()&&t.isDim()===e.isDim()&&t.isStrikethrough()===e.isStrikethrough()}class u extends o{constructor(t,e){super(t),this._terminal=e,this._rowIndex=0,this._allRows=new Array,this._allRowSeparators=new Array,this._currentRow="",this._nullCellCount=0,this._cursorStyle=this._buffer.getNullCell(),this._cursorStyleRow=0,this._cursorStyleCol=0,this._backgroundCell=this._buffer.getNullCell(),this._firstRow=0,this._lastCursorRow=0,this._lastCursorCol=0,this._lastContentCursorRow=0,this._lastContentCursorCol=0,this._thisRowLastChar=this._buffer.getNullCell(),this._thisRowLastSecondChar=this._buffer.getNullCell(),this._nextRowFirstChar=this._buffer.getNullCell()}_beforeSerialize(t,e,s){this._allRows=new Array(t),this._lastContentCursorRow=e,this._lastCursorRow=e,this._firstRow=e}_rowEnd(t,e){this._nullCellCount>0&&!l(this._cursorStyle,this._backgroundCell)&&(this._currentRow+=`[${this._nullCellCount}X`);let s="";if(!e){t-this._firstRow>=this._terminal.rows&&this._buffer.getLine(this._cursorStyleRow)?.getCell(this._cursorStyleCol,this._backgroundCell);const e=this._buffer.getLine(t),r=this._buffer.getLine(t+1);if(r.isWrapped){s="";const i=e.getCell(e.length-1,this._thisRowLastChar),o=e.getCell(e.length-2,this._thisRowLastSecondChar),n=r.getCell(0,this._nextRowFirstChar),a=n.getWidth()>1;let u=!1;(n.getChars()&&a?this._nullCellCount<=1:this._nullCellCount<=0)&&((i.getChars()||0===i.getWidth())&&l(i,n)&&(u=!0),a&&(o.getChars()||0===o.getWidth())&&l(i,n)&&l(o,n)&&(u=!0)),u||(s="-".repeat(this._nullCellCount+1),s+="[1D[1X",this._nullCellCount>0&&(s+="[A",s+=`[${e.length-this._nullCellCount}C`,s+=`[${this._nullCellCount}X`,s+=`[${e.length-this._nullCellCount}D`,s+="[B"),this._lastContentCursorRow=t+1,this._lastContentCursorCol=0,this._lastCursorRow=t+1,this._lastCursorCol=0)}else s="\r\n",this._lastCursorRow=t+1,this._lastCursorCol=0}this._allRows[this._rowIndex]=this._currentRow,this._allRowSeparators[this._rowIndex++]=s,this._currentRow="",this._nullCellCount=0}_diffStyle(t,e){const s=[],r=!n(t,e),i=!l(t,e),o=!a(t,e);if(r||i||o)if(t.isAttributeDefault())e.isAttributeDefault()||s.push(0);else{if(r){const e=t.getFgColor();t.isFgRGB()?s.push(38,2,e>>>16&255,e>>>8&255,255&e):t.isFgPalette()?e>=16?s.push(38,5,e):s.push(8&e?90+(7&e):30+(7&e)):s.push(39)}if(i){const e=t.getBgColor();t.isBgRGB()?s.push(48,2,e>>>16&255,e>>>8&255,255&e):t.isBgPalette()?e>=16?s.push(48,5,e):s.push(8&e?100+(7&e):40+(7&e)):s.push(49)}o&&(t.isInverse()!==e.isInverse()&&s.push(t.isInverse()?7:27),t.isBold()!==e.isBold()&&s.push(t.isBold()?1:22),t.isUnderline()!==e.isUnderline()&&s.push(t.isUnderline()?4:24),t.isOverline()!==e.isOverline()&&s.push(t.isOverline()?53:55),t.isBlink()!==e.isBlink()&&s.push(t.isBlink()?5:25),t.isInvisible()!==e.isInvisible()&&s.push(t.isInvisible()?8:28),t.isItalic()!==e.isItalic()&&s.push(t.isItalic()?3:23),t.isDim()!==e.isDim()&&s.push(t.isDim()?2:22),t.isStrikethrough()!==e.isStrikethrough()&&s.push(t.isStrikethrough()?9:29))}return s}_nextCell(t,e,s,r){if(0===t.getWidth())return;const i=""===t.getChars(),o=this._diffStyle(t,this._cursorStyle);if(i?!l(this._cursorStyle,t):o.length>0){this._nullCellCount>0&&(l(this._cursorStyle,this._backgroundCell)||(this._currentRow+=`[${this._nullCellCount}X`),this._currentRow+=`[${this._nullCellCount}C`,this._nullCellCount=0),this._lastContentCursorRow=this._lastCursorRow=s,this._lastContentCursorCol=this._lastCursorCol=r,this._currentRow+=`[${o.join(";")}m`;const t=this._buffer.getLine(s);void 0!==t&&(t.getCell(r,this._cursorStyle),this._cursorStyleRow=s,this._cursorStyleCol=r)}i?this._nullCellCount+=t.getWidth():(this._nullCellCount>0&&(l(this._cursorStyle,this._backgroundCell)||(this._currentRow+=`[${this._nullCellCount}X`),this._currentRow+=`[${this._nullCellCount}C`,this._nullCellCount=0),this._currentRow+=t.getChars(),this._lastContentCursorRow=this._lastCursorRow=s,this._lastContentCursorCol=this._lastCursorCol=r+t.getWidth())}_serializeString(t){let e=this._allRows.length;this._buffer.length-this._firstRow<=this._terminal.rows&&(e=this._lastContentCursorRow+1-this._firstRow,this._lastCursorCol=this._lastContentCursorCol,this._lastCursorRow=this._lastContentCursorRow);let s="";for(let t=0;t<e;t++)s+=this._allRows[t],t+1<e&&(s+=this._allRowSeparators[t]);if(!t){const t=this._buffer.baseY+this._buffer.cursorY,e=this._buffer.cursorX,i=t=>{t>0?s+=`[${t}C`:t<0&&(s+=`[${-t}D`)};(t!==this._lastCursorRow||e!==this._lastCursorCol)&&((r=t-this._lastCursorRow)>0?s+=`[${r}B`:r<0&&(s+=`[${-r}A`),i(e-this._lastCursorCol))}var r;const i=this._terminal._core._inputHandler._curAttrData,o=this._diffStyle(i,this._cursorStyle);return o.length>0&&(s+=`[${o.join(";")}m`),s}}t.SerializeAddon=class{activate(t){this._terminal=t}_serializeBufferByScrollback(t,e,s){const r=e.length,o=void 0===s?r:i(s+t.rows,0,r);return this._serializeBufferByRange(t,e,{start:r-o,end:r-1},!1)}_serializeBufferByRange(t,e,s,r){return new u(e,t).serialize({start:{x:0,y:"number"==typeof s.start?s.start:s.start.line},end:{x:t.cols,y:"number"==typeof s.end?s.end:s.end.line}},r)}_serializeBufferAsHTML(t,e){const s=t.buffer.active,r=new h(s,t,e);if(!e.onlySelection){const o=s.length,n=e.scrollback,l=void 0===n?o:i(n+t.rows,0,o);return r.serialize({start:{x:0,y:o-l},end:{x:t.cols,y:o-1}})}const o=this._terminal?.getSelectionPosition();return void 0!==o?r.serialize({start:{x:o.start.x,y:o.start.y},end:{x:o.end.x,y:o.end.y}}):""}_serializeModes(t){let e="";const s=t.modes;if(s.applicationCursorKeysMode&&(e+="[?1h"),s.applicationKeypadMode&&(e+="[?66h"),s.bracketedPasteMode&&(e+="[?2004h"),s.insertMode&&(e+="[4h"),s.originMode&&(e+="[?6h"),s.reverseWraparoundMode&&(e+="[?45h"),s.sendFocusMode&&(e+="[?1004h"),!1===s.wraparoundMode&&(e+="[?7l"),"none"!==s.mouseTrackingMode)switch(s.mouseTrackingMode){case"x10":e+="[?9h";break;case"vt200":e+="[?1000h";break;case"drag":e+="[?1002h";break;case"any":e+="[?1003h"}return e}serialize(t){if(!this._terminal)throw new Error("Cannot use addon until it has been loaded");let e=t?.range?this._serializeBufferByRange(this._terminal,this._terminal.buffer.normal,t.range,!0):this._serializeBufferByScrollback(this._terminal,this._terminal.buffer.normal,t?.scrollback);return t?.excludeAltBuffer||"alternate"!==this._terminal.buffer.active.type||(e+=`[?1049h[H${this._serializeBufferByScrollback(this._terminal,this._terminal.buffer.alternate,void 0)}`),t?.excludeModes||(e+=this._serializeModes(this._terminal)),e}serializeAsHTML(t){if(!this._terminal)throw new Error("Cannot use addon until it has been loaded");return this._serializeBufferAsHTML(this._terminal,t||{})}dispose(){}};class h extends o{constructor(t,s,r){super(t),this._terminal=s,this._options=r,this._currentRow="",this._htmlContent="",s._core._themeService?this._ansiColors=s._core._themeService.colors.ansi:this._ansiColors=e.DEFAULT_ANSI_COLORS}_padStart(t,e,s){return e|=0,s=s??" ",t.length>e?t:((e-=t.length)>s.length&&(s+=s.repeat(e/s.length)),s.slice(0,e)+t)}_beforeSerialize(t,e,s){this._htmlContent+="<html><body>\x3c!--StartFragment--\x3e<pre>";let r="#000000",i="#ffffff";this._options.includeGlobalBackground&&(r=this._terminal.options.theme?.foreground??"#ffffff",i=this._terminal.options.theme?.background??"#000000");const o=[];o.push("color: "+r+";"),o.push("background-color: "+i+";"),o.push("font-family: "+this._terminal.options.fontFamily+";"),o.push("font-size: "+this._terminal.options.fontSize+"px;"),this._htmlContent+="<div style='"+o.join(" ")+"'>"}_afterSerialize(){this._htmlContent+="</div>",this._htmlContent+="</pre>\x3c!--EndFragment--\x3e</body></html>"}_rowEnd(t,e){this._htmlContent+="<div><span>"+this._currentRow+"</span></div>",this._currentRow=""}_getHexColor(t,e){const s=e?t.getFgColor():t.getBgColor();return(e?t.isFgRGB():t.isBgRGB())?"#"+[s>>16&255,s>>8&255,255&s].map((t=>this._padStart(t.toString(16),2,"0"))).join(""):(e?t.isFgPalette():t.isBgPalette())?this._ansiColors[s].css:void 0}_diffStyle(t,e){const s=[],r=!n(t,e),i=!l(t,e),o=!a(t,e);if(r||i||o){const e=this._getHexColor(t,!0);e&&s.push("color: "+e+";");const r=this._getHexColor(t,!1);return r&&s.push("background-color: "+r+";"),t.isInverse()&&s.push("color: #000000; background-color: #BFBFBF;"),t.isBold()&&s.push("font-weight: bold;"),t.isUnderline()&&t.isOverline()?s.push("text-decoration: overline underline;"):t.isUnderline()?s.push("text-decoration: underline;"):t.isOverline()&&s.push("text-decoration: overline;"),t.isBlink()&&s.push("text-decoration: blink;"),t.isInvisible()&&s.push("visibility: hidden;"),t.isItalic()&&s.push("font-style: italic;"),t.isDim()&&s.push("opacity: 0.5;"),t.isStrikethrough()&&s.push("text-decoration: line-through;"),s}}_nextCell(t,e,s,r){if(0===t.getWidth())return;const i=""===t.getChars(),o=this._diffStyle(t,e);o&&(this._currentRow+=0===o.length?"</span><span>":"</span><span style='"+o.join(" ")+"'>"),this._currentRow+=i?" ":function(t){switch(t){case"&":return"&amp;";case"<":return"&lt;"}return t}(t.getChars())}_serializeString(){return this._htmlContent}}t.HTMLSerializeHandler=h})(),r})()));//# sourceMappingURL=https://main.vscode-cdn.net/sourcemaps/488a1f239235055e34e673291fb8d8c810886f81/node_modules/@xterm/addon-serialize/lib/addon-serialize.js.map