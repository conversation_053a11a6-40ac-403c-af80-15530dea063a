{"format": "graph-model", "generatedBy": "2.5.0", "convertedBy": "TensorFlow.js Converter v3.7.0", "signature": {"inputs": {"inputs": {"name": "Placeholder:0", "dtype": "DT_STRING", "tensorShape": {"dim": [{"size": "-1"}]}}}, "outputs": {"scores": {"name": "head/predictions/probabilities:0", "dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "-1"}, {"size": "54"}]}}, "classes": {"name": "head/Tile:0", "dtype": "DT_STRING", "tensorShape": {"dim": [{"size": "-1"}, {"size": "54"}]}}}}, "modelTopology": {"node": [{"name": "head/ExpandDims/input", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_STRING", "tensorShape": {"dim": [{"size": "54"}]}}}, "dtype": {"type": "DT_STRING"}}}, {"name": "head/ExpandDims/dim", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "head/strided_slice/stack", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "1"}]}}}}}, {"name": "head/strided_slice/stack_1", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "1"}]}}}}}, {"name": "head/strided_slice/stack_2", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "1"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "head/Tile/multiples/1", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {}}}}}, {"name": "dnn/input_from_feature_columns/input_layer/content_embedding_1/content_embedding_weights/Reshape_1/shape", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "2"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "dnn/input_from_feature_columns/input_layer/content_embedding_1/content_embedding_weights/stack/0", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "dnn/input_from_feature_columns/input_layer/content_embedding_1/content_embedding_weights/strided_slice/stack", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "1"}]}}}}}, {"name": "dnn/input_from_feature_columns/input_layer/content_embedding_1/content_embedding_weights/strided_slice/stack_1", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "1"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "dnn/input_from_feature_columns/input_layer/content_embedding_1/content_embedding_weights/strided_slice/stack_2", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "1"}]}}}}}, {"name": "dnn/input_from_feature_columns/input_layer/content_embedding/embedding_weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "5000"}, {"size": "70"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "dnn/input_from_feature_columns/input_layer/content_embedding_1/content_embedding_weights/GatherV2_1/axis", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {}}}}}, {"name": "dnn/input_from_feature_columns/input_layer/content_embedding_1/content_embedding_weights/GreaterEqual/y", "op": "Const", "attr": {"dtype": {"type": "DT_INT64"}, "value": {"tensor": {"dtype": "DT_INT64", "tensorShape": {}}}}}, {"name": "dnn/input_from_feature_columns/input_layer/content_embedding_1/content_embedding_weights/Reshape/shape", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "1"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "dnn/input_from_feature_columns/input_layer/content_embedding_1/content_embedding_weights/GatherV2_2/axis", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "dnn/input_from_feature_columns/input_layer/content_embedding_1/to_sparse_input/ignore_value/x", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_STRING", "tensorShape": {}}}, "dtype": {"type": "DT_STRING"}}}, {"name": "dnn/input_from_feature_columns/input_layer/content_embedding_1/content_embedding_weights/Slice/begin", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "1"}]}}}}}, {"name": "dnn/input_from_feature_columns/input_layer/content_embedding_1/content_embedding_weights/Slice/size", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "1"}]}}}}}, {"name": "dnn/input_from_feature_columns/input_layer/content_embedding_1/content_embedding_weights/Const", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "1"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "dnn/input_from_feature_columns/input_layer/content_embedding_1/content_embedding_weights/GatherV2/indices", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "dnn/input_from_feature_columns/input_layer/content_embedding_1/content_embedding_weights/GatherV2/axis", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "dnn/input_from_feature_columns/input_layer/content_embedding_1/content_embedding_weights/SparseFillEmptyRows/Const", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT64", "tensorShape": {}}}, "dtype": {"type": "DT_INT64"}}}, {"name": "dnn/input_from_feature_columns/input_layer/content_embedding_1/content_embedding_weights/embedding_lookup_sparse/strided_slice/stack", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "2"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "dnn/input_from_feature_columns/input_layer/content_embedding_1/content_embedding_weights/embedding_lookup_sparse/strided_slice/stack_1", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "2"}]}}}}}, {"name": "dnn/input_from_feature_columns/input_layer/content_embedding_1/content_embedding_weights/embedding_lookup_sparse/strided_slice/stack_2", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "2"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "dnn/hiddenlayer_0/kernel", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "70"}, {"size": "512"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "dnn/hiddenlayer_0/bias", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "512"}]}}}}}, {"name": "dnn/hiddenlayer_1/kernel", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "512"}, {"size": "32"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "dnn/hiddenlayer_1/bias", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "32"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "dnn/logits/kernel", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "32"}, {"size": "54"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "dnn/logits/bias", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "54"}]}}}}}, {"name": "linear/linear_model/linear/linear_model/linear/linear_model/content/weighted_sum/Slice_1/begin", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "1"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "linear/linear_model/linear/linear_model/linear/linear_model/content/weighted_sum/Slice_1/size", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "1"}]}}}}}, {"name": "linear/linear_model/linear/linear_model/linear/linear_model/content/weighted_sum/Reshape_1/shape", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "2"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "linear/linear_model/linear/linear_model/linear/linear_model/content/weighted_sum/stack/0", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {}}}}}, {"name": "linear/linear_model/linear/linear_model/linear/linear_model/content/weighted_sum/strided_slice/stack", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "1"}]}}}}}, {"name": "linear/linear_model/linear/linear_model/linear/linear_model/content/weighted_sum/strided_slice/stack_1", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "1"}]}}}}}, {"name": "linear/linear_model/linear/linear_model/linear/linear_model/content/weighted_sum/strided_slice/stack_2", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "1"}]}}}}}, {"name": "linear/linear_model/content/weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "5000"}, {"size": "54"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "linear/linear_model/linear/linear_model/linear/linear_model/content/weighted_sum/GatherV2_1/axis", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "linear/linear_model/linear/linear_model/linear/linear_model/content/weighted_sum/GreaterEqual/y", "op": "Const", "attr": {"dtype": {"type": "DT_INT64"}, "value": {"tensor": {"dtype": "DT_INT64", "tensorShape": {}}}}}, {"name": "linear/linear_model/linear/linear_model/linear/linear_model/content/weighted_sum/Reshape/shape", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "1"}]}}}}}, {"name": "linear/linear_model/linear/linear_model/linear/linear_model/content/weighted_sum/GatherV2_2/axis", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "linear/linear_model/linear/linear_model/linear/linear_model/content/weighted_sum/Slice/begin", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "1"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "linear/linear_model/linear/linear_model/linear/linear_model/content/weighted_sum/Slice/size", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "1"}]}}}}}, {"name": "linear/linear_model/linear/linear_model/linear/linear_model/content/weighted_sum/Const", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "1"}]}}}}}, {"name": "linear/linear_model/linear/linear_model/linear/linear_model/content/to_sparse_input/ignore_value/x", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_STRING", "tensorShape": {}}}, "dtype": {"type": "DT_STRING"}}}, {"name": "map/while/loop_counter", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {}}}}}, {"name": "map/Const", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "map/TensorArrayV2_1/element_shape", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "map/strided_slice/stack", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "1"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "map/strided_slice/stack_1", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "1"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "map/strided_slice/stack_2", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "1"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "Placeholder", "op": "Placeholder", "attr": {"shape": {"shape": {"dim": [{"size": "-1"}]}}, "dtype": {"type": "DT_STRING"}}}, {"name": "map/TensorArrayUnstack/Const", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "map/TensorArrayV2Stack/TensorListStack/element_shape", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "1"}]}}}}}, {"name": "linear/linear_model/linear/linear_model/linear/linear_model/content/strided_slice/stack", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "1"}]}}}}}, {"name": "linear/linear_model/linear/linear_model/linear/linear_model/content/strided_slice/stack_1", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "1"}]}}}}}, {"name": "linear/linear_model/linear/linear_model/linear/linear_model/content/strided_slice/stack_2", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "1"}]}}}}}, {"name": "linear/linear_model/linear/linear_model/linear/linear_model/content/Cast/x/1", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "linear/linear_model/linear/linear_model/linear/linear_model/content/weighted_sum/GatherV2/indices", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "linear/linear_model/linear/linear_model/linear/linear_model/content/weighted_sum/GatherV2/axis", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "linear/linear_model/linear/linear_model/linear/linear_model/content/weighted_sum/SparseFillEmptyRows/Const", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT64", "tensorShape": {}}}, "dtype": {"type": "DT_INT64"}}}, {"name": "linear/linear_model/linear/linear_model/linear/linear_model/content/weighted_sum/embedding_lookup_sparse/strided_slice/stack", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "2"}]}}}}}, {"name": "linear/linear_model/linear/linear_model/linear/linear_model/content/weighted_sum/embedding_lookup_sparse/strided_slice/stack_1", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "2"}]}}}}}, {"name": "linear/linear_model/linear/linear_model/linear/linear_model/content/weighted_sum/embedding_lookup_sparse/strided_slice/stack_2", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "2"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "linear/linear_model/linear/linear_model/linear/linear_model/content/weighted_sum/Slice_2/begin", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "1"}]}}}}}, {"name": "linear/linear_model/linear/linear_model/linear/linear_model/content/weighted_sum/Slice_2/size", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "1"}]}}}}}, {"name": "linear/linear_model/linear/linear_model/linear/linear_model/content/weighted_sum/concat/axis", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {}}}}}, {"name": "linear/linear_model/bias_weights", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "54"}]}}}}}, {"name": "head/ExpandDims", "op": "ExpandDims", "input": ["head/ExpandDims/input", "head/ExpandDims/dim"], "attr": {"T": {"type": "DT_STRING"}, "Tdim": {"type": "DT_INT32"}}}, {"name": "map/Shape", "op": "<PERSON><PERSON><PERSON>", "input": ["Placeholder"], "attr": {"out_type": {"type": "DT_INT32"}, "T": {"type": "DT_STRING"}}}, {"name": "map/TensorArrayUnstack/TensorListFromTensor", "op": "TensorListFromTensor", "input": ["Placeholder", "map/TensorArrayUnstack/Const"], "attr": {"shape_type": {"type": "DT_INT32"}, "element_dtype": {"type": "DT_STRING"}}}, {"name": "map/strided_slice", "op": "StridedSlice", "input": ["map/Shape", "map/strided_slice/stack", "map/strided_slice/stack_1", "map/strided_slice/stack_2"], "attr": {"begin_mask": {"i": "0"}, "shrink_axis_mask": {"i": "1"}, "new_axis_mask": {"i": "0"}, "end_mask": {"i": "0"}, "Index": {"type": "DT_INT32"}, "T": {"type": "DT_INT32"}, "ellipsis_mask": {"i": "0"}}}, {"name": "map/TensorArrayV2_1", "op": "TensorList<PERSON>eserve", "input": ["map/TensorArrayV2_1/element_shape", "map/strided_slice"], "attr": {"shape_type": {"type": "DT_INT32"}, "element_dtype": {"type": "DT_STRING"}}}, {"name": "map/while", "op": "State<PERSON><PERSON><PERSON><PERSON>", "input": ["map/while/loop_counter", "map/strided_slice", "map/Const", "map/TensorArrayV2_1", "map/strided_slice", "map/TensorArrayUnstack/TensorListFromTensor"], "attr": {"_read_only_resource_inputs": {"list": {}}, "parallel_iterations": {"i": "10"}, "_lower_using_switch_merge": {"b": true}, "cond": {"func": {"name": "map_while_cond_49114"}}, "output_shapes": {"list": {"shape": [{}, {}, {}, {}, {}, {}]}}, "body": {"func": {"name": "map_while_body_49115"}}, "T": {"list": {"type": ["DT_INT32", "DT_INT32", "DT_INT32", "DT_VARIANT", "DT_INT32", "DT_VARIANT"]}}, "_num_original_outputs": {"i": "6"}}}, {"name": "map/TensorArrayV2Stack/TensorListStack", "op": "TensorListStack", "input": ["map/while:3", "map/TensorArrayV2Stack/TensorListStack/element_shape"], "attr": {"num_elements": {"i": "-1"}, "element_dtype": {"type": "DT_STRING"}}}, {"name": "dnn/input_from_feature_columns/input_layer/content_embedding_1/to_sparse_input/NotEqual", "op": "NotEqual", "input": ["map/TensorArrayV2Stack/TensorListStack", "dnn/input_from_feature_columns/input_layer/content_embedding_1/to_sparse_input/ignore_value/x"], "attr": {"incompatible_shape_error": {"b": true}, "T": {"type": "DT_STRING"}}}, {"name": "dnn/input_from_feature_columns/input_layer/content_embedding_1/to_sparse_input/dense_shape", "op": "<PERSON><PERSON><PERSON>", "input": ["map/TensorArrayV2Stack/TensorListStack"], "attr": {"out_type": {"type": "DT_INT64"}, "T": {"type": "DT_STRING"}}}, {"name": "linear/linear_model/linear/linear_model/linear/linear_model/content/to_sparse_input/NotEqual", "op": "NotEqual", "input": ["map/TensorArrayV2Stack/TensorListStack", "linear/linear_model/linear/linear_model/linear/linear_model/content/to_sparse_input/ignore_value/x"], "attr": {"incompatible_shape_error": {"b": true}, "T": {"type": "DT_STRING"}}}, {"name": "linear/linear_model/linear/linear_model/linear/linear_model/content/to_sparse_input/dense_shape", "op": "<PERSON><PERSON><PERSON>", "input": ["map/TensorArrayV2Stack/TensorListStack"], "attr": {"out_type": {"type": "DT_INT64"}, "T": {"type": "DT_STRING"}}}, {"name": "dnn/input_from_feature_columns/input_layer/content_embedding_1/to_sparse_input/indices", "op": "Where", "input": ["dnn/input_from_feature_columns/input_layer/content_embedding_1/to_sparse_input/NotEqual"], "attr": {"T": {"type": "DT_BOOL"}}}, {"name": "dnn/input_from_feature_columns/input_layer/content_embedding_1/content_embedding_weights/Slice", "op": "Slice", "input": ["dnn/input_from_feature_columns/input_layer/content_embedding_1/to_sparse_input/dense_shape", "dnn/input_from_feature_columns/input_layer/content_embedding_1/content_embedding_weights/Slice/begin", "dnn/input_from_feature_columns/input_layer/content_embedding_1/content_embedding_weights/Slice/size"], "attr": {"Index": {"type": "DT_INT32"}, "T": {"type": "DT_INT64"}}}, {"name": "dnn/input_from_feature_columns/input_layer/content_embedding_1/content_embedding_weights/GatherV2", "op": "GatherV2", "input": ["dnn/input_from_feature_columns/input_layer/content_embedding_1/to_sparse_input/dense_shape", "dnn/input_from_feature_columns/input_layer/content_embedding_1/content_embedding_weights/GatherV2/indices", "dnn/input_from_feature_columns/input_layer/content_embedding_1/content_embedding_weights/GatherV2/axis"], "attr": {"batch_dims": {"i": "0"}, "Tindices": {"type": "DT_INT32"}, "Taxis": {"type": "DT_INT32"}, "Tparams": {"type": "DT_INT64"}}}, {"name": "linear/linear_model/linear/linear_model/linear/linear_model/content/to_sparse_input/indices", "op": "Where", "input": ["linear/linear_model/linear/linear_model/linear/linear_model/content/to_sparse_input/NotEqual"], "attr": {"T": {"type": "DT_BOOL"}}}, {"name": "linear/linear_model/linear/linear_model/linear/linear_model/content/Shape/Cast", "op": "Cast", "input": ["linear/linear_model/linear/linear_model/linear/linear_model/content/to_sparse_input/dense_shape"], "attr": {"Truncate": {"b": false}, "SrcT": {"type": "DT_INT64"}, "DstT": {"type": "DT_INT32"}}}, {"name": "dnn/input_from_feature_columns/input_layer/content_embedding_1/to_sparse_input/values", "op": "GatherNd", "input": ["map/TensorArrayV2Stack/TensorListStack", "dnn/input_from_feature_columns/input_layer/content_embedding_1/to_sparse_input/indices"], "attr": {"Tparams": {"type": "DT_STRING"}, "Tindices": {"type": "DT_INT64"}}}, {"name": "dnn/input_from_feature_columns/input_layer/content_embedding_1/content_embedding_weights/Prod", "op": "Prod", "input": ["dnn/input_from_feature_columns/input_layer/content_embedding_1/content_embedding_weights/Slice", "dnn/input_from_feature_columns/input_layer/content_embedding_1/content_embedding_weights/Const"], "attr": {"T": {"type": "DT_INT64"}, "Tidx": {"type": "DT_INT32"}, "keep_dims": {"b": false}}}, {"name": "linear/linear_model/linear/linear_model/linear/linear_model/content/to_sparse_input/values", "op": "GatherNd", "input": ["map/TensorArrayV2Stack/TensorListStack", "linear/linear_model/linear/linear_model/linear/linear_model/content/to_sparse_input/indices"], "attr": {"Tparams": {"type": "DT_STRING"}, "Tindices": {"type": "DT_INT64"}}}, {"name": "linear/linear_model/linear/linear_model/linear/linear_model/content/strided_slice", "op": "StridedSlice", "input": ["linear/linear_model/linear/linear_model/linear/linear_model/content/Shape/Cast", "linear/linear_model/linear/linear_model/linear/linear_model/content/strided_slice/stack", "linear/linear_model/linear/linear_model/linear/linear_model/content/strided_slice/stack_1", "linear/linear_model/linear/linear_model/linear/linear_model/content/strided_slice/stack_2"], "attr": {"Index": {"type": "DT_INT32"}, "ellipsis_mask": {"i": "0"}, "end_mask": {"i": "0"}, "new_axis_mask": {"i": "0"}, "shrink_axis_mask": {"i": "1"}, "begin_mask": {"i": "0"}, "T": {"type": "DT_INT32"}}}, {"name": "dnn/input_from_feature_columns/input_layer/content_embedding_1/lookup", "op": "StringToHashBucketFast", "input": ["dnn/input_from_feature_columns/input_layer/content_embedding_1/to_sparse_input/values"], "attr": {"num_buckets": {"i": "5000"}}}, {"name": "dnn/input_from_feature_columns/input_layer/content_embedding_1/content_embedding_weights/Cast/x", "op": "Pack", "input": ["dnn/input_from_feature_columns/input_layer/content_embedding_1/content_embedding_weights/Prod", "dnn/input_from_feature_columns/input_layer/content_embedding_1/content_embedding_weights/GatherV2"], "attr": {"T": {"type": "DT_INT64"}, "N": {"i": "2"}, "axis": {"i": "0"}}}, {"name": "linear/linear_model/linear/linear_model/linear/linear_model/content/lookup", "op": "StringToHashBucketFast", "input": ["linear/linear_model/linear/linear_model/linear/linear_model/content/to_sparse_input/values"], "attr": {"num_buckets": {"i": "5000"}}}, {"name": "linear/linear_model/linear/linear_model/linear/linear_model/content/Cast/x", "op": "Pack", "input": ["linear/linear_model/linear/linear_model/linear/linear_model/content/strided_slice", "linear/linear_model/linear/linear_model/linear/linear_model/content/Cast/x/1"], "attr": {"axis": {"i": "0"}, "N": {"i": "2"}, "T": {"type": "DT_INT32"}}}, {"name": "dnn/input_from_feature_columns/input_layer/content_embedding_1/content_embedding_weights/GreaterEqual", "op": "GreaterEqual", "input": ["dnn/input_from_feature_columns/input_layer/content_embedding_1/lookup", "dnn/input_from_feature_columns/input_layer/content_embedding_1/content_embedding_weights/GreaterEqual/y"], "attr": {"T": {"type": "DT_INT64"}}}, {"name": "dnn/input_from_feature_columns/input_layer/content_embedding_1/content_embedding_weights/SparseReshape", "op": "SparseReshape", "input": ["dnn/input_from_feature_columns/input_layer/content_embedding_1/to_sparse_input/indices", "dnn/input_from_feature_columns/input_layer/content_embedding_1/to_sparse_input/dense_shape", "dnn/input_from_feature_columns/input_layer/content_embedding_1/content_embedding_weights/Cast/x"]}, {"name": "linear/linear_model/linear/linear_model/linear/linear_model/content/weighted_sum/GreaterEqual", "op": "GreaterEqual", "input": ["linear/linear_model/linear/linear_model/linear/linear_model/content/lookup", "linear/linear_model/linear/linear_model/linear/linear_model/content/weighted_sum/GreaterEqual/y"], "attr": {"T": {"type": "DT_INT64"}}}, {"name": "linear/linear_model/linear/linear_model/linear/linear_model/content/Cast", "op": "Cast", "input": ["linear/linear_model/linear/linear_model/linear/linear_model/content/Cast/x"], "attr": {"SrcT": {"type": "DT_INT32"}, "Truncate": {"b": false}, "DstT": {"type": "DT_INT64"}}}, {"name": "dnn/input_from_feature_columns/input_layer/content_embedding_1/content_embedding_weights/Where", "op": "Where", "input": ["dnn/input_from_feature_columns/input_layer/content_embedding_1/content_embedding_weights/GreaterEqual"], "attr": {"T": {"type": "DT_BOOL"}}}, {"name": "linear/linear_model/linear/linear_model/linear/linear_model/content/weighted_sum/Where", "op": "Where", "input": ["linear/linear_model/linear/linear_model/linear/linear_model/content/weighted_sum/GreaterEqual"], "attr": {"T": {"type": "DT_BOOL"}}}, {"name": "linear/linear_model/linear/linear_model/linear/linear_model/content/SparseReshape", "op": "SparseReshape", "input": ["linear/linear_model/linear/linear_model/linear/linear_model/content/to_sparse_input/indices", "linear/linear_model/linear/linear_model/linear/linear_model/content/to_sparse_input/dense_shape", "linear/linear_model/linear/linear_model/linear/linear_model/content/Cast"]}, {"name": "dnn/input_from_feature_columns/input_layer/content_embedding_1/content_embedding_weights/Reshape", "op": "Reshape", "input": ["dnn/input_from_feature_columns/input_layer/content_embedding_1/content_embedding_weights/Where", "dnn/input_from_feature_columns/input_layer/content_embedding_1/content_embedding_weights/Reshape/shape"], "attr": {"Tshape": {"type": "DT_INT32"}, "T": {"type": "DT_INT64"}}}, {"name": "linear/linear_model/linear/linear_model/linear/linear_model/content/weighted_sum/Reshape", "op": "Reshape", "input": ["linear/linear_model/linear/linear_model/linear/linear_model/content/weighted_sum/Where", "linear/linear_model/linear/linear_model/linear/linear_model/content/weighted_sum/Reshape/shape"], "attr": {"T": {"type": "DT_INT64"}, "Tshape": {"type": "DT_INT32"}}}, {"name": "linear/linear_model/linear/linear_model/linear/linear_model/content/weighted_sum/Cast_1", "op": "Cast", "input": ["linear/linear_model/linear/linear_model/linear/linear_model/content/SparseReshape:1"], "attr": {"DstT": {"type": "DT_INT32"}, "SrcT": {"type": "DT_INT64"}, "Truncate": {"b": false}}}, {"name": "linear/linear_model/linear/linear_model/linear/linear_model/content/weighted_sum/Slice", "op": "Slice", "input": ["linear/linear_model/linear/linear_model/linear/linear_model/content/SparseReshape:1", "linear/linear_model/linear/linear_model/linear/linear_model/content/weighted_sum/Slice/begin", "linear/linear_model/linear/linear_model/linear/linear_model/content/weighted_sum/Slice/size"], "attr": {"T": {"type": "DT_INT64"}, "Index": {"type": "DT_INT32"}}}, {"name": "linear/linear_model/linear/linear_model/linear/linear_model/content/weighted_sum/GatherV2", "op": "GatherV2", "input": ["linear/linear_model/linear/linear_model/linear/linear_model/content/SparseReshape:1", "linear/linear_model/linear/linear_model/linear/linear_model/content/weighted_sum/GatherV2/indices", "linear/linear_model/linear/linear_model/linear/linear_model/content/weighted_sum/GatherV2/axis"], "attr": {"Tparams": {"type": "DT_INT64"}, "Tindices": {"type": "DT_INT32"}, "Taxis": {"type": "DT_INT32"}, "batch_dims": {"i": "0"}}}, {"name": "dnn/input_from_feature_columns/input_layer/content_embedding_1/content_embedding_weights/GatherV2_1", "op": "GatherV2", "input": ["dnn/input_from_feature_columns/input_layer/content_embedding_1/content_embedding_weights/SparseReshape", "dnn/input_from_feature_columns/input_layer/content_embedding_1/content_embedding_weights/Reshape", "dnn/input_from_feature_columns/input_layer/content_embedding_1/content_embedding_weights/GatherV2_1/axis"], "attr": {"Tindices": {"type": "DT_INT64"}, "Taxis": {"type": "DT_INT32"}, "Tparams": {"type": "DT_INT64"}, "batch_dims": {"i": "0"}}}, {"name": "dnn/input_from_feature_columns/input_layer/content_embedding_1/content_embedding_weights/GatherV2_2", "op": "GatherV2", "input": ["dnn/input_from_feature_columns/input_layer/content_embedding_1/lookup", "dnn/input_from_feature_columns/input_layer/content_embedding_1/content_embedding_weights/Reshape", "dnn/input_from_feature_columns/input_layer/content_embedding_1/content_embedding_weights/GatherV2_2/axis"], "attr": {"batch_dims": {"i": "0"}, "Taxis": {"type": "DT_INT32"}, "Tparams": {"type": "DT_INT64"}, "Tindices": {"type": "DT_INT64"}}}, {"name": "linear/linear_model/linear/linear_model/linear/linear_model/content/weighted_sum/GatherV2_2", "op": "GatherV2", "input": ["linear/linear_model/linear/linear_model/linear/linear_model/content/lookup", "linear/linear_model/linear/linear_model/linear/linear_model/content/weighted_sum/Reshape", "linear/linear_model/linear/linear_model/linear/linear_model/content/weighted_sum/GatherV2_2/axis"], "attr": {"Tindices": {"type": "DT_INT64"}, "batch_dims": {"i": "0"}, "Taxis": {"type": "DT_INT32"}, "Tparams": {"type": "DT_INT64"}}}, {"name": "linear/linear_model/linear/linear_model/linear/linear_model/content/weighted_sum/Slice_1", "op": "Slice", "input": ["linear/linear_model/linear/linear_model/linear/linear_model/content/weighted_sum/Cast_1", "linear/linear_model/linear/linear_model/linear/linear_model/content/weighted_sum/Slice_1/begin", "linear/linear_model/linear/linear_model/linear/linear_model/content/weighted_sum/Slice_1/size"], "attr": {"Index": {"type": "DT_INT32"}, "T": {"type": "DT_INT32"}}}, {"name": "linear/linear_model/linear/linear_model/linear/linear_model/content/weighted_sum/Prod", "op": "Prod", "input": ["linear/linear_model/linear/linear_model/linear/linear_model/content/weighted_sum/Slice", "linear/linear_model/linear/linear_model/linear/linear_model/content/weighted_sum/Const"], "attr": {"T": {"type": "DT_INT64"}, "Tidx": {"type": "DT_INT32"}, "keep_dims": {"b": false}}}, {"name": "dnn/input_from_feature_columns/input_layer/content_embedding_1/content_embedding_weights/SparseFillEmptyRows/SparseFillEmptyRows", "op": "SparseFillEmptyRows", "input": ["dnn/input_from_feature_columns/input_layer/content_embedding_1/content_embedding_weights/GatherV2_1", "dnn/input_from_feature_columns/input_layer/content_embedding_1/content_embedding_weights/GatherV2_2", "dnn/input_from_feature_columns/input_layer/content_embedding_1/content_embedding_weights/SparseReshape:1", "dnn/input_from_feature_columns/input_layer/content_embedding_1/content_embedding_weights/SparseFillEmptyRows/Const"], "attr": {"T": {"type": "DT_INT64"}}}, {"name": "linear/linear_model/linear/linear_model/linear/linear_model/content/weighted_sum/Cast/x", "op": "Pack", "input": ["linear/linear_model/linear/linear_model/linear/linear_model/content/weighted_sum/Prod", "linear/linear_model/linear/linear_model/linear/linear_model/content/weighted_sum/GatherV2"], "attr": {"T": {"type": "DT_INT64"}, "axis": {"i": "0"}, "N": {"i": "2"}}}, {"name": "dnn/input_from_feature_columns/input_layer/content_embedding_1/content_embedding_weights/embedding_lookup_sparse/strided_slice", "op": "StridedSlice", "input": ["dnn/input_from_feature_columns/input_layer/content_embedding_1/content_embedding_weights/SparseFillEmptyRows/SparseFillEmptyRows", "dnn/input_from_feature_columns/input_layer/content_embedding_1/content_embedding_weights/embedding_lookup_sparse/strided_slice/stack", "dnn/input_from_feature_columns/input_layer/content_embedding_1/content_embedding_weights/embedding_lookup_sparse/strided_slice/stack_1", "dnn/input_from_feature_columns/input_layer/content_embedding_1/content_embedding_weights/embedding_lookup_sparse/strided_slice/stack_2"], "attr": {"ellipsis_mask": {"i": "0"}, "Index": {"type": "DT_INT32"}, "begin_mask": {"i": "1"}, "end_mask": {"i": "1"}, "new_axis_mask": {"i": "0"}, "T": {"type": "DT_INT64"}, "shrink_axis_mask": {"i": "2"}}}, {"name": "dnn/input_from_feature_columns/input_layer/content_embedding_1/content_embedding_weights/Reshape_1", "op": "Reshape", "input": ["dnn/input_from_feature_columns/input_layer/content_embedding_1/content_embedding_weights/SparseFillEmptyRows/SparseFillEmptyRows:2", "dnn/input_from_feature_columns/input_layer/content_embedding_1/content_embedding_weights/Reshape_1/shape"], "attr": {"T": {"type": "DT_BOOL"}, "Tshape": {"type": "DT_INT32"}}}, {"name": "linear/linear_model/linear/linear_model/linear/linear_model/content/weighted_sum/SparseReshape", "op": "SparseReshape", "input": ["linear/linear_model/linear/linear_model/linear/linear_model/content/SparseReshape", "linear/linear_model/linear/linear_model/linear/linear_model/content/SparseReshape:1", "linear/linear_model/linear/linear_model/linear/linear_model/content/weighted_sum/Cast/x"]}, {"name": "dnn/input_from_feature_columns/input_layer/content_embedding_1/content_embedding_weights/embedding_lookup_sparse", "op": "SparseSegmentMean", "input": ["dnn/input_from_feature_columns/input_layer/content_embedding/embedding_weights", "dnn/input_from_feature_columns/input_layer/content_embedding_1/content_embedding_weights/SparseFillEmptyRows/SparseFillEmptyRows:1", "dnn/input_from_feature_columns/input_layer/content_embedding_1/content_embedding_weights/embedding_lookup_sparse/strided_slice"], "attr": {"Tidx": {"type": "DT_INT64"}, "T": {"type": "DT_FLOAT"}, "Tsegmentids": {"type": "DT_INT64"}}}, {"name": "linear/linear_model/linear/linear_model/linear/linear_model/content/weighted_sum/GatherV2_1", "op": "GatherV2", "input": ["linear/linear_model/linear/linear_model/linear/linear_model/content/weighted_sum/SparseReshape", "linear/linear_model/linear/linear_model/linear/linear_model/content/weighted_sum/Reshape", "linear/linear_model/linear/linear_model/linear/linear_model/content/weighted_sum/GatherV2_1/axis"], "attr": {"Tparams": {"type": "DT_INT64"}, "Tindices": {"type": "DT_INT64"}, "batch_dims": {"i": "0"}, "Taxis": {"type": "DT_INT32"}}}, {"name": "dnn/input_from_feature_columns/input_layer/content_embedding_1/content_embedding_weights/Shape", "op": "<PERSON><PERSON><PERSON>", "input": ["dnn/input_from_feature_columns/input_layer/content_embedding_1/content_embedding_weights/embedding_lookup_sparse"], "attr": {"T": {"type": "DT_FLOAT"}, "out_type": {"type": "DT_INT32"}}}, {"name": "dnn/input_from_feature_columns/input_layer/content_embedding_1/content_embedding_weights/zeros_like", "op": "ZerosLike", "input": ["dnn/input_from_feature_columns/input_layer/content_embedding_1/content_embedding_weights/embedding_lookup_sparse"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "linear/linear_model/linear/linear_model/linear/linear_model/content/weighted_sum/SparseFillEmptyRows/SparseFillEmptyRows", "op": "SparseFillEmptyRows", "input": ["linear/linear_model/linear/linear_model/linear/linear_model/content/weighted_sum/GatherV2_1", "linear/linear_model/linear/linear_model/linear/linear_model/content/weighted_sum/GatherV2_2", "linear/linear_model/linear/linear_model/linear/linear_model/content/weighted_sum/SparseReshape:1", "linear/linear_model/linear/linear_model/linear/linear_model/content/weighted_sum/SparseFillEmptyRows/Const"], "attr": {"T": {"type": "DT_INT64"}}}, {"name": "dnn/input_from_feature_columns/input_layer/content_embedding_1/content_embedding_weights/strided_slice", "op": "StridedSlice", "input": ["dnn/input_from_feature_columns/input_layer/content_embedding_1/content_embedding_weights/Shape", "dnn/input_from_feature_columns/input_layer/content_embedding_1/content_embedding_weights/strided_slice/stack", "dnn/input_from_feature_columns/input_layer/content_embedding_1/content_embedding_weights/strided_slice/stack_1", "dnn/input_from_feature_columns/input_layer/content_embedding_1/content_embedding_weights/strided_slice/stack_2"], "attr": {"end_mask": {"i": "0"}, "shrink_axis_mask": {"i": "1"}, "ellipsis_mask": {"i": "0"}, "Index": {"type": "DT_INT32"}, "T": {"type": "DT_INT32"}, "new_axis_mask": {"i": "0"}, "begin_mask": {"i": "0"}}}, {"name": "linear/linear_model/linear/linear_model/linear/linear_model/content/weighted_sum/Reshape_1", "op": "Reshape", "input": ["linear/linear_model/linear/linear_model/linear/linear_model/content/weighted_sum/SparseFillEmptyRows/SparseFillEmptyRows:2", "linear/linear_model/linear/linear_model/linear/linear_model/content/weighted_sum/Reshape_1/shape"], "attr": {"T": {"type": "DT_BOOL"}, "Tshape": {"type": "DT_INT32"}}}, {"name": "linear/linear_model/linear/linear_model/linear/linear_model/content/weighted_sum/embedding_lookup_sparse/strided_slice", "op": "StridedSlice", "input": ["linear/linear_model/linear/linear_model/linear/linear_model/content/weighted_sum/SparseFillEmptyRows/SparseFillEmptyRows", "linear/linear_model/linear/linear_model/linear/linear_model/content/weighted_sum/embedding_lookup_sparse/strided_slice/stack", "linear/linear_model/linear/linear_model/linear/linear_model/content/weighted_sum/embedding_lookup_sparse/strided_slice/stack_1", "linear/linear_model/linear/linear_model/linear/linear_model/content/weighted_sum/embedding_lookup_sparse/strided_slice/stack_2"], "attr": {"shrink_axis_mask": {"i": "2"}, "Index": {"type": "DT_INT32"}, "T": {"type": "DT_INT64"}, "end_mask": {"i": "1"}, "ellipsis_mask": {"i": "0"}, "begin_mask": {"i": "1"}, "new_axis_mask": {"i": "0"}}}, {"name": "dnn/input_from_feature_columns/input_layer/content_embedding_1/content_embedding_weights/stack", "op": "Pack", "input": ["dnn/input_from_feature_columns/input_layer/content_embedding_1/content_embedding_weights/stack/0", "dnn/input_from_feature_columns/input_layer/content_embedding_1/content_embedding_weights/strided_slice"], "attr": {"N": {"i": "2"}, "axis": {"i": "0"}, "T": {"type": "DT_INT32"}}}, {"name": "linear/linear_model/linear/linear_model/linear/linear_model/content/weighted_sum/embedding_lookup_sparse", "op": "SparseSegmentSum", "input": ["linear/linear_model/content/weights", "linear/linear_model/linear/linear_model/linear/linear_model/content/weighted_sum/SparseFillEmptyRows/SparseFillEmptyRows:1", "linear/linear_model/linear/linear_model/linear/linear_model/content/weighted_sum/embedding_lookup_sparse/strided_slice"], "attr": {"T": {"type": "DT_FLOAT"}, "Tsegmentids": {"type": "DT_INT64"}, "Tidx": {"type": "DT_INT64"}}}, {"name": "dnn/input_from_feature_columns/input_layer/content_embedding_1/content_embedding_weights/Tile", "op": "Tile", "input": ["dnn/input_from_feature_columns/input_layer/content_embedding_1/content_embedding_weights/Reshape_1", "dnn/input_from_feature_columns/input_layer/content_embedding_1/content_embedding_weights/stack"], "attr": {"T": {"type": "DT_BOOL"}, "Tmultiples": {"type": "DT_INT32"}}}, {"name": "linear/linear_model/linear/linear_model/linear/linear_model/content/weighted_sum/Shape", "op": "<PERSON><PERSON><PERSON>", "input": ["linear/linear_model/linear/linear_model/linear/linear_model/content/weighted_sum/embedding_lookup_sparse"], "attr": {"out_type": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}}}, {"name": "linear/linear_model/linear/linear_model/linear/linear_model/content/weighted_sum/zeros_like", "op": "ZerosLike", "input": ["linear/linear_model/linear/linear_model/linear/linear_model/content/weighted_sum/embedding_lookup_sparse"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "dnn/input_from_feature_columns/input_layer/content_embedding_1/content_embedding_weights", "op": "Select", "input": ["dnn/input_from_feature_columns/input_layer/content_embedding_1/content_embedding_weights/Tile", "dnn/input_from_feature_columns/input_layer/content_embedding_1/content_embedding_weights/zeros_like", "dnn/input_from_feature_columns/input_layer/content_embedding_1/content_embedding_weights/embedding_lookup_sparse"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "linear/linear_model/linear/linear_model/linear/linear_model/content/weighted_sum/strided_slice", "op": "StridedSlice", "input": ["linear/linear_model/linear/linear_model/linear/linear_model/content/weighted_sum/Shape", "linear/linear_model/linear/linear_model/linear/linear_model/content/weighted_sum/strided_slice/stack", "linear/linear_model/linear/linear_model/linear/linear_model/content/weighted_sum/strided_slice/stack_1", "linear/linear_model/linear/linear_model/linear/linear_model/content/weighted_sum/strided_slice/stack_2"], "attr": {"ellipsis_mask": {"i": "0"}, "begin_mask": {"i": "0"}, "new_axis_mask": {"i": "0"}, "end_mask": {"i": "0"}, "shrink_axis_mask": {"i": "1"}, "T": {"type": "DT_INT32"}, "Index": {"type": "DT_INT32"}}}, {"name": "dnn/hiddenlayer_0/Relu", "op": "_FusedMatMul", "input": ["dnn/input_from_feature_columns/input_layer/content_embedding_1/content_embedding_weights", "dnn/hiddenlayer_0/kernel", "dnn/hiddenlayer_0/bias"], "device": "/device:CPU:0", "attr": {"num_args": {"i": "1"}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UmVsdQ=="]}}, "T": {"type": "DT_FLOAT"}, "transpose_b": {"b": false}, "transpose_a": {"b": false}, "epsilon": {"f": 0.0}}}, {"name": "linear/linear_model/linear/linear_model/linear/linear_model/content/weighted_sum/stack", "op": "Pack", "input": ["linear/linear_model/linear/linear_model/linear/linear_model/content/weighted_sum/stack/0", "linear/linear_model/linear/linear_model/linear/linear_model/content/weighted_sum/strided_slice"], "attr": {"axis": {"i": "0"}, "T": {"type": "DT_INT32"}, "N": {"i": "2"}}}, {"name": "dnn/hiddenlayer_1/<PERSON>lu", "op": "_FusedMatMul", "input": ["dnn/hiddenlayer_0/Relu", "dnn/hiddenlayer_1/kernel", "dnn/hiddenlayer_1/bias"], "device": "/device:CPU:0", "attr": {"fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UmVsdQ=="]}}, "num_args": {"i": "1"}, "epsilon": {"f": 0.0}, "transpose_a": {"b": false}, "T": {"type": "DT_FLOAT"}, "transpose_b": {"b": false}}}, {"name": "linear/linear_model/linear/linear_model/linear/linear_model/content/weighted_sum/Tile", "op": "Tile", "input": ["linear/linear_model/linear/linear_model/linear/linear_model/content/weighted_sum/Reshape_1", "linear/linear_model/linear/linear_model/linear/linear_model/content/weighted_sum/stack"], "attr": {"Tmultiples": {"type": "DT_INT32"}, "T": {"type": "DT_BOOL"}}}, {"name": "dnn/logits/BiasAdd", "op": "_FusedMatMul", "input": ["dnn/hiddenlayer_1/<PERSON>lu", "dnn/logits/kernel", "dnn/logits/bias"], "device": "/device:CPU:0", "attr": {"num_args": {"i": "1"}, "transpose_b": {"b": false}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "epsilon": {"f": 0.0}, "transpose_a": {"b": false}, "T": {"type": "DT_FLOAT"}}}, {"name": "linear/linear_model/linear/linear_model/linear/linear_model/content/weighted_sum", "op": "Select", "input": ["linear/linear_model/linear/linear_model/linear/linear_model/content/weighted_sum/Tile", "linear/linear_model/linear/linear_model/linear/linear_model/content/weighted_sum/zeros_like", "linear/linear_model/linear/linear_model/linear/linear_model/content/weighted_sum/embedding_lookup_sparse"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "linear/linear_model/linear/linear_model/linear/linear_model/content/weighted_sum/Shape_1", "op": "<PERSON><PERSON><PERSON>", "input": ["linear/linear_model/linear/linear_model/linear/linear_model/content/weighted_sum"], "attr": {"out_type": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}}}, {"name": "linear/linear_model/linear/linear_model/linear/linear_model/content/weighted_sum/Slice_2", "op": "Slice", "input": ["linear/linear_model/linear/linear_model/linear/linear_model/content/weighted_sum/Shape_1", "linear/linear_model/linear/linear_model/linear/linear_model/content/weighted_sum/Slice_2/begin", "linear/linear_model/linear/linear_model/linear/linear_model/content/weighted_sum/Slice_2/size"], "attr": {"Index": {"type": "DT_INT32"}, "T": {"type": "DT_INT32"}}}, {"name": "linear/linear_model/linear/linear_model/linear/linear_model/content/weighted_sum/concat", "op": "ConcatV2", "input": ["linear/linear_model/linear/linear_model/linear/linear_model/content/weighted_sum/Slice_1", "linear/linear_model/linear/linear_model/linear/linear_model/content/weighted_sum/Slice_2", "linear/linear_model/linear/linear_model/linear/linear_model/content/weighted_sum/concat/axis"], "attr": {"N": {"i": "2"}, "T": {"type": "DT_INT32"}, "Tidx": {"type": "DT_INT32"}}}, {"name": "linear/linear_model/linear/linear_model/linear/linear_model/content/weighted_sum/Reshape_2", "op": "Reshape", "input": ["linear/linear_model/linear/linear_model/linear/linear_model/content/weighted_sum", "linear/linear_model/linear/linear_model/linear/linear_model/content/weighted_sum/concat"], "attr": {"Tshape": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}}}, {"name": "linear/linear_model/linear/linear_model/linear/linear_model/weighted_sum", "op": "BiasAdd", "input": ["linear/linear_model/linear/linear_model/linear/linear_model/content/weighted_sum/Reshape_2", "linear/linear_model/bias_weights"], "attr": {"T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}}}, {"name": "add", "op": "AddV2", "input": ["dnn/logits/BiasAdd", "linear/linear_model/linear/linear_model/linear/linear_model/weighted_sum"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "head/predictions/probabilities", "op": "Softmax", "input": ["add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "head/Shape", "op": "<PERSON><PERSON><PERSON>", "input": ["head/predictions/probabilities"], "attr": {"T": {"type": "DT_FLOAT"}, "out_type": {"type": "DT_INT32"}}}, {"name": "head/strided_slice", "op": "StridedSlice", "input": ["head/Shape", "head/strided_slice/stack", "head/strided_slice/stack_1", "head/strided_slice/stack_2"], "attr": {"ellipsis_mask": {"i": "0"}, "begin_mask": {"i": "0"}, "end_mask": {"i": "0"}, "T": {"type": "DT_INT32"}, "shrink_axis_mask": {"i": "1"}, "new_axis_mask": {"i": "0"}, "Index": {"type": "DT_INT32"}}}, {"name": "head/Tile/multiples", "op": "Pack", "input": ["head/strided_slice", "head/Tile/multiples/1"], "attr": {"T": {"type": "DT_INT32"}, "N": {"i": "2"}, "axis": {"i": "0"}}}, {"name": "head/Tile", "op": "Tile", "input": ["head/ExpandDims", "head/Tile/multiples"], "attr": {"Tmultiples": {"type": "DT_INT32"}, "T": {"type": "DT_STRING"}}}], "library": {"function": [{"signature": {"name": "map_while_body_49115", "inputArg": [{"name": "map_while_loop_counter", "type": "DT_INT32"}, {"name": "map_strided_slice_0", "type": "DT_INT32"}, {"name": "placeholder", "type": "DT_INT32"}, {"name": "placeholder_1", "type": "DT_VARIANT"}, {"name": "map_strided_slice_1_0", "type": "DT_INT32"}, {"name": "tensorarrayv2read_tensorlistgetitem_map_tensorarrayunstack_tensorlistfromtensor_0", "type": "DT_VARIANT"}], "outputArg": [{"name": "add_1", "type": "DT_INT32"}, {"name": "map_strided_slice", "type": "DT_INT32"}, {"name": "add", "type": "DT_INT32"}, {"name": "tensorarrayv2write_tensorlistsetitem", "type": "DT_VARIANT"}, {"name": "map_strided_slice_1", "type": "DT_INT32"}, {"name": "tensorarrayv2read_tensorlistgetitem_map_tensorarrayunstack_tensorlistfromtensor", "type": "DT_VARIANT"}]}, "nodeDef": [{"name": "map_while_body_49115/add_1/y", "op": "Const", "attr": {"_output_shapes": {"list": {"shape": [{}]}}, "dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {}}}}}, {"name": "map_while_body_49115/add/y", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {}}}, "_output_shapes": {"list": {"shape": [{}]}}}}, {"name": "map_while_body_49115/StringNGrams/RaggedGetItem/strided_slice/stack", "op": "Const", "attr": {"_output_shapes": {"list": {"shape": [{"dim": [{"size": "1"}]}]}}, "dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "1"}]}}}}}, {"name": "map_while_body_49115/StringNGrams/RaggedGetItem/strided_slice/stack_1", "op": "Const", "attr": {"_output_shapes": {"list": {"shape": [{"dim": [{"size": "1"}]}]}}, "dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "1"}]}}}}}, {"name": "map_while_body_49115/StringNGrams/RaggedGetItem/strided_slice/stack_2", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "1"}]}}}, "dtype": {"type": "DT_INT32"}, "_output_shapes": {"list": {"shape": [{"dim": [{"size": "1"}]}]}}}}, {"name": "map_while_body_49115/StringNGrams/RaggedGetItem/strided_slice_2/stack", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "1"}]}}}, "_output_shapes": {"list": {"shape": [{"dim": [{"size": "1"}]}]}}, "dtype": {"type": "DT_INT32"}}}, {"name": "map_while_body_49115/StringNGrams/RaggedGetItem/strided_slice_2/stack_1", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "1"}]}}}, "_output_shapes": {"list": {"shape": [{"dim": [{"size": "1"}]}]}}, "dtype": {"type": "DT_INT32"}}}, {"name": "map_while_body_49115/StringNGrams/RaggedGetItem/strided_slice_2/stack_2", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "1"}]}}}, "dtype": {"type": "DT_INT32"}, "_output_shapes": {"list": {"shape": [{"dim": [{"size": "1"}]}]}}}}, {"name": "map_while_body_49115/StringNGrams/RaggedFromRowStarts/RowPartitionFromRowStarts/row_starts", "op": "Const", "attr": {"dtype": {"type": "DT_INT64"}, "_output_shapes": {"list": {"shape": [{"dim": [{"size": "1"}]}]}}, "value": {"tensor": {"dtype": "DT_INT64", "tensorShape": {"dim": [{"size": "1"}]}}}}}, {"name": "map_while_body_49115/StringsByteSplit/RaggedGetItem/strided_slice/stack", "op": "Const", "attr": {"_output_shapes": {"list": {"shape": [{"dim": [{"size": "1"}]}]}}, "dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "1"}]}}}}}, {"name": "map_while_body_49115/StringsByteSplit/RaggedGetItem/strided_slice/stack_1", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "1"}]}}}, "_output_shapes": {"list": {"shape": [{"dim": [{"size": "1"}]}]}}}}, {"name": "map_while_body_49115/StringsByteSplit/RaggedGetItem/strided_slice/stack_2", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "1"}]}}}, "_output_shapes": {"list": {"shape": [{"dim": [{"size": "1"}]}]}}}}, {"name": "map_while_body_49115/StringsByteSplit/RaggedGetItem/strided_slice_2/stack", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "1"}]}}}, "_output_shapes": {"list": {"shape": [{"dim": [{"size": "1"}]}]}}, "dtype": {"type": "DT_INT32"}}}, {"name": "map_while_body_49115/StringsByteSplit/RaggedGetItem/strided_slice_2/stack_1", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "_output_shapes": {"list": {"shape": [{"dim": [{"size": "1"}]}]}}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "1"}]}}}}}, {"name": "map_while_body_49115/StringsByteSplit/RaggedGetItem/strided_slice_2/stack_2", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "1"}]}}}, "_output_shapes": {"list": {"shape": [{"dim": [{"size": "1"}]}]}}}}, {"name": "map_while_body_49115/StringsByteSplit/StringsByteSplit/RaggedFromValueRowIds/RowPartitionFromValueRowIds/concat/values_0", "op": "Const", "attr": {"dtype": {"type": "DT_INT64"}, "value": {"tensor": {"dtype": "DT_INT64", "tensorShape": {"dim": [{"size": "1"}]}}}, "_output_shapes": {"list": {"shape": [{"dim": [{"size": "1"}]}]}}}}, {"name": "map_while_body_49115/StringsByteSplit/StringsByteSplit/strided_slice_1/stack", "op": "Const", "attr": {"_output_shapes": {"list": {"shape": [{"dim": [{"size": "1"}]}]}}, "dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "1"}]}}}}}, {"name": "map_while_body_49115/StringsByteSplit/StringsByteSplit/strided_slice_1/stack_1", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "1"}]}}}, "_output_shapes": {"list": {"shape": [{"dim": [{"size": "1"}]}]}}}}, {"name": "map_while_body_49115/StringsByteSplit/StringsByteSplit/strided_slice_1/stack_2", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "1"}]}}}, "_output_shapes": {"list": {"shape": [{"dim": [{"size": "1"}]}]}}, "dtype": {"type": "DT_INT32"}}}, {"name": "map_while_body_49115/StringsByteSplit/StringsByteSplit/RaggedFromValueRowIds/RowPartitionFromValueRowIds/bincount/Const", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "_output_shapes": {"list": {"shape": [{"dim": [{"size": "1"}]}]}}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "1"}]}}}}}, {"name": "map_while_body_49115/StringsByteSplit/StringsByteSplit/RaggedFromValueRowIds/RowPartitionFromValueRowIds/bincount/Greater/y", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {}}}, "dtype": {"type": "DT_INT32"}, "_output_shapes": {"list": {"shape": [{}]}}}}, {"name": "map_while_body_49115/TensorArrayV2Read/Const", "op": "Const", "attr": {"_output_shapes": {"list": {"shape": [{"dim": [{}]}]}}, "dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{}]}}}}}, {"name": "map_while_body_49115/StringsByteSplit/StringsByteSplit/StringSplit/delimiter", "op": "Const", "attr": {"dtype": {"type": "DT_STRING"}, "value": {"tensor": {"dtype": "DT_STRING", "tensorShape": {}}}, "_output_shapes": {"list": {"shape": [{}]}}}}, {"name": "map_while_body_49115/StringsByteSplit/StringsByteSplit/strided_slice/stack", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "2"}]}}}, "_output_shapes": {"list": {"shape": [{"dim": [{"size": "2"}]}]}}}}, {"name": "map_while_body_49115/StringsByteSplit/StringsByteSplit/strided_slice/stack_1", "op": "Const", "attr": {"_output_shapes": {"list": {"shape": [{"dim": [{"size": "2"}]}]}}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "2"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "map_while_body_49115/StringsByteSplit/StringsByteSplit/strided_slice/stack_2", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "_output_shapes": {"list": {"shape": [{"dim": [{"size": "2"}]}]}}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "2"}]}}}}}, {"name": "map_while_body_49115/StringsByteSplit/StringsByteSplit/RaggedFromValueRowIds/RowPartitionFromValueRowIds/bincount/Const_1", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "1"}]}}}, "dtype": {"type": "DT_INT32"}, "_output_shapes": {"list": {"shape": [{"dim": [{"size": "1"}]}]}}}}, {"name": "map_while_body_49115/StringsByteSplit/StringsByteSplit/RaggedFromValueRowIds/RowPartitionFromValueRowIds/bincount/add/y", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {}}}, "dtype": {"type": "DT_INT32"}, "_output_shapes": {"list": {"shape": [{}]}}}}, {"name": "map_while_body_49115/StringsByteSplit/StringsByteSplit/RaggedFromValueRowIds/RowPartitionFromValueRowIds/bincount/Const_2", "op": "Const", "attr": {"dtype": {"type": "DT_INT64"}, "_output_shapes": {"list": {"shape": [{"dim": [{}]}]}}, "value": {"tensor": {"dtype": "DT_INT64", "tensorShape": {"dim": [{}]}}}}}, {"name": "map_while_body_49115/StringsByteSplit/StringsByteSplit/RaggedFromValueRowIds/RowPartitionFromValueRowIds/Cumsum/axis", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {}}}, "_output_shapes": {"list": {"shape": [{}]}}}}, {"name": "map_while_body_49115/StringsByteSplit/StringsByteSplit/RaggedFromValueRowIds/RowPartitionFromValueRowIds/concat/axis", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {}}}, "dtype": {"type": "DT_INT32"}, "_output_shapes": {"list": {"shape": [{}]}}}}, {"name": "map_while_body_49115/StringsByteSplit/RaggedGetItem/strided_slice_1/stack", "op": "Const", "attr": {"_output_shapes": {"list": {"shape": [{"dim": [{"size": "1"}]}]}}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "1"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "map_while_body_49115/StringsByteSplit/RaggedGetItem/strided_slice_1/stack_1", "op": "Const", "attr": {"_output_shapes": {"list": {"shape": [{"dim": [{"size": "1"}]}]}}, "dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "1"}]}}}}}, {"name": "map_while_body_49115/StringsByteSplit/RaggedGetItem/strided_slice_1/stack_2", "op": "Const", "attr": {"_output_shapes": {"list": {"shape": [{"dim": [{"size": "1"}]}]}}, "dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "1"}]}}}}}, {"name": "map_while_body_49115/StringsByteSplit/RaggedGetItem/strided_slice_3/stack", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "1"}]}}}, "dtype": {"type": "DT_INT32"}, "_output_shapes": {"list": {"shape": [{"dim": [{"size": "1"}]}]}}}}, {"name": "map_while_body_49115/StringsByteSplit/RaggedGetItem/strided_slice_3/stack_1", "op": "Const", "attr": {"_output_shapes": {"list": {"shape": [{"dim": [{"size": "1"}]}]}}, "dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "1"}]}}}}}, {"name": "map_while_body_49115/StringsByteSplit/RaggedGetItem/strided_slice_3/stack_2", "op": "Const", "attr": {"_output_shapes": {"list": {"shape": [{"dim": [{"size": "1"}]}]}}, "dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "1"}]}}}}}, {"name": "map_while_body_49115/StringsByteSplit/RaggedGetItem/strided_slice_4/Cast", "op": "Const", "attr": {"dtype": {"type": "DT_INT64"}, "value": {"tensor": {"dtype": "DT_INT64", "tensorShape": {"dim": [{"size": "1"}]}}}}}, {"name": "map_while_body_49115/StringsByteSplit/RaggedGetItem/strided_slice_5/Const", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "_output_shapes": {"list": {"shape": [{"dim": [{}]}]}}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{}]}}}}}, {"name": "map_while_body_49115/StringNGrams/RaggedFromRowStarts/strided_slice/stack", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "1"}]}}}, "dtype": {"type": "DT_INT32"}, "_output_shapes": {"list": {"shape": [{"dim": [{"size": "1"}]}]}}}}, {"name": "map_while_body_49115/StringNGrams/RaggedFromRowStarts/strided_slice/stack_1", "op": "Const", "attr": {"_output_shapes": {"list": {"shape": [{"dim": [{"size": "1"}]}]}}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "1"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "map_while_body_49115/StringNGrams/RaggedFromRowStarts/strided_slice/stack_2", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "1"}]}}}, "_output_shapes": {"list": {"shape": [{"dim": [{"size": "1"}]}]}}}}, {"name": "map_while_body_49115/StringNGrams/RaggedFromRowStarts/RowPartitionFromRowStarts/concat/axis", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {}}}, "dtype": {"type": "DT_INT32"}, "_output_shapes": {"list": {"shape": [{}]}}}}, {"name": "map_while_body_49115/StringNGrams/RaggedGetItem/strided_slice_1/stack", "op": "Const", "attr": {"_output_shapes": {"list": {"shape": [{"dim": [{"size": "1"}]}]}}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "1"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "map_while_body_49115/StringNGrams/RaggedGetItem/strided_slice_1/stack_1", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "_output_shapes": {"list": {"shape": [{"dim": [{"size": "1"}]}]}}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "1"}]}}}}}, {"name": "map_while_body_49115/StringNGrams/RaggedGetItem/strided_slice_1/stack_2", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "_output_shapes": {"list": {"shape": [{"dim": [{"size": "1"}]}]}}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "1"}]}}}}}, {"name": "map_while_body_49115/StringNGrams/RaggedGetItem/strided_slice_3/stack", "op": "Const", "attr": {"_output_shapes": {"list": {"shape": [{"dim": [{"size": "1"}]}]}}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "1"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "map_while_body_49115/StringNGrams/RaggedGetItem/strided_slice_3/stack_1", "op": "Const", "attr": {"_output_shapes": {"list": {"shape": [{"dim": [{"size": "1"}]}]}}, "dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "1"}]}}}}}, {"name": "map_while_body_49115/StringNGrams/RaggedGetItem/strided_slice_3/stack_2", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "1"}]}}}, "_output_shapes": {"list": {"shape": [{"dim": [{"size": "1"}]}]}}}}, {"name": "map_while_body_49115/StringNGrams/RaggedGetItem/strided_slice_4/Cast", "op": "Const", "attr": {"dtype": {"type": "DT_INT64"}, "value": {"tensor": {"dtype": "DT_INT64", "tensorShape": {"dim": [{"size": "1"}]}}}}}, {"name": "map_while_body_49115/StringNGrams/RaggedGetItem/strided_slice_5/Const", "op": "Const", "attr": {"_output_shapes": {"list": {"shape": [{"dim": [{}]}]}}, "dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{}]}}}}}, {"name": "map_while_body_49115/Const", "op": "Const", "attr": {"dtype": {"type": "DT_STRING"}, "_output_shapes": {"list": {"shape": [{"dim": [{"size": "10000"}]}]}}, "value": {"tensor": {"dtype": "DT_STRING", "tensorShape": {"dim": [{"size": "10000"}]}}}}}, {"name": "map_while_body_49115/concat/axis", "op": "Const", "attr": {"_output_shapes": {"list": {"shape": [{}]}}, "dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {}}}}}, {"name": "map_while_body_49115/strided_slice/stack", "op": "Const", "attr": {"_output_shapes": {"list": {"shape": [{"dim": [{"size": "1"}]}]}}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "1"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "map_while_body_49115/strided_slice/stack_1", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "1"}]}}}, "dtype": {"type": "DT_INT32"}, "_output_shapes": {"list": {"shape": [{"dim": [{"size": "1"}]}]}}}}, {"name": "map_while_body_49115/strided_slice/stack_2", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "_output_shapes": {"list": {"shape": [{"dim": [{"size": "1"}]}]}}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "1"}]}}}}}, {"name": "add_1_0", "op": "AddV2", "input": ["map_while_loop_counter", "map_while_body_49115/add_1/y:output:0"], "attr": {"T": {"type": "DT_INT32"}}}, {"name": "add_0", "op": "AddV2", "input": ["placeholder", "map_while_body_49115/add/y:output:0"], "attr": {"T": {"type": "DT_INT32"}}}, {"name": "TensorArrayV2Read/TensorListGetItem", "op": "TensorListGetItem", "input": ["tensorarrayv2read_tensorlistgetitem_map_tensorarrayunstack_tensorlistfromtensor_0", "placeholder", "map_while_body_49115/TensorArrayV2Read/Const:output:0"], "attr": {"_output_shapes": {"list": {"shape": [{}]}}, "element_dtype": {"type": "DT_STRING"}}}, {"name": "map_while_body_49115/ConstantFolding/StringsByteSplit/stack_const_axis", "op": "Const", "input": ["^TensorArrayV2Read/TensorListGetItem"], "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {}}}}}, {"name": "StringsByteSplit/stack", "op": "ExpandDims", "input": ["TensorArrayV2Read/TensorListGetItem:item:0", "map_while_body_49115/ConstantFolding/StringsByteSplit/stack_const_axis:output:0"], "attr": {"_output_shapes": {"list": {"shape": [{"dim": [{"size": "1"}]}]}}, "Tdim": {"type": "DT_INT32"}, "T": {"type": "DT_STRING"}}}, {"name": "StringsByteSplit/StringsByteSplit/StringSplit", "op": "StringSplit", "input": ["StringsByteSplit/stack:output:0", "map_while_body_49115/StringsByteSplit/StringsByteSplit/StringSplit/delimiter:output:0"], "attr": {"_output_shapes": {"list": {"shape": [{"dim": [{"size": "-1"}, {"size": "2"}]}, {"dim": [{"size": "-1"}]}, {"dim": [{"size": "2"}]}]}}, "skip_empty": {"b": false}}}, {"name": "StringsByteSplit/StringsByteSplit/strided_slice_1", "op": "StridedSlice", "input": ["StringsByteSplit/StringsByteSplit/StringSplit:shape:0", "map_while_body_49115/StringsByteSplit/StringsByteSplit/strided_slice_1/stack:output:0", "map_while_body_49115/StringsByteSplit/StringsByteSplit/strided_slice_1/stack_1:output:0", "map_while_body_49115/StringsByteSplit/StringsByteSplit/strided_slice_1/stack_2:output:0"], "attr": {"begin_mask": {"i": "0"}, "end_mask": {"i": "0"}, "Index": {"type": "DT_INT32"}, "new_axis_mask": {"i": "0"}, "ellipsis_mask": {"i": "0"}, "shrink_axis_mask": {"i": "1"}, "T": {"type": "DT_INT64"}, "_output_shapes": {"list": {"shape": [{}]}}}}, {"name": "StringsByteSplit/StringsByteSplit/strided_slice", "op": "StridedSlice", "input": ["StringsByteSplit/StringsByteSplit/StringSplit:indices:0", "map_while_body_49115/StringsByteSplit/StringsByteSplit/strided_slice/stack:output:0", "map_while_body_49115/StringsByteSplit/StringsByteSplit/strided_slice/stack_1:output:0", "map_while_body_49115/StringsByteSplit/StringsByteSplit/strided_slice/stack_2:output:0"], "attr": {"begin_mask": {"i": "1"}, "shrink_axis_mask": {"i": "2"}, "Index": {"type": "DT_INT32"}, "T": {"type": "DT_INT64"}, "new_axis_mask": {"i": "0"}, "end_mask": {"i": "1"}, "ellipsis_mask": {"i": "0"}, "_output_shapes": {"list": {"shape": [{"dim": [{"size": "-1"}]}]}}}}, {"name": "StringsByteSplit/StringsByteSplit/RaggedFromValueRowIds/RowPartitionFromValueRowIds/Cast_1", "op": "Cast", "input": ["StringsByteSplit/StringsByteSplit/strided_slice_1:output:0"], "attr": {"Truncate": {"b": false}, "SrcT": {"type": "DT_INT64"}, "_output_shapes": {"list": {"shape": [{}]}}, "DstT": {"type": "DT_INT32"}}}, {"name": "StringsByteSplit/StringsByteSplit/RaggedFromValueRowIds/RowPartitionFromValueRowIds/Cast", "op": "Cast", "input": ["StringsByteSplit/StringsByteSplit/strided_slice:output:0"], "attr": {"Truncate": {"b": false}, "DstT": {"type": "DT_INT32"}, "_output_shapes": {"list": {"shape": [{"dim": [{"size": "-1"}]}]}}, "SrcT": {"type": "DT_INT64"}}}, {"name": "StringsByteSplit/StringsByteSplit/RaggedFromValueRowIds/RowPartitionFromValueRowIds/bincount/Shape", "op": "<PERSON><PERSON><PERSON>", "input": ["StringsByteSplit/StringsByteSplit/RaggedFromValueRowIds/RowPartitionFromValueRowIds/Cast:y:0"], "attr": {"_output_shapes": {"list": {"shape": [{"dim": [{"size": "1"}]}]}}, "T": {"type": "DT_INT32"}, "out_type": {"type": "DT_INT32"}}}, {"name": "StringsByteSplit/StringsByteSplit/RaggedFromValueRowIds/RowPartitionFromValueRowIds/bincount/Max", "op": "Max", "input": ["StringsByteSplit/StringsByteSplit/RaggedFromValueRowIds/RowPartitionFromValueRowIds/Cast:y:0", "map_while_body_49115/StringsByteSplit/StringsByteSplit/RaggedFromValueRowIds/RowPartitionFromValueRowIds/bincount/Const_1:output:0"], "attr": {"Tidx": {"type": "DT_INT32"}, "keep_dims": {"b": false}, "T": {"type": "DT_INT32"}, "_output_shapes": {"list": {"shape": [{}]}}}}, {"name": "StringsByteSplit/StringsByteSplit/RaggedFromValueRowIds/RowPartitionFromValueRowIds/bincount/Prod", "op": "Prod", "input": ["StringsByteSplit/StringsByteSplit/RaggedFromValueRowIds/RowPartitionFromValueRowIds/bincount/Shape:output:0", "map_while_body_49115/StringsByteSplit/StringsByteSplit/RaggedFromValueRowIds/RowPartitionFromValueRowIds/bincount/Const:output:0"], "attr": {"T": {"type": "DT_INT32"}, "Tidx": {"type": "DT_INT32"}, "_output_shapes": {"list": {"shape": [{}]}}, "keep_dims": {"b": false}}}, {"name": "StringsByteSplit/StringsByteSplit/RaggedFromValueRowIds/RowPartitionFromValueRowIds/bincount/add", "op": "AddV2", "input": ["StringsByteSplit/StringsByteSplit/RaggedFromValueRowIds/RowPartitionFromValueRowIds/bincount/Max:output:0", "map_while_body_49115/StringsByteSplit/StringsByteSplit/RaggedFromValueRowIds/RowPartitionFromValueRowIds/bincount/add/y:output:0"], "attr": {"T": {"type": "DT_INT32"}, "_output_shapes": {"list": {"shape": [{}]}}}}, {"name": "StringsByteSplit/StringsByteSplit/RaggedFromValueRowIds/RowPartitionFromValueRowIds/bincount/Greater", "op": "Greater", "input": ["StringsByteSplit/StringsByteSplit/RaggedFromValueRowIds/RowPartitionFromValueRowIds/bincount/Prod:output:0", "map_while_body_49115/StringsByteSplit/StringsByteSplit/RaggedFromValueRowIds/RowPartitionFromValueRowIds/bincount/Greater/y:output:0"], "attr": {"T": {"type": "DT_INT32"}, "_output_shapes": {"list": {"shape": [{}]}}}}, {"name": "StringsByteSplit/StringsByteSplit/RaggedFromValueRowIds/RowPartitionFromValueRowIds/bincount/Cast", "op": "Cast", "input": ["StringsByteSplit/StringsByteSplit/RaggedFromValueRowIds/RowPartitionFromValueRowIds/bincount/Greater:z:0"], "attr": {"SrcT": {"type": "DT_BOOL"}, "DstT": {"type": "DT_INT32"}, "Truncate": {"b": false}, "_output_shapes": {"list": {"shape": [{}]}}}}, {"name": "StringsByteSplit/StringsByteSplit/RaggedFromValueRowIds/RowPartitionFromValueRowIds/bincount/mul", "op": "<PERSON><PERSON>", "input": ["StringsByteSplit/StringsByteSplit/RaggedFromValueRowIds/RowPartitionFromValueRowIds/bincount/Cast:y:0", "StringsByteSplit/StringsByteSplit/RaggedFromValueRowIds/RowPartitionFromValueRowIds/bincount/add:z:0"], "attr": {"_output_shapes": {"list": {"shape": [{}]}}, "T": {"type": "DT_INT32"}}}, {"name": "StringsByteSplit/StringsByteSplit/RaggedFromValueRowIds/RowPartitionFromValueRowIds/bincount/Maximum", "op": "Maximum", "input": ["StringsByteSplit/StringsByteSplit/RaggedFromValueRowIds/RowPartitionFromValueRowIds/Cast_1:y:0", "StringsByteSplit/StringsByteSplit/RaggedFromValueRowIds/RowPartitionFromValueRowIds/bincount/mul:z:0"], "attr": {"_output_shapes": {"list": {"shape": [{}]}}, "T": {"type": "DT_INT32"}}}, {"name": "StringsByteSplit/StringsByteSplit/RaggedFromValueRowIds/RowPartitionFromValueRowIds/bincount/Minimum", "op": "Minimum", "input": ["StringsByteSplit/StringsByteSplit/RaggedFromValueRowIds/RowPartitionFromValueRowIds/Cast_1:y:0", "StringsByteSplit/StringsByteSplit/RaggedFromValueRowIds/RowPartitionFromValueRowIds/bincount/Maximum:z:0"], "attr": {"T": {"type": "DT_INT32"}, "_output_shapes": {"list": {"shape": [{}]}}}}, {"name": "StringsByteSplit/StringsByteSplit/RaggedFromValueRowIds/RowPartitionFromValueRowIds/bincount/Bincount", "op": "Bincount", "input": ["StringsByteSplit/StringsByteSplit/RaggedFromValueRowIds/RowPartitionFromValueRowIds/Cast:y:0", "StringsByteSplit/StringsByteSplit/RaggedFromValueRowIds/RowPartitionFromValueRowIds/bincount/Minimum:z:0", "map_while_body_49115/StringsByteSplit/StringsByteSplit/RaggedFromValueRowIds/RowPartitionFromValueRowIds/bincount/Const_2:output:0"], "attr": {"_output_shapes": {"list": {"shape": [{"dim": [{"size": "-1"}]}]}}, "T": {"type": "DT_INT64"}}}, {"name": "StringsByteSplit/StringsByteSplit/RaggedFromValueRowIds/RowPartitionFromValueRowIds/Cumsum", "op": "Cumsum", "input": ["StringsByteSplit/StringsByteSplit/RaggedFromValueRowIds/RowPartitionFromValueRowIds/bincount/Bincount:bins:0", "map_while_body_49115/StringsByteSplit/StringsByteSplit/RaggedFromValueRowIds/RowPartitionFromValueRowIds/Cumsum/axis:output:0"], "attr": {"reverse": {"b": false}, "_output_shapes": {"list": {"shape": [{"dim": [{"size": "-1"}]}]}}, "exclusive": {"b": false}, "Tidx": {"type": "DT_INT32"}, "T": {"type": "DT_INT64"}}}, {"name": "StringsByteSplit/StringsByteSplit/RaggedFromValueRowIds/RowPartitionFromValueRowIds/concat", "op": "ConcatV2", "input": ["map_while_body_49115/StringsByteSplit/StringsByteSplit/RaggedFromValueRowIds/RowPartitionFromValueRowIds/concat/values_0:output:0", "StringsByteSplit/StringsByteSplit/RaggedFromValueRowIds/RowPartitionFromValueRowIds/Cumsum:out:0", "map_while_body_49115/StringsByteSplit/StringsByteSplit/RaggedFromValueRowIds/RowPartitionFromValueRowIds/concat/axis:output:0"], "attr": {"N": {"i": "2"}, "Tidx": {"type": "DT_INT32"}, "_output_shapes": {"list": {"shape": [{"dim": [{"size": "-1"}]}]}}, "T": {"type": "DT_INT64"}}}, {"name": "StringsByteSplit/RaggedGetItem/strided_slice", "op": "StridedSlice", "input": ["StringsByteSplit/StringsByteSplit/RaggedFromValueRowIds/RowPartitionFromValueRowIds/concat:output:0", "map_while_body_49115/StringsByteSplit/RaggedGetItem/strided_slice/stack:output:0", "map_while_body_49115/StringsByteSplit/RaggedGetItem/strided_slice/stack_1:output:0", "map_while_body_49115/StringsByteSplit/RaggedGetItem/strided_slice/stack_2:output:0"], "attr": {"shrink_axis_mask": {"i": "0"}, "new_axis_mask": {"i": "0"}, "begin_mask": {"i": "1"}, "_output_shapes": {"list": {"shape": [{"dim": [{"size": "-1"}]}]}}, "ellipsis_mask": {"i": "0"}, "end_mask": {"i": "0"}, "Index": {"type": "DT_INT32"}, "T": {"type": "DT_INT64"}}}, {"name": "StringsByteSplit/RaggedGetItem/strided_slice_1", "op": "StridedSlice", "input": ["StringsByteSplit/StringsByteSplit/RaggedFromValueRowIds/RowPartitionFromValueRowIds/concat:output:0", "map_while_body_49115/StringsByteSplit/RaggedGetItem/strided_slice_1/stack:output:0", "map_while_body_49115/StringsByteSplit/RaggedGetItem/strided_slice_1/stack_1:output:0", "map_while_body_49115/StringsByteSplit/RaggedGetItem/strided_slice_1/stack_2:output:0"], "attr": {"new_axis_mask": {"i": "0"}, "Index": {"type": "DT_INT32"}, "end_mask": {"i": "1"}, "shrink_axis_mask": {"i": "0"}, "ellipsis_mask": {"i": "0"}, "begin_mask": {"i": "0"}, "_output_shapes": {"list": {"shape": [{"dim": [{"size": "-1"}]}]}}, "T": {"type": "DT_INT64"}}}, {"name": "StringsByteSplit/RaggedGetItem/strided_slice_2", "op": "StridedSlice", "input": ["StringsByteSplit/RaggedGetItem/strided_slice:output:0", "map_while_body_49115/StringsByteSplit/RaggedGetItem/strided_slice_2/stack:output:0", "map_while_body_49115/StringsByteSplit/RaggedGetItem/strided_slice_2/stack_1:output:0", "map_while_body_49115/StringsByteSplit/RaggedGetItem/strided_slice_2/stack_2:output:0"], "attr": {"shrink_axis_mask": {"i": "1"}, "T": {"type": "DT_INT64"}, "new_axis_mask": {"i": "0"}, "begin_mask": {"i": "0"}, "_output_shapes": {"list": {"shape": [{}]}}, "end_mask": {"i": "0"}, "ellipsis_mask": {"i": "0"}, "Index": {"type": "DT_INT32"}}}, {"name": "StringsByteSplit/RaggedGetItem/strided_slice_3", "op": "StridedSlice", "input": ["StringsByteSplit/RaggedGetItem/strided_slice_1:output:0", "map_while_body_49115/StringsByteSplit/RaggedGetItem/strided_slice_3/stack:output:0", "map_while_body_49115/StringsByteSplit/RaggedGetItem/strided_slice_3/stack_1:output:0", "map_while_body_49115/StringsByteSplit/RaggedGetItem/strided_slice_3/stack_2:output:0"], "attr": {"new_axis_mask": {"i": "0"}, "end_mask": {"i": "0"}, "begin_mask": {"i": "0"}, "shrink_axis_mask": {"i": "1"}, "Index": {"type": "DT_INT32"}, "T": {"type": "DT_INT64"}, "_output_shapes": {"list": {"shape": [{}]}}, "ellipsis_mask": {"i": "0"}}}, {"name": "map_while_body_49115/ConstantFolding/StringsByteSplit/RaggedGetItem/strided_slice_4/stack_const_axis", "op": "Const", "input": ["^StringsByteSplit/RaggedGetItem/strided_slice_2"], "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "map_while_body_49115/ConstantFolding/StringsByteSplit/RaggedGetItem/strided_slice_4/stack_1_const_axis", "op": "Const", "input": ["^StringsByteSplit/RaggedGetItem/strided_slice_3"], "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "StringsByteSplit/RaggedGetItem/strided_slice_4/stack", "op": "ExpandDims", "input": ["StringsByteSplit/RaggedGetItem/strided_slice_2:output:0", "map_while_body_49115/ConstantFolding/StringsByteSplit/RaggedGetItem/strided_slice_4/stack_const_axis:output:0"], "attr": {"T": {"type": "DT_INT64"}, "_output_shapes": {"list": {"shape": [{"dim": [{"size": "1"}]}]}}, "Tdim": {"type": "DT_INT32"}}}, {"name": "StringsByteSplit/RaggedGetItem/strided_slice_4/stack_1", "op": "ExpandDims", "input": ["StringsByteSplit/RaggedGetItem/strided_slice_3:output:0", "map_while_body_49115/ConstantFolding/StringsByteSplit/RaggedGetItem/strided_slice_4/stack_1_const_axis:output:0"], "attr": {"Tdim": {"type": "DT_INT32"}, "T": {"type": "DT_INT64"}, "_output_shapes": {"list": {"shape": [{"dim": [{"size": "1"}]}]}}}}, {"name": "StringsByteSplit/RaggedGetItem/strided_slice_4", "op": "StridedSlice", "input": ["StringsByteSplit/StringsByteSplit/StringSplit:values:0", "StringsByteSplit/RaggedGetItem/strided_slice_4/stack:output:0", "StringsByteSplit/RaggedGetItem/strided_slice_4/stack_1:output:0", "map_while_body_49115/StringsByteSplit/RaggedGetItem/strided_slice_4/Cast:output:0"], "attr": {"begin_mask": {"i": "0"}, "shrink_axis_mask": {"i": "0"}, "ellipsis_mask": {"i": "0"}, "new_axis_mask": {"i": "0"}, "Index": {"type": "DT_INT64"}, "_output_shapes": {"list": {"shape": [{"dim": [{"size": "-1"}]}]}}, "T": {"type": "DT_STRING"}, "end_mask": {"i": "0"}}}, {"name": "StringsByteSplit/RaggedGetItem/strided_slice_5", "op": "StridedSlice", "input": ["StringsByteSplit/RaggedGetItem/strided_slice_4:output:0", "map_while_body_49115/StringsByteSplit/RaggedGetItem/strided_slice_5/Const:output:0", "map_while_body_49115/StringsByteSplit/RaggedGetItem/strided_slice_5/Const:output:0", "map_while_body_49115/StringsByteSplit/RaggedGetItem/strided_slice_5/Const:output:0"], "attr": {"_output_shapes": {"list": {"shape": [{"dim": [{"size": "-1"}]}]}}, "end_mask": {"i": "0"}, "T": {"type": "DT_STRING"}, "shrink_axis_mask": {"i": "0"}, "ellipsis_mask": {"i": "0"}, "Index": {"type": "DT_INT32"}, "new_axis_mask": {"i": "0"}, "begin_mask": {"i": "0"}}}, {"name": "StringNGrams/RaggedFromRowStarts/Shape", "op": "<PERSON><PERSON><PERSON>", "input": ["StringsByteSplit/RaggedGetItem/strided_slice_5:output:0"], "attr": {"_output_shapes": {"list": {"shape": [{"dim": [{"size": "1"}]}]}}, "T": {"type": "DT_STRING"}, "out_type": {"type": "DT_INT32"}}}, {"name": "StringNGrams/RaggedFromRowStarts/strided_slice", "op": "StridedSlice", "input": ["StringNGrams/RaggedFromRowStarts/Shape:output:0", "map_while_body_49115/StringNGrams/RaggedFromRowStarts/strided_slice/stack:output:0", "map_while_body_49115/StringNGrams/RaggedFromRowStarts/strided_slice/stack_1:output:0", "map_while_body_49115/StringNGrams/RaggedFromRowStarts/strided_slice/stack_2:output:0"], "attr": {"shrink_axis_mask": {"i": "1"}, "T": {"type": "DT_INT32"}, "end_mask": {"i": "0"}, "_output_shapes": {"list": {"shape": [{}]}}, "begin_mask": {"i": "0"}, "Index": {"type": "DT_INT32"}, "new_axis_mask": {"i": "0"}, "ellipsis_mask": {"i": "0"}}}, {"name": "StringNGrams/RaggedFromRowStarts/RowPartitionFromRowStarts/Cast", "op": "Cast", "input": ["StringNGrams/RaggedFromRowStarts/strided_slice:output:0"], "attr": {"DstT": {"type": "DT_INT64"}, "_output_shapes": {"list": {"shape": [{}]}}, "SrcT": {"type": "DT_INT32"}, "Truncate": {"b": false}}}, {"name": "map_while_body_49115/ConstantFolding/StringNGrams/RaggedFromRowStarts/RowPartitionFromRowStarts/concat/values_1_const_axis", "op": "Const", "input": ["^StringNGrams/RaggedFromRowStarts/RowPartitionFromRowStarts/Cast"], "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "StringNGrams/RaggedFromRowStarts/RowPartitionFromRowStarts/concat/values_1", "op": "ExpandDims", "input": ["StringNGrams/RaggedFromRowStarts/RowPartitionFromRowStarts/Cast:y:0", "map_while_body_49115/ConstantFolding/StringNGrams/RaggedFromRowStarts/RowPartitionFromRowStarts/concat/values_1_const_axis:output:0"], "attr": {"T": {"type": "DT_INT64"}, "_output_shapes": {"list": {"shape": [{"dim": [{"size": "1"}]}]}}, "Tdim": {"type": "DT_INT32"}}}, {"name": "StringNGrams/RaggedFromRowStarts/RowPartitionFromRowStarts/concat", "op": "ConcatV2", "input": ["map_while_body_49115/StringNGrams/RaggedFromRowStarts/RowPartitionFromRowStarts/row_starts:output:0", "StringNGrams/RaggedFromRowStarts/RowPartitionFromRowStarts/concat/values_1:output:0", "map_while_body_49115/StringNGrams/RaggedFromRowStarts/RowPartitionFromRowStarts/concat/axis:output:0"], "attr": {"T": {"type": "DT_INT64"}, "N": {"i": "2"}, "_output_shapes": {"list": {"shape": [{"dim": [{"size": "2"}]}]}}, "Tidx": {"type": "DT_INT32"}}}, {"name": "StringNGrams/StringNGrams/StringNGrams", "op": "StringNGrams", "input": ["StringsByteSplit/RaggedGetItem/strided_slice_5:output:0", "StringNGrams/RaggedFromRowStarts/RowPartitionFromRowStarts/concat:output:0"], "attr": {"_output_shapes": {"list": {"shape": [{"dim": [{"size": "-1"}]}, {"dim": [{"size": "2"}]}]}}, "right_pad": {"s": ""}, "ngram_widths": {"list": {"i": ["2"]}}, "left_pad": {"s": ""}, "separator": {"s": "IA=="}, "preserve_short_sequences": {"b": false}, "Tsplits": {"type": "DT_INT64"}, "pad_width": {"i": "0"}}}, {"name": "StringNGrams/RaggedGetItem/strided_slice", "op": "StridedSlice", "input": ["StringNGrams/StringNGrams/StringNGrams:ngrams_splits:0", "map_while_body_49115/StringNGrams/RaggedGetItem/strided_slice/stack:output:0", "map_while_body_49115/StringNGrams/RaggedGetItem/strided_slice/stack_1:output:0", "map_while_body_49115/StringNGrams/RaggedGetItem/strided_slice/stack_2:output:0"], "attr": {"begin_mask": {"i": "1"}, "ellipsis_mask": {"i": "0"}, "end_mask": {"i": "0"}, "shrink_axis_mask": {"i": "0"}, "Index": {"type": "DT_INT32"}, "_output_shapes": {"list": {"shape": [{"dim": [{"size": "1"}]}]}}, "new_axis_mask": {"i": "0"}, "T": {"type": "DT_INT64"}}}, {"name": "StringNGrams/RaggedGetItem/strided_slice_1", "op": "StridedSlice", "input": ["StringNGrams/StringNGrams/StringNGrams:ngrams_splits:0", "map_while_body_49115/StringNGrams/RaggedGetItem/strided_slice_1/stack:output:0", "map_while_body_49115/StringNGrams/RaggedGetItem/strided_slice_1/stack_1:output:0", "map_while_body_49115/StringNGrams/RaggedGetItem/strided_slice_1/stack_2:output:0"], "attr": {"ellipsis_mask": {"i": "0"}, "end_mask": {"i": "1"}, "begin_mask": {"i": "0"}, "shrink_axis_mask": {"i": "0"}, "new_axis_mask": {"i": "0"}, "_output_shapes": {"list": {"shape": [{"dim": [{"size": "1"}]}]}}, "T": {"type": "DT_INT64"}, "Index": {"type": "DT_INT32"}}}, {"name": "StringNGrams/RaggedGetItem/strided_slice_2", "op": "StridedSlice", "input": ["StringNGrams/RaggedGetItem/strided_slice:output:0", "map_while_body_49115/StringNGrams/RaggedGetItem/strided_slice_2/stack:output:0", "map_while_body_49115/StringNGrams/RaggedGetItem/strided_slice_2/stack_1:output:0", "map_while_body_49115/StringNGrams/RaggedGetItem/strided_slice_2/stack_2:output:0"], "attr": {"T": {"type": "DT_INT64"}, "end_mask": {"i": "0"}, "begin_mask": {"i": "0"}, "Index": {"type": "DT_INT32"}, "new_axis_mask": {"i": "0"}, "_output_shapes": {"list": {"shape": [{}]}}, "shrink_axis_mask": {"i": "1"}, "ellipsis_mask": {"i": "0"}}}, {"name": "StringNGrams/RaggedGetItem/strided_slice_3", "op": "StridedSlice", "input": ["StringNGrams/RaggedGetItem/strided_slice_1:output:0", "map_while_body_49115/StringNGrams/RaggedGetItem/strided_slice_3/stack:output:0", "map_while_body_49115/StringNGrams/RaggedGetItem/strided_slice_3/stack_1:output:0", "map_while_body_49115/StringNGrams/RaggedGetItem/strided_slice_3/stack_2:output:0"], "attr": {"end_mask": {"i": "0"}, "shrink_axis_mask": {"i": "1"}, "begin_mask": {"i": "0"}, "new_axis_mask": {"i": "0"}, "T": {"type": "DT_INT64"}, "_output_shapes": {"list": {"shape": [{}]}}, "ellipsis_mask": {"i": "0"}, "Index": {"type": "DT_INT32"}}}, {"name": "map_while_body_49115/ConstantFolding/StringNGrams/RaggedGetItem/strided_slice_4/stack_const_axis", "op": "Const", "input": ["^StringNGrams/RaggedGetItem/strided_slice_2"], "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "map_while_body_49115/ConstantFolding/StringNGrams/RaggedGetItem/strided_slice_4/stack_1_const_axis", "op": "Const", "input": ["^StringNGrams/RaggedGetItem/strided_slice_3"], "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {}}}}}, {"name": "StringNGrams/RaggedGetItem/strided_slice_4/stack", "op": "ExpandDims", "input": ["StringNGrams/RaggedGetItem/strided_slice_2:output:0", "map_while_body_49115/ConstantFolding/StringNGrams/RaggedGetItem/strided_slice_4/stack_const_axis:output:0"], "attr": {"Tdim": {"type": "DT_INT32"}, "_output_shapes": {"list": {"shape": [{"dim": [{"size": "1"}]}]}}, "T": {"type": "DT_INT64"}}}, {"name": "StringNGrams/RaggedGetItem/strided_slice_4/stack_1", "op": "ExpandDims", "input": ["StringNGrams/RaggedGetItem/strided_slice_3:output:0", "map_while_body_49115/ConstantFolding/StringNGrams/RaggedGetItem/strided_slice_4/stack_1_const_axis:output:0"], "attr": {"Tdim": {"type": "DT_INT32"}, "T": {"type": "DT_INT64"}, "_output_shapes": {"list": {"shape": [{"dim": [{"size": "1"}]}]}}}}, {"name": "StringNGrams/RaggedGetItem/strided_slice_4", "op": "StridedSlice", "input": ["StringNGrams/StringNGrams/StringNGrams:ngrams:0", "StringNGrams/RaggedGetItem/strided_slice_4/stack:output:0", "StringNGrams/RaggedGetItem/strided_slice_4/stack_1:output:0", "map_while_body_49115/StringNGrams/RaggedGetItem/strided_slice_4/Cast:output:0"], "attr": {"_output_shapes": {"list": {"shape": [{"dim": [{"size": "-1"}]}]}}, "Index": {"type": "DT_INT64"}, "begin_mask": {"i": "0"}, "T": {"type": "DT_STRING"}, "shrink_axis_mask": {"i": "0"}, "end_mask": {"i": "0"}, "ellipsis_mask": {"i": "0"}, "new_axis_mask": {"i": "0"}}}, {"name": "StringNGrams/RaggedGetItem/strided_slice_5", "op": "StridedSlice", "input": ["StringNGrams/RaggedGetItem/strided_slice_4:output:0", "map_while_body_49115/StringNGrams/RaggedGetItem/strided_slice_5/Const:output:0", "map_while_body_49115/StringNGrams/RaggedGetItem/strided_slice_5/Const:output:0", "map_while_body_49115/StringNGrams/RaggedGetItem/strided_slice_5/Const:output:0"], "attr": {"_output_shapes": {"list": {"shape": [{"dim": [{"size": "-1"}]}]}}, "Index": {"type": "DT_INT32"}, "shrink_axis_mask": {"i": "0"}, "end_mask": {"i": "0"}, "new_axis_mask": {"i": "0"}, "begin_mask": {"i": "0"}, "T": {"type": "DT_STRING"}, "ellipsis_mask": {"i": "0"}}}, {"name": "concat", "op": "ConcatV2", "input": ["StringNGrams/RaggedGetItem/strided_slice_5:output:0", "map_while_body_49115/Const:output:0", "map_while_body_49115/concat/axis:output:0"], "attr": {"_output_shapes": {"list": {"shape": [{"dim": [{"size": "-1"}]}]}}, "N": {"i": "2"}, "T": {"type": "DT_STRING"}, "Tidx": {"type": "DT_INT32"}}}, {"name": "strided_slice", "op": "StridedSlice", "input": ["concat:output:0", "map_while_body_49115/strided_slice/stack:output:0", "map_while_body_49115/strided_slice/stack_1:output:0", "map_while_body_49115/strided_slice/stack_2:output:0"], "attr": {"Index": {"type": "DT_INT32"}, "shrink_axis_mask": {"i": "0"}, "begin_mask": {"i": "1"}, "end_mask": {"i": "0"}, "T": {"type": "DT_STRING"}, "new_axis_mask": {"i": "0"}, "_output_shapes": {"list": {"shape": [{"dim": [{"size": "-1"}]}]}}, "ellipsis_mask": {"i": "0"}}}, {"name": "TensorArrayV2Write/TensorListSetItem", "op": "TensorListSetItem", "input": ["placeholder_1", "placeholder", "strided_slice:output:0"], "attr": {"_output_shapes": {"list": {"shape": [{}]}}, "element_dtype": {"type": "DT_STRING"}}}], "ret": {"add": "add_0:z:0", "tensorarrayv2read_tensorlistgetitem_map_tensorarrayunstack_tensorlistfromtensor": "tensorarrayv2read_tensorlistgetitem_map_tensorarrayunstack_tensorlistfromtensor_0", "map_strided_slice": "map_strided_slice_0", "map_strided_slice_1": "map_strided_slice_1_0", "tensorarrayv2write_tensorlistsetitem": "TensorArrayV2Write/TensorListSetItem:output_handle:0", "add_1": "add_1_0:z:0"}, "attr": {"_input_shapes": {"list": {"shape": [{}, {}, {}, {}, {}, {}]}}}, "argAttr": {"0": {"attr": {"_output_shapes": {"list": {"shape": [{}]}}}}, "5": {"attr": {"_output_shapes": {"list": {"shape": [{}]}}}}, "3": {"attr": {"_output_shapes": {"list": {"shape": [{}]}}}}, "1": {"attr": {"_output_shapes": {"list": {"shape": [{}]}}}}, "4": {"attr": {"_output_shapes": {"list": {"shape": [{}]}}}}, "2": {"attr": {"_output_shapes": {"list": {"shape": [{}]}}}}}}, {"signature": {"name": "map_while_cond_49114", "inputArg": [{"name": "map_while_loop_counter", "type": "DT_INT32"}, {"name": "map_strided_slice", "type": "DT_INT32"}, {"name": "placeholder", "type": "DT_INT32"}, {"name": "placeholder_1", "type": "DT_VARIANT"}, {"name": "less_map_strided_slice", "type": "DT_INT32"}, {"name": "map_while_cond_49114___redundant_placeholder0", "type": "DT_VARIANT"}], "outputArg": [{"name": "logicaland", "type": "DT_BOOL"}]}, "nodeDef": [{"name": "Less_1", "op": "Less", "input": ["map_while_loop_counter", "map_strided_slice"], "attr": {"_output_shapes": {"list": {"shape": [{}]}}, "T": {"type": "DT_INT32"}}}, {"name": "Less", "op": "Less", "input": ["placeholder", "less_map_strided_slice"], "attr": {"T": {"type": "DT_INT32"}, "_output_shapes": {"list": {"shape": [{}]}}}}, {"name": "LogicalAnd", "op": "LogicalAnd", "input": ["Less_1:z:0", "Less:z:0"], "attr": {"_output_shapes": {"list": {"shape": [{}]}}}}], "ret": {"logicaland": "LogicalAnd:z:0"}, "attr": {"_input_shapes": {"list": {"shape": [{}, {}, {}, {}, {}, {"unknownRank": true}]}}}, "argAttr": {"4": {"attr": {"_output_shapes": {"list": {"shape": [{}]}}}}, "2": {"attr": {"_output_shapes": {"list": {"shape": [{}]}}}}, "5": {"attr": {"_output_shapes": {"list": {"shape": [{"unknownRank": true}]}}}}, "0": {"attr": {"_output_shapes": {"list": {"shape": [{}]}}}}, "3": {"attr": {"_output_shapes": {"list": {"shape": [{}]}}}}, "1": {"attr": {"_output_shapes": {"list": {"shape": [{}]}}}}}}]}, "versions": {"minConsumer": 12}}, "modelInitializer": {"node": [{"name": "head/predictions/class_string_lookup/Const", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_STRING", "tensorShape": {"dim": [{"size": "54"}]}}}, "dtype": {"type": "DT_STRING"}}}, {"name": "head/predictions/class_string_lookup/Size", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "head/predictions/class_string_lookup/range/start", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "head/predictions/class_string_lookup/range/delta", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {}}}}}, {"name": "head/predictions/class_string_lookup/range", "op": "Range", "input": ["head/predictions/class_string_lookup/range/start", "head/predictions/class_string_lookup/Size", "head/predictions/class_string_lookup/range/delta"], "attr": {"Tidx": {"type": "DT_INT32"}}}, {"name": "head/predictions/class_string_lookup/Cast", "op": "Cast", "input": ["head/predictions/class_string_lookup/range"], "attr": {"Truncate": {"b": false}, "SrcT": {"type": "DT_INT32"}, "DstT": {"type": "DT_INT64"}}}, {"name": "head/predictions/class_string_lookup/hash_table", "op": "HashTableV2", "attr": {"value_dtype": {"type": "DT_STRING"}, "container": {"s": ""}, "shared_name": {"s": "aGFzaF90YWJsZV80MTQ1NGNlMC0xMmMwLTRiMmEtOTI0My1hZjUxZjE4YWJmOTQ="}, "key_dtype": {"type": "DT_INT64"}, "use_node_name_sharing": {"b": false}}}, {"name": "head/predictions/class_string_lookup/table_init/LookupTableImportV2", "op": "LookupTableImportV2", "input": ["head/predictions/class_string_lookup/hash_table", "head/predictions/class_string_lookup/Cast", "head/predictions/class_string_lookup/Const"], "attr": {"Tout": {"type": "DT_STRING"}, "Tin": {"type": "DT_INT64"}}}], "library": {"function": [{"signature": {"name": "dnn_zero_fraction_1_cond_false_49441", "inputArg": [{"name": "count_nonzero_notequal_dnn_hiddenlayer_1_relu", "type": "DT_FLOAT"}], "outputArg": [{"name": "count_nonzero_nonzero_count", "type": "DT_INT64"}, {"name": "optionalfromvalue", "type": "DT_VARIANT"}, {"name": "optionalfromvalue_1", "type": "DT_VARIANT"}, {"name": "optionalfromvalue_2", "type": "DT_VARIANT"}, {"name": "optionalfromvalue_3", "type": "DT_VARIANT"}, {"name": "optionalnone", "type": "DT_VARIANT"}]}, "nodeDef": [{"name": "count_nonzero/zeros", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "_output_shapes": {"list": {"shape": [{}]}}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}, "floatVal": [0.0]}}}, "experimentalDebugInfo": {"originalNodeNames": ["count_nonzero/zeros"]}}, {"name": "count_nonzero/NotEqual", "op": "NotEqual", "input": ["count_nonzero_notequal_dnn_hiddenlayer_1_relu", "count_nonzero/zeros:output:0"], "attr": {"_output_shapes": {"list": {"shape": [{"dim": [{"size": "-1"}, {"size": "32"}]}]}}, "T": {"type": "DT_FLOAT"}}, "experimentalDebugInfo": {"originalNodeNames": ["count_nonzero/NotEqual"]}}, {"name": "count_nonzero/Cast", "op": "Cast", "input": ["count_nonzero/NotEqual:z:0"], "attr": {"SrcT": {"type": "DT_BOOL"}, "DstT": {"type": "DT_INT64"}, "_output_shapes": {"list": {"shape": [{"dim": [{"size": "-1"}, {"size": "32"}]}]}}}, "experimentalDebugInfo": {"originalNodeNames": ["count_nonzero/Cast"]}}, {"name": "count_nonzero/Const", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "2"}]}, "tensorContent": "AAAAAAEAAAA="}}, "dtype": {"type": "DT_INT32"}, "_output_shapes": {"list": {"shape": [{"dim": [{"size": "2"}]}]}}}, "experimentalDebugInfo": {"originalNodeNames": ["count_nonzero/Const"]}}, {"name": "count_nonzero/nonzero_count", "op": "Sum", "input": ["count_nonzero/Cast:y:0", "count_nonzero/Const:output:0"], "attr": {"_output_shapes": {"list": {"shape": [{}]}}, "T": {"type": "DT_INT64"}}, "experimentalDebugInfo": {"originalNodeNames": ["count_nonzero/nonzero_count"]}}, {"name": "OptionalFromValue", "op": "OptionalFromValue", "input": ["count_nonzero/zeros:output:0"], "attr": {"_output_shapes": {"list": {"shape": [{}]}}, "Toutput_types": {"list": {"type": ["DT_FLOAT"]}}}, "experimentalDebugInfo": {"originalNodeNames": ["OptionalFromValue"]}}, {"name": "OptionalFromValue_1", "op": "OptionalFromValue", "input": ["count_nonzero/NotEqual:z:0"], "attr": {"Toutput_types": {"list": {"type": ["DT_BOOL"]}}, "_output_shapes": {"list": {"shape": [{}]}}}, "experimentalDebugInfo": {"originalNodeNames": ["OptionalFromValue_1"]}}, {"name": "OptionalFromValue_2", "op": "OptionalFromValue", "input": ["count_nonzero/Cast:y:0"], "attr": {"_output_shapes": {"list": {"shape": [{}]}}, "Toutput_types": {"list": {"type": ["DT_INT64"]}}}, "experimentalDebugInfo": {"originalNodeNames": ["OptionalFromValue_2"]}}, {"name": "OptionalFromValue_3", "op": "OptionalFromValue", "input": ["count_nonzero/Const:output:0"], "attr": {"Toutput_types": {"list": {"type": ["DT_INT32"]}}, "_output_shapes": {"list": {"shape": [{}]}}}, "experimentalDebugInfo": {"originalNodeNames": ["OptionalFromValue_3"]}}, {"name": "OptionalNone", "op": "OptionalNone", "attr": {"_output_shapes": {"list": {"shape": [{}]}}}, "experimentalDebugInfo": {"originalNodeNames": ["OptionalNone"]}}], "ret": {"count_nonzero_nonzero_count": "count_nonzero/nonzero_count:output:0", "optionalfromvalue": "OptionalFromValue:optional:0", "optionalfromvalue_3": "OptionalFromValue_3:optional:0", "optionalfromvalue_2": "OptionalFromValue_2:optional:0", "optionalfromvalue_1": "OptionalFromValue_1:optional:0", "optionalnone": "OptionalNone:optional:0"}, "attr": {"_input_shapes": {"list": {"shape": [{"dim": [{"size": "-1"}, {"size": "32"}]}]}}}, "argAttr": {"0": {"attr": {"_output_shapes": {"list": {"shape": [{"dim": [{"size": "-1"}, {"size": "32"}]}]}}}}}}, {"signature": {"name": "map_while_cond_49114", "inputArg": [{"name": "map_while_loop_counter", "type": "DT_INT32"}, {"name": "map_strided_slice", "type": "DT_INT32"}, {"name": "placeholder", "type": "DT_INT32"}, {"name": "placeholder_1", "type": "DT_VARIANT"}, {"name": "less_map_strided_slice", "type": "DT_INT32"}, {"name": "map_while_cond_49114___redundant_placeholder0", "type": "DT_VARIANT"}], "outputArg": [{"name": "logicaland", "type": "DT_BOOL"}]}, "nodeDef": [{"name": "Less", "op": "Less", "input": ["placeholder", "less_map_strided_slice"], "attr": {"T": {"type": "DT_INT32"}, "_output_shapes": {"list": {"shape": [{}]}}}, "experimentalDebugInfo": {"originalNodeNames": ["Less"]}}, {"name": "Less_1", "op": "Less", "input": ["map_while_loop_counter", "map_strided_slice"], "attr": {"T": {"type": "DT_INT32"}, "_output_shapes": {"list": {"shape": [{}]}}}, "experimentalDebugInfo": {"originalNodeNames": ["Less_1"]}}, {"name": "LogicalAnd", "op": "LogicalAnd", "input": ["Less_1:z:0", "Less:z:0"], "attr": {"_output_shapes": {"list": {"shape": [{}]}}}, "experimentalDebugInfo": {"originalNodeNames": ["LogicalAnd"]}}], "ret": {"logicaland": "LogicalAnd:z:0"}, "attr": {"_input_shapes": {"list": {"shape": [{}, {}, {}, {}, {}, {"unknownRank": true}]}}}, "argAttr": {"1": {"attr": {"_output_shapes": {"list": {"shape": [{}]}}}}, "3": {"attr": {"_output_shapes": {"list": {"shape": [{}]}}}}, "0": {"attr": {"_output_shapes": {"list": {"shape": [{}]}}}}, "5": {"attr": {"_output_shapes": {"list": {"shape": [{"unknownRank": true}]}}}}, "2": {"attr": {"_output_shapes": {"list": {"shape": [{}]}}}}, "4": {"attr": {"_output_shapes": {"list": {"shape": [{}]}}}}}}, {"signature": {"name": "zero_fraction_total_zero_zero_count_false_49655", "inputArg": [{"name": "zero_fraction_readvariableop_linear_linear_model_content_weights", "type": "DT_RESOURCE", "handleData": [{"dtype": "DT_FLOAT", "shape": {"dim": [{"size": "5000"}, {"size": "54"}]}}]}, {"name": "cast_zero_fraction_total_size_size", "type": "DT_INT64"}], "outputArg": [{"name": "mul", "type": "DT_FLOAT"}], "isStateful": true}, "nodeDef": [{"name": "zero_fraction/ReadVariableOp", "op": "ReadVariableOp", "input": ["zero_fraction_readvariableop_linear_linear_model_content_weights"], "attr": {"_output_shapes": {"list": {"shape": [{"dim": [{"size": "5000"}, {"size": "54"}]}]}}, "dtype": {"type": "DT_FLOAT"}}, "experimentalDebugInfo": {"originalNodeNames": ["zero_fraction/ReadVariableOp"]}}, {"name": "zero_fraction/Size", "op": "Const", "attr": {"_output_shapes": {"list": {"shape": [{}]}}, "value": {"tensor": {"dtype": "DT_INT64", "tensorShape": {}, "int64Val": ["270000"]}}, "dtype": {"type": "DT_INT64"}}, "experimentalDebugInfo": {"originalNodeNames": ["zero_fraction/Size"]}}, {"name": "zero_fraction/LessEqual/y", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT64", "tensorShape": {}, "int64Val": ["2147483647"]}}, "_output_shapes": {"list": {"shape": [{}]}}, "dtype": {"type": "DT_INT64"}}, "experimentalDebugInfo": {"originalNodeNames": ["zero_fraction/LessEqual/y"]}}, {"name": "zero_fraction/LessEqual", "op": "LessEqual", "input": ["zero_fraction/Size:output:0", "zero_fraction/LessEqual/y:output:0"], "attr": {"T": {"type": "DT_INT64"}, "_output_shapes": {"list": {"shape": [{}]}}}, "experimentalDebugInfo": {"originalNodeNames": ["zero_fraction/LessEqual"]}}, {"name": "zero_fraction/cond", "op": "StatelessIf", "input": ["zero_fraction/LessEqual:z:0", "zero_fraction/ReadVariableOp:value:0"], "attr": {"else_branch": {"func": {"name": "zero_fraction_cond_false_49665"}}, "then_branch": {"func": {"name": "zero_fraction_cond_true_49664"}}, "Tin": {"list": {"type": ["DT_FLOAT"]}}, "_read_only_resource_inputs": {"list": {}}, "Tcond": {"type": "DT_BOOL"}, "_lower_using_switch_merge": {"b": true}, "output_shapes": {"list": {"shape": [{}]}}, "Tout": {"list": {"type": ["DT_INT64"]}}, "_output_shapes": {"list": {"shape": [{}]}}}, "experimentalDebugInfo": {"originalNodeNames": ["zero_fraction/cond"]}}, {"name": "zero_fraction/cond/Identity", "op": "Identity", "input": ["zero_fraction/cond:output:0"], "attr": {"_output_shapes": {"list": {"shape": [{}]}}, "T": {"type": "DT_INT64"}}, "experimentalDebugInfo": {"originalNodeNames": ["zero_fraction/cond/Identity"]}}, {"name": "zero_fraction/counts_to_fraction/sub", "op": "Sub", "input": ["zero_fraction/Size:output:0", "zero_fraction/cond/Identity:output:0"], "attr": {"T": {"type": "DT_INT64"}, "_output_shapes": {"list": {"shape": [{}]}}}, "experimentalDebugInfo": {"originalNodeNames": ["zero_fraction/counts_to_fraction/sub"]}}, {"name": "zero_fraction/counts_to_fraction/Cast", "op": "Cast", "input": ["zero_fraction/counts_to_fraction/sub:z:0"], "attr": {"_output_shapes": {"list": {"shape": [{}]}}, "DstT": {"type": "DT_FLOAT"}, "SrcT": {"type": "DT_INT64"}}, "experimentalDebugInfo": {"originalNodeNames": ["zero_fraction/counts_to_fraction/Cast"]}}, {"name": "zero_fraction/counts_to_fraction/Cast_1", "op": "Cast", "input": ["zero_fraction/Size:output:0"], "attr": {"DstT": {"type": "DT_FLOAT"}, "_output_shapes": {"list": {"shape": [{}]}}, "SrcT": {"type": "DT_INT64"}}, "experimentalDebugInfo": {"originalNodeNames": ["zero_fraction/counts_to_fraction/Cast_1"]}}, {"name": "zero_fraction/counts_to_fraction/truediv", "op": "RealDiv", "input": ["zero_fraction/counts_to_fraction/Cast:y:0", "zero_fraction/counts_to_fraction/Cast_1:y:0"], "attr": {"T": {"type": "DT_FLOAT"}, "_output_shapes": {"list": {"shape": [{}]}}}, "experimentalDebugInfo": {"originalNodeNames": ["zero_fraction/counts_to_fraction/truediv"]}}, {"name": "zero_fraction/fraction", "op": "Identity", "input": ["zero_fraction/counts_to_fraction/truediv:z:0"], "attr": {"T": {"type": "DT_FLOAT"}, "_output_shapes": {"list": {"shape": [{}]}}}, "experimentalDebugInfo": {"originalNodeNames": ["zero_fraction/fraction"]}}, {"name": "Cast", "op": "Cast", "input": ["cast_zero_fraction_total_size_size"], "attr": {"DstT": {"type": "DT_FLOAT"}, "SrcT": {"type": "DT_INT64"}, "_output_shapes": {"list": {"shape": [{}]}}}, "experimentalDebugInfo": {"originalNodeNames": ["Cast"]}}, {"name": "mul_0", "op": "<PERSON><PERSON>", "input": ["zero_fraction/fraction:output:0", "Cast:y:0"], "attr": {"T": {"type": "DT_FLOAT"}}, "experimentalDebugInfo": {"originalNodeNames": ["mul"]}}], "ret": {"mul": "mul_0:z:0"}, "attr": {"_input_shapes": {"list": {"shape": [{"unknownRank": true}, {}]}}}, "argAttr": {"1": {"attr": {"_output_shapes": {"list": {"shape": [{}]}}}}}}, {"signature": {"name": "dnn_zero_fraction_2_cond_false_49510", "inputArg": [{"name": "count_nonzero_notequal_dnn_logits_biasadd", "type": "DT_FLOAT"}], "outputArg": [{"name": "count_nonzero_nonzero_count", "type": "DT_INT64"}, {"name": "optionalfromvalue", "type": "DT_VARIANT"}, {"name": "optionalfromvalue_1", "type": "DT_VARIANT"}, {"name": "optionalfromvalue_2", "type": "DT_VARIANT"}, {"name": "optionalfromvalue_3", "type": "DT_VARIANT"}, {"name": "optionalnone", "type": "DT_VARIANT"}]}, "nodeDef": [{"name": "count_nonzero/zeros", "op": "Const", "attr": {"_output_shapes": {"list": {"shape": [{}]}}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}, "floatVal": [0.0]}}, "dtype": {"type": "DT_FLOAT"}}, "experimentalDebugInfo": {"originalNodeNames": ["count_nonzero/zeros"]}}, {"name": "count_nonzero/NotEqual", "op": "NotEqual", "input": ["count_nonzero_notequal_dnn_logits_biasadd", "count_nonzero/zeros:output:0"], "attr": {"_output_shapes": {"list": {"shape": [{"dim": [{"size": "-1"}, {"size": "54"}]}]}}, "T": {"type": "DT_FLOAT"}}, "experimentalDebugInfo": {"originalNodeNames": ["count_nonzero/NotEqual"]}}, {"name": "count_nonzero/Cast", "op": "Cast", "input": ["count_nonzero/NotEqual:z:0"], "attr": {"SrcT": {"type": "DT_BOOL"}, "DstT": {"type": "DT_INT64"}, "_output_shapes": {"list": {"shape": [{"dim": [{"size": "-1"}, {"size": "54"}]}]}}}, "experimentalDebugInfo": {"originalNodeNames": ["count_nonzero/Cast"]}}, {"name": "count_nonzero/Const", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "2"}]}, "tensorContent": "AAAAAAEAAAA="}}, "dtype": {"type": "DT_INT32"}, "_output_shapes": {"list": {"shape": [{"dim": [{"size": "2"}]}]}}}, "experimentalDebugInfo": {"originalNodeNames": ["count_nonzero/Const"]}}, {"name": "count_nonzero/nonzero_count", "op": "Sum", "input": ["count_nonzero/Cast:y:0", "count_nonzero/Const:output:0"], "attr": {"_output_shapes": {"list": {"shape": [{}]}}, "T": {"type": "DT_INT64"}}, "experimentalDebugInfo": {"originalNodeNames": ["count_nonzero/nonzero_count"]}}, {"name": "OptionalFromValue", "op": "OptionalFromValue", "input": ["count_nonzero/zeros:output:0"], "attr": {"_output_shapes": {"list": {"shape": [{}]}}, "Toutput_types": {"list": {"type": ["DT_FLOAT"]}}}, "experimentalDebugInfo": {"originalNodeNames": ["OptionalFromValue"]}}, {"name": "OptionalFromValue_1", "op": "OptionalFromValue", "input": ["count_nonzero/NotEqual:z:0"], "attr": {"_output_shapes": {"list": {"shape": [{}]}}, "Toutput_types": {"list": {"type": ["DT_BOOL"]}}}, "experimentalDebugInfo": {"originalNodeNames": ["OptionalFromValue_1"]}}, {"name": "OptionalFromValue_2", "op": "OptionalFromValue", "input": ["count_nonzero/Cast:y:0"], "attr": {"Toutput_types": {"list": {"type": ["DT_INT64"]}}, "_output_shapes": {"list": {"shape": [{}]}}}, "experimentalDebugInfo": {"originalNodeNames": ["OptionalFromValue_2"]}}, {"name": "OptionalFromValue_3", "op": "OptionalFromValue", "input": ["count_nonzero/Const:output:0"], "attr": {"Toutput_types": {"list": {"type": ["DT_INT32"]}}, "_output_shapes": {"list": {"shape": [{}]}}}, "experimentalDebugInfo": {"originalNodeNames": ["OptionalFromValue_3"]}}, {"name": "OptionalNone", "op": "OptionalNone", "attr": {"_output_shapes": {"list": {"shape": [{}]}}}, "experimentalDebugInfo": {"originalNodeNames": ["OptionalNone"]}}], "ret": {"optionalfromvalue_1": "OptionalFromValue_1:optional:0", "optionalfromvalue_3": "OptionalFromValue_3:optional:0", "count_nonzero_nonzero_count": "count_nonzero/nonzero_count:output:0", "optionalfromvalue_2": "OptionalFromValue_2:optional:0", "optionalnone": "OptionalNone:optional:0", "optionalfromvalue": "OptionalFromValue:optional:0"}, "attr": {"_input_shapes": {"list": {"shape": [{"dim": [{"size": "-1"}, {"size": "54"}]}]}}}, "argAttr": {"0": {"attr": {"_output_shapes": {"list": {"shape": [{"dim": [{"size": "-1"}, {"size": "54"}]}]}}}}}}, {"signature": {"name": "dnn_zero_fraction_2_cond_true_49509", "inputArg": [{"name": "count_nonzero_notequal_dnn_logits_biasadd", "type": "DT_FLOAT"}], "outputArg": [{"name": "cast", "type": "DT_INT64"}, {"name": "optionalfromvalue", "type": "DT_VARIANT"}, {"name": "optionalfromvalue_1", "type": "DT_VARIANT"}, {"name": "optionalfromvalue_2", "type": "DT_VARIANT"}, {"name": "optionalfromvalue_3", "type": "DT_VARIANT"}, {"name": "optionalfromvalue_4", "type": "DT_VARIANT"}]}, "nodeDef": [{"name": "count_nonzero/zeros", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}, "floatVal": [0.0]}}, "dtype": {"type": "DT_FLOAT"}, "_output_shapes": {"list": {"shape": [{}]}}}, "experimentalDebugInfo": {"originalNodeNames": ["count_nonzero/zeros"]}}, {"name": "count_nonzero/NotEqual", "op": "NotEqual", "input": ["count_nonzero_notequal_dnn_logits_biasadd", "count_nonzero/zeros:output:0"], "attr": {"_output_shapes": {"list": {"shape": [{"dim": [{"size": "-1"}, {"size": "54"}]}]}}, "T": {"type": "DT_FLOAT"}}, "experimentalDebugInfo": {"originalNodeNames": ["count_nonzero/NotEqual"]}}, {"name": "count_nonzero/Cast", "op": "Cast", "input": ["count_nonzero/NotEqual:z:0"], "attr": {"_output_shapes": {"list": {"shape": [{"dim": [{"size": "-1"}, {"size": "54"}]}]}}, "SrcT": {"type": "DT_BOOL"}, "DstT": {"type": "DT_INT32"}}, "experimentalDebugInfo": {"originalNodeNames": ["count_nonzero/Cast"]}}, {"name": "count_nonzero/Const", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "2"}]}, "tensorContent": "AAAAAAEAAAA="}}, "dtype": {"type": "DT_INT32"}, "_output_shapes": {"list": {"shape": [{"dim": [{"size": "2"}]}]}}}, "experimentalDebugInfo": {"originalNodeNames": ["count_nonzero/Const"]}}, {"name": "count_nonzero/nonzero_count", "op": "Sum", "input": ["count_nonzero/Cast:y:0", "count_nonzero/Const:output:0"], "attr": {"T": {"type": "DT_INT32"}, "_output_shapes": {"list": {"shape": [{}]}}}, "experimentalDebugInfo": {"originalNodeNames": ["count_nonzero/nonzero_count"]}}, {"name": "Cast", "op": "Cast", "input": ["count_nonzero/nonzero_count:output:0"], "attr": {"DstT": {"type": "DT_INT64"}, "_output_shapes": {"list": {"shape": [{}]}}, "SrcT": {"type": "DT_INT32"}}, "experimentalDebugInfo": {"originalNodeNames": ["Cast"]}}, {"name": "OptionalFromValue", "op": "OptionalFromValue", "input": ["count_nonzero/zeros:output:0"], "attr": {"Toutput_types": {"list": {"type": ["DT_FLOAT"]}}, "_output_shapes": {"list": {"shape": [{}]}}}, "experimentalDebugInfo": {"originalNodeNames": ["OptionalFromValue"]}}, {"name": "OptionalFromValue_1", "op": "OptionalFromValue", "input": ["count_nonzero/NotEqual:z:0"], "attr": {"Toutput_types": {"list": {"type": ["DT_BOOL"]}}, "_output_shapes": {"list": {"shape": [{}]}}}, "experimentalDebugInfo": {"originalNodeNames": ["OptionalFromValue_1"]}}, {"name": "OptionalFromValue_2", "op": "OptionalFromValue", "input": ["count_nonzero/Cast:y:0"], "attr": {"_output_shapes": {"list": {"shape": [{}]}}, "Toutput_types": {"list": {"type": ["DT_INT32"]}}}, "experimentalDebugInfo": {"originalNodeNames": ["OptionalFromValue_2"]}}, {"name": "OptionalFromValue_3", "op": "OptionalFromValue", "input": ["count_nonzero/Const:output:0"], "attr": {"Toutput_types": {"list": {"type": ["DT_INT32"]}}, "_output_shapes": {"list": {"shape": [{}]}}}, "experimentalDebugInfo": {"originalNodeNames": ["OptionalFromValue_3"]}}, {"name": "OptionalFromValue_4", "op": "OptionalFromValue", "input": ["count_nonzero/nonzero_count:output:0"], "attr": {"_output_shapes": {"list": {"shape": [{}]}}, "Toutput_types": {"list": {"type": ["DT_INT32"]}}}, "experimentalDebugInfo": {"originalNodeNames": ["OptionalFromValue_4"]}}], "ret": {"cast": "Cast:y:0", "optionalfromvalue_2": "OptionalFromValue_2:optional:0", "optionalfromvalue": "OptionalFromValue:optional:0", "optionalfromvalue_3": "OptionalFromValue_3:optional:0", "optionalfromvalue_4": "OptionalFromValue_4:optional:0", "optionalfromvalue_1": "OptionalFromValue_1:optional:0"}, "attr": {"_input_shapes": {"list": {"shape": [{"dim": [{"size": "-1"}, {"size": "54"}]}]}}}, "argAttr": {"0": {"attr": {"_output_shapes": {"list": {"shape": [{"dim": [{"size": "-1"}, {"size": "54"}]}]}}}}}}, {"signature": {"name": "dnn_zero_fraction_cond_false_49371", "inputArg": [{"name": "count_nonzero_notequal_dnn_hiddenlayer_0_relu", "type": "DT_FLOAT"}], "outputArg": [{"name": "count_nonzero_nonzero_count", "type": "DT_INT64"}, {"name": "optionalfromvalue", "type": "DT_VARIANT"}, {"name": "optionalfromvalue_1", "type": "DT_VARIANT"}, {"name": "optionalfromvalue_2", "type": "DT_VARIANT"}, {"name": "optionalfromvalue_3", "type": "DT_VARIANT"}, {"name": "optionalnone", "type": "DT_VARIANT"}]}, "nodeDef": [{"name": "count_nonzero/zeros", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}, "floatVal": [0.0]}}, "_output_shapes": {"list": {"shape": [{}]}}}, "experimentalDebugInfo": {"originalNodeNames": ["count_nonzero/zeros"]}}, {"name": "count_nonzero/NotEqual", "op": "NotEqual", "input": ["count_nonzero_notequal_dnn_hiddenlayer_0_relu", "count_nonzero/zeros:output:0"], "attr": {"T": {"type": "DT_FLOAT"}, "_output_shapes": {"list": {"shape": [{"dim": [{"size": "-1"}, {"size": "512"}]}]}}}, "experimentalDebugInfo": {"originalNodeNames": ["count_nonzero/NotEqual"]}}, {"name": "count_nonzero/Cast", "op": "Cast", "input": ["count_nonzero/NotEqual:z:0"], "attr": {"SrcT": {"type": "DT_BOOL"}, "DstT": {"type": "DT_INT64"}, "_output_shapes": {"list": {"shape": [{"dim": [{"size": "-1"}, {"size": "512"}]}]}}}, "experimentalDebugInfo": {"originalNodeNames": ["count_nonzero/Cast"]}}, {"name": "count_nonzero/Const", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "_output_shapes": {"list": {"shape": [{"dim": [{"size": "2"}]}]}}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "2"}]}, "tensorContent": "AAAAAAEAAAA="}}}, "experimentalDebugInfo": {"originalNodeNames": ["count_nonzero/Const"]}}, {"name": "count_nonzero/nonzero_count", "op": "Sum", "input": ["count_nonzero/Cast:y:0", "count_nonzero/Const:output:0"], "attr": {"T": {"type": "DT_INT64"}, "_output_shapes": {"list": {"shape": [{}]}}}, "experimentalDebugInfo": {"originalNodeNames": ["count_nonzero/nonzero_count"]}}, {"name": "OptionalFromValue", "op": "OptionalFromValue", "input": ["count_nonzero/zeros:output:0"], "attr": {"_output_shapes": {"list": {"shape": [{}]}}, "Toutput_types": {"list": {"type": ["DT_FLOAT"]}}}, "experimentalDebugInfo": {"originalNodeNames": ["OptionalFromValue"]}}, {"name": "OptionalFromValue_1", "op": "OptionalFromValue", "input": ["count_nonzero/NotEqual:z:0"], "attr": {"_output_shapes": {"list": {"shape": [{}]}}, "Toutput_types": {"list": {"type": ["DT_BOOL"]}}}, "experimentalDebugInfo": {"originalNodeNames": ["OptionalFromValue_1"]}}, {"name": "OptionalFromValue_2", "op": "OptionalFromValue", "input": ["count_nonzero/Cast:y:0"], "attr": {"_output_shapes": {"list": {"shape": [{}]}}, "Toutput_types": {"list": {"type": ["DT_INT64"]}}}, "experimentalDebugInfo": {"originalNodeNames": ["OptionalFromValue_2"]}}, {"name": "OptionalFromValue_3", "op": "OptionalFromValue", "input": ["count_nonzero/Const:output:0"], "attr": {"_output_shapes": {"list": {"shape": [{}]}}, "Toutput_types": {"list": {"type": ["DT_INT32"]}}}, "experimentalDebugInfo": {"originalNodeNames": ["OptionalFromValue_3"]}}, {"name": "OptionalNone", "op": "OptionalNone", "attr": {"_output_shapes": {"list": {"shape": [{}]}}}, "experimentalDebugInfo": {"originalNodeNames": ["OptionalNone"]}}], "ret": {"optionalfromvalue_2": "OptionalFromValue_2:optional:0", "count_nonzero_nonzero_count": "count_nonzero/nonzero_count:output:0", "optionalfromvalue_1": "OptionalFromValue_1:optional:0", "optionalnone": "OptionalNone:optional:0", "optionalfromvalue": "OptionalFromValue:optional:0", "optionalfromvalue_3": "OptionalFromValue_3:optional:0"}, "attr": {"_input_shapes": {"list": {"shape": [{"dim": [{"size": "-1"}, {"size": "512"}]}]}}}, "argAttr": {"0": {"attr": {"_output_shapes": {"list": {"shape": [{"dim": [{"size": "-1"}, {"size": "512"}]}]}}}}}}, {"signature": {"name": "map_while_body_49115", "inputArg": [{"name": "map_while_loop_counter", "type": "DT_INT32"}, {"name": "map_strided_slice_0", "type": "DT_INT32"}, {"name": "placeholder", "type": "DT_INT32"}, {"name": "placeholder_1", "type": "DT_VARIANT"}, {"name": "map_strided_slice_1_0", "type": "DT_INT32"}, {"name": "tensorarrayv2read_tensorlistgetitem_map_tensorarrayunstack_tensorlistfromtensor_0", "type": "DT_VARIANT"}], "outputArg": [{"name": "add_1", "type": "DT_INT32"}, {"name": "map_strided_slice", "type": "DT_INT32"}, {"name": "add", "type": "DT_INT32"}, {"name": "tensorarrayv2write_tensorlistsetitem", "type": "DT_VARIANT"}, {"name": "map_strided_slice_1", "type": "DT_INT32"}, {"name": "tensorarrayv2read_tensorlistgetitem_map_tensorarrayunstack_tensorlistfromtensor", "type": "DT_VARIANT"}]}, "nodeDef": [{"name": "TensorArrayV2Read/Const", "op": "Const", "attr": {"_output_shapes": {"list": {"shape": [{"dim": [{}]}]}}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{}]}}}, "dtype": {"type": "DT_INT32"}}, "experimentalDebugInfo": {"originalNodeNames": ["TensorArrayV2Read/Const"]}}, {"name": "TensorArrayV2Read/TensorListGetItem", "op": "TensorListGetItem", "input": ["tensorarrayv2read_tensorlistgetitem_map_tensorarrayunstack_tensorlistfromtensor_0", "placeholder", "TensorArrayV2Read/Const:output:0"], "attr": {"_output_shapes": {"list": {"shape": [{}]}}, "element_dtype": {"type": "DT_STRING"}}, "experimentalDebugInfo": {"originalNodeNames": ["TensorArrayV2Read/TensorListGetItem"]}}, {"name": "Const", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_STRING", "tensorShape": {"dim": [{"size": "10000"}]}, "stringVal": ["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""]}}, "_output_shapes": {"list": {"shape": [{"dim": [{"size": "10000"}]}]}}, "dtype": {"type": "DT_STRING"}}, "experimentalDebugInfo": {"originalNodeNames": ["Const"]}}, {"name": "StringsByteSplit/stack", "op": "Pack", "input": ["TensorArrayV2Read/TensorListGetItem:item:0"], "attr": {"_output_shapes": {"list": {"shape": [{"dim": [{"size": "1"}]}]}}, "N": {"i": "1"}, "T": {"type": "DT_STRING"}}, "experimentalDebugInfo": {"originalNodeNames": ["StringsByteSplit/stack"]}}, {"name": "StringsByteSplit/StringsByteSplit/StringSplit/delimiter", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_STRING", "tensorShape": {}, "stringVal": [""]}}, "dtype": {"type": "DT_STRING"}, "_output_shapes": {"list": {"shape": [{}]}}}, "experimentalDebugInfo": {"originalNodeNames": ["StringsByteSplit/StringsByteSplit/StringSplit/delimiter"]}}, {"name": "StringsByteSplit/StringsByteSplit/StringSplit", "op": "StringSplit", "input": ["StringsByteSplit/stack:output:0", "StringsByteSplit/StringsByteSplit/StringSplit/delimiter:output:0"], "attr": {"_output_shapes": {"list": {"shape": [{"dim": [{"size": "-1"}, {"size": "2"}]}, {"dim": [{"size": "-1"}]}, {"dim": [{"size": "2"}]}]}}, "skip_empty": {"b": false}}, "experimentalDebugInfo": {"originalNodeNames": ["StringsByteSplit/StringsByteSplit/StringSplit"]}}, {"name": "StringsByteSplit/StringsByteSplit/strided_slice/stack", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "2"}]}, "tensorContent": "AAAAAAAAAAA="}}, "_output_shapes": {"list": {"shape": [{"dim": [{"size": "2"}]}]}}, "dtype": {"type": "DT_INT32"}}, "experimentalDebugInfo": {"originalNodeNames": ["StringsByteSplit/StringsByteSplit/strided_slice/stack"]}}, {"name": "StringsByteSplit/StringsByteSplit/strided_slice/stack_1", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "2"}]}, "tensorContent": "AAAAAAEAAAA="}}, "_output_shapes": {"list": {"shape": [{"dim": [{"size": "2"}]}]}}, "dtype": {"type": "DT_INT32"}}, "experimentalDebugInfo": {"originalNodeNames": ["StringsByteSplit/StringsByteSplit/strided_slice/stack_1"]}}, {"name": "StringsByteSplit/StringsByteSplit/strided_slice/stack_2", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "2"}]}, "tensorContent": "AQAAAAEAAAA="}}, "dtype": {"type": "DT_INT32"}, "_output_shapes": {"list": {"shape": [{"dim": [{"size": "2"}]}]}}}, "experimentalDebugInfo": {"originalNodeNames": ["StringsByteSplit/StringsByteSplit/strided_slice/stack_2"]}}, {"name": "StringsByteSplit/StringsByteSplit/strided_slice", "op": "StridedSlice", "input": ["StringsByteSplit/StringsByteSplit/StringSplit:indices:0", "StringsByteSplit/StringsByteSplit/strided_slice/stack:output:0", "StringsByteSplit/StringsByteSplit/strided_slice/stack_1:output:0", "StringsByteSplit/StringsByteSplit/strided_slice/stack_2:output:0"], "attr": {"Index": {"type": "DT_INT32"}, "shrink_axis_mask": {"i": "2"}, "begin_mask": {"i": "1"}, "T": {"type": "DT_INT64"}, "_output_shapes": {"list": {"shape": [{"dim": [{"size": "-1"}]}]}}, "end_mask": {"i": "1"}}, "experimentalDebugInfo": {"originalNodeNames": ["StringsByteSplit/StringsByteSplit/strided_slice"]}}, {"name": "StringsByteSplit/StringsByteSplit/strided_slice_1/stack", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "_output_shapes": {"list": {"shape": [{"dim": [{"size": "1"}]}]}}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "1"}]}, "intVal": [0]}}}, "experimentalDebugInfo": {"originalNodeNames": ["StringsByteSplit/StringsByteSplit/strided_slice_1/stack"]}}, {"name": "StringsByteSplit/StringsByteSplit/strided_slice_1/stack_1", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "1"}]}, "intVal": [1]}}, "_output_shapes": {"list": {"shape": [{"dim": [{"size": "1"}]}]}}}, "experimentalDebugInfo": {"originalNodeNames": ["StringsByteSplit/StringsByteSplit/strided_slice_1/stack_1"]}}, {"name": "StringsByteSplit/StringsByteSplit/strided_slice_1/stack_2", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "1"}]}, "intVal": [1]}}, "_output_shapes": {"list": {"shape": [{"dim": [{"size": "1"}]}]}}, "dtype": {"type": "DT_INT32"}}, "experimentalDebugInfo": {"originalNodeNames": ["StringsByteSplit/StringsByteSplit/strided_slice_1/stack_2"]}}, {"name": "StringsByteSplit/StringsByteSplit/strided_slice_1", "op": "StridedSlice", "input": ["StringsByteSplit/StringsByteSplit/StringSplit:shape:0", "StringsByteSplit/StringsByteSplit/strided_slice_1/stack:output:0", "StringsByteSplit/StringsByteSplit/strided_slice_1/stack_1:output:0", "StringsByteSplit/StringsByteSplit/strided_slice_1/stack_2:output:0"], "attr": {"_output_shapes": {"list": {"shape": [{}]}}, "T": {"type": "DT_INT64"}, "shrink_axis_mask": {"i": "1"}, "Index": {"type": "DT_INT32"}}, "experimentalDebugInfo": {"originalNodeNames": ["StringsByteSplit/StringsByteSplit/strided_slice_1"]}}, {"name": "StringsByteSplit/StringsByteSplit/RaggedFromValueRowIds/RowPartitionFromValueRowIds/Cast", "op": "Cast", "input": ["StringsByteSplit/StringsByteSplit/strided_slice:output:0"], "attr": {"SrcT": {"type": "DT_INT64"}, "_output_shapes": {"list": {"shape": [{"dim": [{"size": "-1"}]}]}}, "DstT": {"type": "DT_INT32"}}, "experimentalDebugInfo": {"originalNodeNames": ["StringsByteSplit/StringsByteSplit/RaggedFromValueRowIds/RowPartitionFromValueRowIds/Cast"]}}, {"name": "StringsByteSplit/StringsByteSplit/RaggedFromValueRowIds/RowPartitionFromValueRowIds/Cast_1", "op": "Cast", "input": ["StringsByteSplit/StringsByteSplit/strided_slice_1:output:0"], "attr": {"DstT": {"type": "DT_INT32"}, "SrcT": {"type": "DT_INT64"}, "_output_shapes": {"list": {"shape": [{}]}}}, "experimentalDebugInfo": {"originalNodeNames": ["StringsByteSplit/StringsByteSplit/RaggedFromValueRowIds/RowPartitionFromValueRowIds/Cast_1"]}}, {"name": "StringsByteSplit/StringsByteSplit/RaggedFromValueRowIds/RowPartitionFromValueRowIds/bincount/Shape", "op": "<PERSON><PERSON><PERSON>", "input": ["StringsByteSplit/StringsByteSplit/RaggedFromValueRowIds/RowPartitionFromValueRowIds/Cast:y:0"], "attr": {"_output_shapes": {"list": {"shape": [{"dim": [{"size": "1"}]}]}}, "T": {"type": "DT_INT32"}}, "experimentalDebugInfo": {"originalNodeNames": ["StringsByteSplit/StringsByteSplit/RaggedFromValueRowIds/RowPartitionFromValueRowIds/bincount/Shape"]}}, {"name": "StringsByteSplit/StringsByteSplit/RaggedFromValueRowIds/RowPartitionFromValueRowIds/bincount/Const", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "1"}]}, "intVal": [0]}}, "_output_shapes": {"list": {"shape": [{"dim": [{"size": "1"}]}]}}, "dtype": {"type": "DT_INT32"}}, "experimentalDebugInfo": {"originalNodeNames": ["StringsByteSplit/StringsByteSplit/RaggedFromValueRowIds/RowPartitionFromValueRowIds/bincount/Const"]}}, {"name": "StringsByteSplit/StringsByteSplit/RaggedFromValueRowIds/RowPartitionFromValueRowIds/bincount/Prod", "op": "Prod", "input": ["StringsByteSplit/StringsByteSplit/RaggedFromValueRowIds/RowPartitionFromValueRowIds/bincount/Shape:output:0", "StringsByteSplit/StringsByteSplit/RaggedFromValueRowIds/RowPartitionFromValueRowIds/bincount/Const:output:0"], "attr": {"_output_shapes": {"list": {"shape": [{}]}}, "T": {"type": "DT_INT32"}}, "experimentalDebugInfo": {"originalNodeNames": ["StringsByteSplit/StringsByteSplit/RaggedFromValueRowIds/RowPartitionFromValueRowIds/bincount/Prod"]}}, {"name": "StringsByteSplit/StringsByteSplit/RaggedFromValueRowIds/RowPartitionFromValueRowIds/bincount/Greater/y", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {}, "intVal": [0]}}, "_output_shapes": {"list": {"shape": [{}]}}}, "experimentalDebugInfo": {"originalNodeNames": ["StringsByteSplit/StringsByteSplit/RaggedFromValueRowIds/RowPartitionFromValueRowIds/bincount/Greater/y"]}}, {"name": "StringsByteSplit/StringsByteSplit/RaggedFromValueRowIds/RowPartitionFromValueRowIds/bincount/Greater", "op": "Greater", "input": ["StringsByteSplit/StringsByteSplit/RaggedFromValueRowIds/RowPartitionFromValueRowIds/bincount/Prod:output:0", "StringsByteSplit/StringsByteSplit/RaggedFromValueRowIds/RowPartitionFromValueRowIds/bincount/Greater/y:output:0"], "attr": {"T": {"type": "DT_INT32"}, "_output_shapes": {"list": {"shape": [{}]}}}, "experimentalDebugInfo": {"originalNodeNames": ["StringsByteSplit/StringsByteSplit/RaggedFromValueRowIds/RowPartitionFromValueRowIds/bincount/Greater"]}}, {"name": "StringsByteSplit/StringsByteSplit/RaggedFromValueRowIds/RowPartitionFromValueRowIds/bincount/Cast", "op": "Cast", "input": ["StringsByteSplit/StringsByteSplit/RaggedFromValueRowIds/RowPartitionFromValueRowIds/bincount/Greater:z:0"], "attr": {"SrcT": {"type": "DT_BOOL"}, "DstT": {"type": "DT_INT32"}, "_output_shapes": {"list": {"shape": [{}]}}}, "experimentalDebugInfo": {"originalNodeNames": ["StringsByteSplit/StringsByteSplit/RaggedFromValueRowIds/RowPartitionFromValueRowIds/bincount/Cast"]}}, {"name": "StringsByteSplit/StringsByteSplit/RaggedFromValueRowIds/RowPartitionFromValueRowIds/bincount/Const_1", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "1"}]}, "intVal": [0]}}, "_output_shapes": {"list": {"shape": [{"dim": [{"size": "1"}]}]}}}, "experimentalDebugInfo": {"originalNodeNames": ["StringsByteSplit/StringsByteSplit/RaggedFromValueRowIds/RowPartitionFromValueRowIds/bincount/Const_1"]}}, {"name": "StringsByteSplit/StringsByteSplit/RaggedFromValueRowIds/RowPartitionFromValueRowIds/bincount/Max", "op": "Max", "input": ["StringsByteSplit/StringsByteSplit/RaggedFromValueRowIds/RowPartitionFromValueRowIds/Cast:y:0", "StringsByteSplit/StringsByteSplit/RaggedFromValueRowIds/RowPartitionFromValueRowIds/bincount/Const_1:output:0"], "attr": {"T": {"type": "DT_INT32"}, "_output_shapes": {"list": {"shape": [{}]}}}, "experimentalDebugInfo": {"originalNodeNames": ["StringsByteSplit/StringsByteSplit/RaggedFromValueRowIds/RowPartitionFromValueRowIds/bincount/Max"]}}, {"name": "StringsByteSplit/StringsByteSplit/RaggedFromValueRowIds/RowPartitionFromValueRowIds/bincount/add/y", "op": "Const", "attr": {"_output_shapes": {"list": {"shape": [{}]}}, "dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {}, "intVal": [1]}}}, "experimentalDebugInfo": {"originalNodeNames": ["StringsByteSplit/StringsByteSplit/RaggedFromValueRowIds/RowPartitionFromValueRowIds/bincount/add/y"]}}, {"name": "StringsByteSplit/StringsByteSplit/RaggedFromValueRowIds/RowPartitionFromValueRowIds/bincount/add", "op": "AddV2", "input": ["StringsByteSplit/StringsByteSplit/RaggedFromValueRowIds/RowPartitionFromValueRowIds/bincount/Max:output:0", "StringsByteSplit/StringsByteSplit/RaggedFromValueRowIds/RowPartitionFromValueRowIds/bincount/add/y:output:0"], "attr": {"T": {"type": "DT_INT32"}, "_output_shapes": {"list": {"shape": [{}]}}}, "experimentalDebugInfo": {"originalNodeNames": ["StringsByteSplit/StringsByteSplit/RaggedFromValueRowIds/RowPartitionFromValueRowIds/bincount/add"]}}, {"name": "StringsByteSplit/StringsByteSplit/RaggedFromValueRowIds/RowPartitionFromValueRowIds/bincount/mul", "op": "<PERSON><PERSON>", "input": ["StringsByteSplit/StringsByteSplit/RaggedFromValueRowIds/RowPartitionFromValueRowIds/bincount/Cast:y:0", "StringsByteSplit/StringsByteSplit/RaggedFromValueRowIds/RowPartitionFromValueRowIds/bincount/add:z:0"], "attr": {"T": {"type": "DT_INT32"}, "_output_shapes": {"list": {"shape": [{}]}}}, "experimentalDebugInfo": {"originalNodeNames": ["StringsByteSplit/StringsByteSplit/RaggedFromValueRowIds/RowPartitionFromValueRowIds/bincount/mul"]}}, {"name": "StringsByteSplit/StringsByteSplit/RaggedFromValueRowIds/RowPartitionFromValueRowIds/bincount/Maximum", "op": "Maximum", "input": ["StringsByteSplit/StringsByteSplit/RaggedFromValueRowIds/RowPartitionFromValueRowIds/Cast_1:y:0", "StringsByteSplit/StringsByteSplit/RaggedFromValueRowIds/RowPartitionFromValueRowIds/bincount/mul:z:0"], "attr": {"_output_shapes": {"list": {"shape": [{}]}}, "T": {"type": "DT_INT32"}}, "experimentalDebugInfo": {"originalNodeNames": ["StringsByteSplit/StringsByteSplit/RaggedFromValueRowIds/RowPartitionFromValueRowIds/bincount/Maximum"]}}, {"name": "StringsByteSplit/StringsByteSplit/RaggedFromValueRowIds/RowPartitionFromValueRowIds/bincount/Minimum", "op": "Minimum", "input": ["StringsByteSplit/StringsByteSplit/RaggedFromValueRowIds/RowPartitionFromValueRowIds/Cast_1:y:0", "StringsByteSplit/StringsByteSplit/RaggedFromValueRowIds/RowPartitionFromValueRowIds/bincount/Maximum:z:0"], "attr": {"_output_shapes": {"list": {"shape": [{}]}}, "T": {"type": "DT_INT32"}}, "experimentalDebugInfo": {"originalNodeNames": ["StringsByteSplit/StringsByteSplit/RaggedFromValueRowIds/RowPartitionFromValueRowIds/bincount/Minimum"]}}, {"name": "StringsByteSplit/StringsByteSplit/RaggedFromValueRowIds/RowPartitionFromValueRowIds/bincount/Const_2", "op": "Const", "attr": {"dtype": {"type": "DT_INT64"}, "_output_shapes": {"list": {"shape": [{"dim": [{}]}]}}, "value": {"tensor": {"dtype": "DT_INT64", "tensorShape": {"dim": [{}]}}}}, "experimentalDebugInfo": {"originalNodeNames": ["StringsByteSplit/StringsByteSplit/RaggedFromValueRowIds/RowPartitionFromValueRowIds/bincount/Const_2"]}}, {"name": "StringsByteSplit/StringsByteSplit/RaggedFromValueRowIds/RowPartitionFromValueRowIds/bincount/Bincount", "op": "Bincount", "input": ["StringsByteSplit/StringsByteSplit/RaggedFromValueRowIds/RowPartitionFromValueRowIds/Cast:y:0", "StringsByteSplit/StringsByteSplit/RaggedFromValueRowIds/RowPartitionFromValueRowIds/bincount/Minimum:z:0", "StringsByteSplit/StringsByteSplit/RaggedFromValueRowIds/RowPartitionFromValueRowIds/bincount/Const_2:output:0"], "attr": {"_output_shapes": {"list": {"shape": [{"dim": [{"size": "-1"}]}]}}, "T": {"type": "DT_INT64"}}, "experimentalDebugInfo": {"originalNodeNames": ["StringsByteSplit/StringsByteSplit/RaggedFromValueRowIds/RowPartitionFromValueRowIds/bincount/Bincount"]}}, {"name": "StringsByteSplit/StringsByteSplit/RaggedFromValueRowIds/RowPartitionFromValueRowIds/Cumsum/axis", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {}, "intVal": [0]}}, "_output_shapes": {"list": {"shape": [{}]}}}, "experimentalDebugInfo": {"originalNodeNames": ["StringsByteSplit/StringsByteSplit/RaggedFromValueRowIds/RowPartitionFromValueRowIds/Cumsum/axis"]}}, {"name": "StringsByteSplit/StringsByteSplit/RaggedFromValueRowIds/RowPartitionFromValueRowIds/Cumsum", "op": "Cumsum", "input": ["StringsByteSplit/StringsByteSplit/RaggedFromValueRowIds/RowPartitionFromValueRowIds/bincount/Bincount:bins:0", "StringsByteSplit/StringsByteSplit/RaggedFromValueRowIds/RowPartitionFromValueRowIds/Cumsum/axis:output:0"], "attr": {"_output_shapes": {"list": {"shape": [{"dim": [{"size": "-1"}]}]}}, "T": {"type": "DT_INT64"}}, "experimentalDebugInfo": {"originalNodeNames": ["StringsByteSplit/StringsByteSplit/RaggedFromValueRowIds/RowPartitionFromValueRowIds/Cumsum"]}}, {"name": "StringsByteSplit/StringsByteSplit/RaggedFromValueRowIds/RowPartitionFromValueRowIds/concat/values_0", "op": "Const", "attr": {"dtype": {"type": "DT_INT64"}, "value": {"tensor": {"dtype": "DT_INT64", "tensorShape": {"dim": [{"size": "1"}]}, "int64Val": ["0"]}}, "_output_shapes": {"list": {"shape": [{"dim": [{"size": "1"}]}]}}}, "experimentalDebugInfo": {"originalNodeNames": ["StringsByteSplit/StringsByteSplit/RaggedFromValueRowIds/RowPartitionFromValueRowIds/concat/values_0"]}}, {"name": "StringsByteSplit/StringsByteSplit/RaggedFromValueRowIds/RowPartitionFromValueRowIds/concat/axis", "op": "Const", "attr": {"_output_shapes": {"list": {"shape": [{}]}}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {}, "intVal": [0]}}, "dtype": {"type": "DT_INT32"}}, "experimentalDebugInfo": {"originalNodeNames": ["StringsByteSplit/StringsByteSplit/RaggedFromValueRowIds/RowPartitionFromValueRowIds/concat/axis"]}}, {"name": "StringsByteSplit/StringsByteSplit/RaggedFromValueRowIds/RowPartitionFromValueRowIds/concat", "op": "ConcatV2", "input": ["StringsByteSplit/StringsByteSplit/RaggedFromValueRowIds/RowPartitionFromValueRowIds/concat/values_0:output:0", "StringsByteSplit/StringsByteSplit/RaggedFromValueRowIds/RowPartitionFromValueRowIds/Cumsum:out:0", "StringsByteSplit/StringsByteSplit/RaggedFromValueRowIds/RowPartitionFromValueRowIds/concat/axis:output:0"], "attr": {"_output_shapes": {"list": {"shape": [{"dim": [{"size": "-1"}]}]}}, "N": {"i": "2"}, "T": {"type": "DT_INT64"}}, "experimentalDebugInfo": {"originalNodeNames": ["StringsByteSplit/StringsByteSplit/RaggedFromValueRowIds/RowPartitionFromValueRowIds/concat"]}}, {"name": "StringsByteSplit/RaggedGetItem/strided_slice/stack", "op": "Const", "attr": {"_output_shapes": {"list": {"shape": [{"dim": [{"size": "1"}]}]}}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "1"}]}, "intVal": [0]}}, "dtype": {"type": "DT_INT32"}}, "experimentalDebugInfo": {"originalNodeNames": ["StringsByteSplit/RaggedGetItem/strided_slice/stack"]}}, {"name": "StringsByteSplit/RaggedGetItem/strided_slice/stack_1", "op": "Const", "attr": {"_output_shapes": {"list": {"shape": [{"dim": [{"size": "1"}]}]}}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "1"}]}, "intVal": [-1]}}, "dtype": {"type": "DT_INT32"}}, "experimentalDebugInfo": {"originalNodeNames": ["StringsByteSplit/RaggedGetItem/strided_slice/stack_1"]}}, {"name": "StringsByteSplit/RaggedGetItem/strided_slice/stack_2", "op": "Const", "attr": {"_output_shapes": {"list": {"shape": [{"dim": [{"size": "1"}]}]}}, "dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "1"}]}, "intVal": [1]}}}, "experimentalDebugInfo": {"originalNodeNames": ["StringsByteSplit/RaggedGetItem/strided_slice/stack_2"]}}, {"name": "StringsByteSplit/RaggedGetItem/strided_slice", "op": "StridedSlice", "input": ["StringsByteSplit/StringsByteSplit/RaggedFromValueRowIds/RowPartitionFromValueRowIds/concat:output:0", "StringsByteSplit/RaggedGetItem/strided_slice/stack:output:0", "StringsByteSplit/RaggedGetItem/strided_slice/stack_1:output:0", "StringsByteSplit/RaggedGetItem/strided_slice/stack_2:output:0"], "attr": {"_output_shapes": {"list": {"shape": [{"dim": [{"size": "-1"}]}]}}, "begin_mask": {"i": "1"}, "Index": {"type": "DT_INT32"}, "T": {"type": "DT_INT64"}}, "experimentalDebugInfo": {"originalNodeNames": ["StringsByteSplit/RaggedGetItem/strided_slice"]}}, {"name": "StringsByteSplit/RaggedGetItem/strided_slice_1/stack", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "1"}]}, "intVal": [1]}}, "_output_shapes": {"list": {"shape": [{"dim": [{"size": "1"}]}]}}, "dtype": {"type": "DT_INT32"}}, "experimentalDebugInfo": {"originalNodeNames": ["StringsByteSplit/RaggedGetItem/strided_slice_1/stack"]}}, {"name": "StringsByteSplit/RaggedGetItem/strided_slice_1/stack_1", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "1"}]}, "intVal": [0]}}, "_output_shapes": {"list": {"shape": [{"dim": [{"size": "1"}]}]}}}, "experimentalDebugInfo": {"originalNodeNames": ["StringsByteSplit/RaggedGetItem/strided_slice_1/stack_1"]}}, {"name": "StringsByteSplit/RaggedGetItem/strided_slice_1/stack_2", "op": "Const", "attr": {"_output_shapes": {"list": {"shape": [{"dim": [{"size": "1"}]}]}}, "dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "1"}]}, "intVal": [1]}}}, "experimentalDebugInfo": {"originalNodeNames": ["StringsByteSplit/RaggedGetItem/strided_slice_1/stack_2"]}}, {"name": "StringsByteSplit/RaggedGetItem/strided_slice_1", "op": "StridedSlice", "input": ["StringsByteSplit/StringsByteSplit/RaggedFromValueRowIds/RowPartitionFromValueRowIds/concat:output:0", "StringsByteSplit/RaggedGetItem/strided_slice_1/stack:output:0", "StringsByteSplit/RaggedGetItem/strided_slice_1/stack_1:output:0", "StringsByteSplit/RaggedGetItem/strided_slice_1/stack_2:output:0"], "attr": {"end_mask": {"i": "1"}, "T": {"type": "DT_INT64"}, "Index": {"type": "DT_INT32"}, "_output_shapes": {"list": {"shape": [{"dim": [{"size": "-1"}]}]}}}, "experimentalDebugInfo": {"originalNodeNames": ["StringsByteSplit/RaggedGetItem/strided_slice_1"]}}, {"name": "StringsByteSplit/RaggedGetItem/strided_slice_2/stack", "op": "Const", "attr": {"_output_shapes": {"list": {"shape": [{"dim": [{"size": "1"}]}]}}, "dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "1"}]}, "intVal": [0]}}}, "experimentalDebugInfo": {"originalNodeNames": ["StringsByteSplit/RaggedGetItem/strided_slice_2/stack"]}}, {"name": "StringsByteSplit/RaggedGetItem/strided_slice_2/stack_1", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "1"}]}, "intVal": [1]}}, "_output_shapes": {"list": {"shape": [{"dim": [{"size": "1"}]}]}}}, "experimentalDebugInfo": {"originalNodeNames": ["StringsByteSplit/RaggedGetItem/strided_slice_2/stack_1"]}}, {"name": "StringsByteSplit/RaggedGetItem/strided_slice_2/stack_2", "op": "Const", "attr": {"_output_shapes": {"list": {"shape": [{"dim": [{"size": "1"}]}]}}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "1"}]}, "intVal": [1]}}, "dtype": {"type": "DT_INT32"}}, "experimentalDebugInfo": {"originalNodeNames": ["StringsByteSplit/RaggedGetItem/strided_slice_2/stack_2"]}}, {"name": "StringsByteSplit/RaggedGetItem/strided_slice_2", "op": "StridedSlice", "input": ["StringsByteSplit/RaggedGetItem/strided_slice:output:0", "StringsByteSplit/RaggedGetItem/strided_slice_2/stack:output:0", "StringsByteSplit/RaggedGetItem/strided_slice_2/stack_1:output:0", "StringsByteSplit/RaggedGetItem/strided_slice_2/stack_2:output:0"], "attr": {"Index": {"type": "DT_INT32"}, "T": {"type": "DT_INT64"}, "_output_shapes": {"list": {"shape": [{}]}}, "shrink_axis_mask": {"i": "1"}}, "experimentalDebugInfo": {"originalNodeNames": ["StringsByteSplit/RaggedGetItem/strided_slice_2"]}}, {"name": "StringsByteSplit/RaggedGetItem/strided_slice_3/stack", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "1"}]}, "intVal": [0]}}, "_output_shapes": {"list": {"shape": [{"dim": [{"size": "1"}]}]}}, "dtype": {"type": "DT_INT32"}}, "experimentalDebugInfo": {"originalNodeNames": ["StringsByteSplit/RaggedGetItem/strided_slice_3/stack"]}}, {"name": "StringsByteSplit/RaggedGetItem/strided_slice_3/stack_1", "op": "Const", "attr": {"_output_shapes": {"list": {"shape": [{"dim": [{"size": "1"}]}]}}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "1"}]}, "intVal": [1]}}, "dtype": {"type": "DT_INT32"}}, "experimentalDebugInfo": {"originalNodeNames": ["StringsByteSplit/RaggedGetItem/strided_slice_3/stack_1"]}}, {"name": "StringsByteSplit/RaggedGetItem/strided_slice_3/stack_2", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "1"}]}, "intVal": [1]}}, "_output_shapes": {"list": {"shape": [{"dim": [{"size": "1"}]}]}}}, "experimentalDebugInfo": {"originalNodeNames": ["StringsByteSplit/RaggedGetItem/strided_slice_3/stack_2"]}}, {"name": "StringsByteSplit/RaggedGetItem/strided_slice_3", "op": "StridedSlice", "input": ["StringsByteSplit/RaggedGetItem/strided_slice_1:output:0", "StringsByteSplit/RaggedGetItem/strided_slice_3/stack:output:0", "StringsByteSplit/RaggedGetItem/strided_slice_3/stack_1:output:0", "StringsByteSplit/RaggedGetItem/strided_slice_3/stack_2:output:0"], "attr": {"Index": {"type": "DT_INT32"}, "shrink_axis_mask": {"i": "1"}, "_output_shapes": {"list": {"shape": [{}]}}, "T": {"type": "DT_INT64"}}, "experimentalDebugInfo": {"originalNodeNames": ["StringsByteSplit/RaggedGetItem/strided_slice_3"]}}, {"name": "StringsByteSplit/RaggedGetItem/strided_slice_4/stack", "op": "Pack", "input": ["StringsByteSplit/RaggedGetItem/strided_slice_2:output:0"], "attr": {"_output_shapes": {"list": {"shape": [{"dim": [{"size": "1"}]}]}}, "T": {"type": "DT_INT64"}, "N": {"i": "1"}}, "experimentalDebugInfo": {"originalNodeNames": ["StringsByteSplit/RaggedGetItem/strided_slice_4/stack"]}}, {"name": "StringsByteSplit/RaggedGetItem/strided_slice_4/stack_1", "op": "Pack", "input": ["StringsByteSplit/RaggedGetItem/strided_slice_3:output:0"], "attr": {"T": {"type": "DT_INT64"}, "N": {"i": "1"}, "_output_shapes": {"list": {"shape": [{"dim": [{"size": "1"}]}]}}}, "experimentalDebugInfo": {"originalNodeNames": ["StringsByteSplit/RaggedGetItem/strided_slice_4/stack_1"]}}, {"name": "StringsByteSplit/RaggedGetItem/strided_slice_4/stack_2", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "1"}]}, "intVal": [1]}}, "_output_shapes": {"list": {"shape": [{"dim": [{"size": "1"}]}]}}}, "experimentalDebugInfo": {"originalNodeNames": ["StringsByteSplit/RaggedGetItem/strided_slice_4/stack_2"]}}, {"name": "StringsByteSplit/RaggedGetItem/strided_slice_4/Cast", "op": "Cast", "input": ["StringsByteSplit/RaggedGetItem/strided_slice_4/stack_2:output:0"], "attr": {"DstT": {"type": "DT_INT64"}, "SrcT": {"type": "DT_INT32"}, "_output_shapes": {"list": {"shape": [{"dim": [{"size": "1"}]}]}}}, "experimentalDebugInfo": {"originalNodeNames": ["StringsByteSplit/RaggedGetItem/strided_slice_4/Cast"]}}, {"name": "StringsByteSplit/RaggedGetItem/strided_slice_4", "op": "StridedSlice", "input": ["StringsByteSplit/StringsByteSplit/StringSplit:values:0", "StringsByteSplit/RaggedGetItem/strided_slice_4/stack:output:0", "StringsByteSplit/RaggedGetItem/strided_slice_4/stack_1:output:0", "StringsByteSplit/RaggedGetItem/strided_slice_4/Cast:y:0"], "attr": {"T": {"type": "DT_STRING"}, "Index": {"type": "DT_INT64"}, "_output_shapes": {"list": {"shape": [{"dim": [{"size": "-1"}]}]}}}, "experimentalDebugInfo": {"originalNodeNames": ["StringsByteSplit/RaggedGetItem/strided_slice_4"]}}, {"name": "StringsByteSplit/RaggedGetItem/strided_slice_5/Const", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{}]}}}, "_output_shapes": {"list": {"shape": [{"dim": [{}]}]}}, "dtype": {"type": "DT_INT32"}}, "experimentalDebugInfo": {"originalNodeNames": ["StringsByteSplit/RaggedGetItem/strided_slice_5/Const"]}}, {"name": "StringsByteSplit/RaggedGetItem/strided_slice_5", "op": "StridedSlice", "input": ["StringsByteSplit/RaggedGetItem/strided_slice_4:output:0", "StringsByteSplit/RaggedGetItem/strided_slice_5/Const:output:0", "StringsByteSplit/RaggedGetItem/strided_slice_5/Const:output:0", "StringsByteSplit/RaggedGetItem/strided_slice_5/Const:output:0"], "attr": {"_output_shapes": {"list": {"shape": [{"dim": [{"size": "-1"}]}]}}, "T": {"type": "DT_STRING"}, "Index": {"type": "DT_INT32"}}, "experimentalDebugInfo": {"originalNodeNames": ["StringsByteSplit/RaggedGetItem/strided_slice_5"]}}, {"name": "StringNGrams/Shape", "op": "<PERSON><PERSON><PERSON>", "input": ["StringsByteSplit/RaggedGetItem/strided_slice_5:output:0"], "attr": {"T": {"type": "DT_STRING"}, "_output_shapes": {"list": {"shape": [{"dim": [{"size": "1"}]}]}}}, "experimentalDebugInfo": {"originalNodeNames": ["StringNGrams/Shape"]}}, {"name": "StringNGrams/strided_slice/stack", "op": "Const", "attr": {"_output_shapes": {"list": {"shape": [{"dim": [{"size": "1"}]}]}}, "dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "1"}]}, "intVal": [0]}}}, "experimentalDebugInfo": {"originalNodeNames": ["StringNGrams/strided_slice/stack"]}}, {"name": "StringNGrams/strided_slice/stack_1", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "1"}]}, "intVal": [-1]}}, "_output_shapes": {"list": {"shape": [{"dim": [{"size": "1"}]}]}}}, "experimentalDebugInfo": {"originalNodeNames": ["StringNGrams/strided_slice/stack_1"]}}, {"name": "StringNGrams/strided_slice/stack_2", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "_output_shapes": {"list": {"shape": [{"dim": [{"size": "1"}]}]}}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "1"}]}, "intVal": [1]}}}, "experimentalDebugInfo": {"originalNodeNames": ["StringNGrams/strided_slice/stack_2"]}}, {"name": "StringNGrams/strided_slice", "op": "StridedSlice", "input": ["StringNGrams/Shape:output:0", "StringNGrams/strided_slice/stack:output:0", "StringNGrams/strided_slice/stack_1:output:0", "StringNGrams/strided_slice/stack_2:output:0"], "attr": {"Index": {"type": "DT_INT32"}, "begin_mask": {"i": "1"}, "_output_shapes": {"list": {"shape": [{"dim": [{}]}]}}, "T": {"type": "DT_INT32"}}, "experimentalDebugInfo": {"originalNodeNames": ["StringNGrams/strided_slice"]}}, {"name": "StringNGrams/concat/values_1", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "1"}]}, "intVal": [-1]}}, "_output_shapes": {"list": {"shape": [{"dim": [{"size": "1"}]}]}}}, "experimentalDebugInfo": {"originalNodeNames": ["StringNGrams/concat/values_1"]}}, {"name": "StringNGrams/concat/axis", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {}, "intVal": [0]}}, "_output_shapes": {"list": {"shape": [{}]}}}, "experimentalDebugInfo": {"originalNodeNames": ["StringNGrams/concat/axis"]}}, {"name": "StringNGrams/concat", "op": "ConcatV2", "input": ["StringNGrams/strided_slice:output:0", "StringNGrams/concat/values_1:output:0", "StringNGrams/concat/axis:output:0"], "attr": {"T": {"type": "DT_INT32"}, "_output_shapes": {"list": {"shape": [{"dim": [{"size": "1"}]}]}}, "N": {"i": "2"}}, "experimentalDebugInfo": {"originalNodeNames": ["StringNGrams/concat"]}}, {"name": "StringNGrams/RaggedFromRowStarts/Shape", "op": "<PERSON><PERSON><PERSON>", "input": ["StringsByteSplit/RaggedGetItem/strided_slice_5:output:0"], "attr": {"_output_shapes": {"list": {"shape": [{"dim": [{"size": "1"}]}]}}, "T": {"type": "DT_STRING"}}, "experimentalDebugInfo": {"originalNodeNames": ["StringNGrams/RaggedFromRowStarts/Shape"]}}, {"name": "StringNGrams/RaggedFromRowStarts/strided_slice/stack", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "1"}]}, "intVal": [0]}}, "_output_shapes": {"list": {"shape": [{"dim": [{"size": "1"}]}]}}, "dtype": {"type": "DT_INT32"}}, "experimentalDebugInfo": {"originalNodeNames": ["StringNGrams/RaggedFromRowStarts/strided_slice/stack"]}}, {"name": "StringNGrams/RaggedFromRowStarts/strided_slice/stack_1", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "_output_shapes": {"list": {"shape": [{"dim": [{"size": "1"}]}]}}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "1"}]}, "intVal": [1]}}}, "experimentalDebugInfo": {"originalNodeNames": ["StringNGrams/RaggedFromRowStarts/strided_slice/stack_1"]}}, {"name": "StringNGrams/RaggedFromRowStarts/strided_slice/stack_2", "op": "Const", "attr": {"_output_shapes": {"list": {"shape": [{"dim": [{"size": "1"}]}]}}, "dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "1"}]}, "intVal": [1]}}}, "experimentalDebugInfo": {"originalNodeNames": ["StringNGrams/RaggedFromRowStarts/strided_slice/stack_2"]}}, {"name": "StringNGrams/RaggedFromRowStarts/strided_slice", "op": "StridedSlice", "input": ["StringNGrams/RaggedFromRowStarts/Shape:output:0", "StringNGrams/RaggedFromRowStarts/strided_slice/stack:output:0", "StringNGrams/RaggedFromRowStarts/strided_slice/stack_1:output:0", "StringNGrams/RaggedFromRowStarts/strided_slice/stack_2:output:0"], "attr": {"shrink_axis_mask": {"i": "1"}, "T": {"type": "DT_INT32"}, "Index": {"type": "DT_INT32"}, "_output_shapes": {"list": {"shape": [{}]}}}, "experimentalDebugInfo": {"originalNodeNames": ["StringNGrams/RaggedFromRowStarts/strided_slice"]}}, {"name": "StringNGrams/RaggedFromRowStarts/RowPartitionFromRowStarts/row_starts", "op": "Const", "attr": {"_output_shapes": {"list": {"shape": [{"dim": [{"size": "1"}]}]}}, "value": {"tensor": {"dtype": "DT_INT64", "tensorShape": {"dim": [{"size": "1"}]}, "int64Val": ["0"]}}, "dtype": {"type": "DT_INT64"}}, "experimentalDebugInfo": {"originalNodeNames": ["StringNGrams/RaggedFromRowStarts/RowPartitionFromRowStarts/row_starts"]}}, {"name": "StringNGrams/RaggedFromRowStarts/RowPartitionFromRowStarts/Cast", "op": "Cast", "input": ["StringNGrams/RaggedFromRowStarts/strided_slice:output:0"], "attr": {"SrcT": {"type": "DT_INT32"}, "DstT": {"type": "DT_INT64"}, "_output_shapes": {"list": {"shape": [{}]}}}, "experimentalDebugInfo": {"originalNodeNames": ["StringNGrams/RaggedFromRowStarts/RowPartitionFromRowStarts/Cast"]}}, {"name": "StringNGrams/RaggedFromRowStarts/RowPartitionFromRowStarts/concat/values_1", "op": "Pack", "input": ["StringNGrams/RaggedFromRowStarts/RowPartitionFromRowStarts/Cast:y:0"], "attr": {"N": {"i": "1"}, "T": {"type": "DT_INT64"}, "_output_shapes": {"list": {"shape": [{"dim": [{"size": "1"}]}]}}}, "experimentalDebugInfo": {"originalNodeNames": ["StringNGrams/RaggedFromRowStarts/RowPartitionFromRowStarts/concat/values_1"]}}, {"name": "StringNGrams/RaggedFromRowStarts/RowPartitionFromRowStarts/concat/axis", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {}, "intVal": [0]}}, "dtype": {"type": "DT_INT32"}, "_output_shapes": {"list": {"shape": [{}]}}}, "experimentalDebugInfo": {"originalNodeNames": ["StringNGrams/RaggedFromRowStarts/RowPartitionFromRowStarts/concat/axis"]}}, {"name": "StringNGrams/RaggedFromRowStarts/RowPartitionFromRowStarts/concat", "op": "ConcatV2", "input": ["StringNGrams/RaggedFromRowStarts/RowPartitionFromRowStarts/row_starts:output:0", "StringNGrams/RaggedFromRowStarts/RowPartitionFromRowStarts/concat/values_1:output:0", "StringNGrams/RaggedFromRowStarts/RowPartitionFromRowStarts/concat/axis:output:0"], "attr": {"T": {"type": "DT_INT64"}, "_output_shapes": {"list": {"shape": [{"dim": [{"size": "2"}]}]}}, "N": {"i": "2"}}, "experimentalDebugInfo": {"originalNodeNames": ["StringNGrams/RaggedFromRowStarts/RowPartitionFromRowStarts/concat"]}}, {"name": "StringNGrams/StringNGrams/StringNGrams", "op": "StringNGrams", "input": ["StringsByteSplit/RaggedGetItem/strided_slice_5:output:0", "StringNGrams/RaggedFromRowStarts/RowPartitionFromRowStarts/concat:output:0"], "attr": {"preserve_short_sequences": {"b": false}, "_output_shapes": {"list": {"shape": [{"dim": [{"size": "-1"}]}, {"dim": [{"size": "2"}]}]}}, "left_pad": {"s": ""}, "right_pad": {"s": ""}, "separator": {"s": "IA=="}, "pad_width": {"i": "0"}, "ngram_widths": {"list": {"i": ["2"]}}}, "experimentalDebugInfo": {"originalNodeNames": ["StringNGrams/StringNGrams/StringNGrams"]}}, {"name": "StringNGrams/RaggedGetItem/strided_slice/stack", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "1"}]}, "intVal": [0]}}, "dtype": {"type": "DT_INT32"}, "_output_shapes": {"list": {"shape": [{"dim": [{"size": "1"}]}]}}}, "experimentalDebugInfo": {"originalNodeNames": ["StringNGrams/RaggedGetItem/strided_slice/stack"]}}, {"name": "StringNGrams/RaggedGetItem/strided_slice/stack_1", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "_output_shapes": {"list": {"shape": [{"dim": [{"size": "1"}]}]}}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "1"}]}, "intVal": [-1]}}}, "experimentalDebugInfo": {"originalNodeNames": ["StringNGrams/RaggedGetItem/strided_slice/stack_1"]}}, {"name": "StringNGrams/RaggedGetItem/strided_slice/stack_2", "op": "Const", "attr": {"_output_shapes": {"list": {"shape": [{"dim": [{"size": "1"}]}]}}, "dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "1"}]}, "intVal": [1]}}}, "experimentalDebugInfo": {"originalNodeNames": ["StringNGrams/RaggedGetItem/strided_slice/stack_2"]}}, {"name": "StringNGrams/RaggedGetItem/strided_slice", "op": "StridedSlice", "input": ["StringNGrams/StringNGrams/StringNGrams:ngrams_splits:0", "StringNGrams/RaggedGetItem/strided_slice/stack:output:0", "StringNGrams/RaggedGetItem/strided_slice/stack_1:output:0", "StringNGrams/RaggedGetItem/strided_slice/stack_2:output:0"], "attr": {"Index": {"type": "DT_INT32"}, "T": {"type": "DT_INT64"}, "begin_mask": {"i": "1"}, "_output_shapes": {"list": {"shape": [{"dim": [{"size": "1"}]}]}}}, "experimentalDebugInfo": {"originalNodeNames": ["StringNGrams/RaggedGetItem/strided_slice"]}}, {"name": "StringNGrams/RaggedGetItem/strided_slice_1/stack", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "_output_shapes": {"list": {"shape": [{"dim": [{"size": "1"}]}]}}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "1"}]}, "intVal": [1]}}}, "experimentalDebugInfo": {"originalNodeNames": ["StringNGrams/RaggedGetItem/strided_slice_1/stack"]}}, {"name": "StringNGrams/RaggedGetItem/strided_slice_1/stack_1", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "1"}]}, "intVal": [0]}}, "_output_shapes": {"list": {"shape": [{"dim": [{"size": "1"}]}]}}}, "experimentalDebugInfo": {"originalNodeNames": ["StringNGrams/RaggedGetItem/strided_slice_1/stack_1"]}}, {"name": "StringNGrams/RaggedGetItem/strided_slice_1/stack_2", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "1"}]}, "intVal": [1]}}, "_output_shapes": {"list": {"shape": [{"dim": [{"size": "1"}]}]}}}, "experimentalDebugInfo": {"originalNodeNames": ["StringNGrams/RaggedGetItem/strided_slice_1/stack_2"]}}, {"name": "StringNGrams/RaggedGetItem/strided_slice_1", "op": "StridedSlice", "input": ["StringNGrams/StringNGrams/StringNGrams:ngrams_splits:0", "StringNGrams/RaggedGetItem/strided_slice_1/stack:output:0", "StringNGrams/RaggedGetItem/strided_slice_1/stack_1:output:0", "StringNGrams/RaggedGetItem/strided_slice_1/stack_2:output:0"], "attr": {"Index": {"type": "DT_INT32"}, "T": {"type": "DT_INT64"}, "_output_shapes": {"list": {"shape": [{"dim": [{"size": "1"}]}]}}, "end_mask": {"i": "1"}}, "experimentalDebugInfo": {"originalNodeNames": ["StringNGrams/RaggedGetItem/strided_slice_1"]}}, {"name": "StringNGrams/RaggedGetItem/strided_slice_2/stack", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "1"}]}, "intVal": [0]}}, "dtype": {"type": "DT_INT32"}, "_output_shapes": {"list": {"shape": [{"dim": [{"size": "1"}]}]}}}, "experimentalDebugInfo": {"originalNodeNames": ["StringNGrams/RaggedGetItem/strided_slice_2/stack"]}}, {"name": "StringNGrams/RaggedGetItem/strided_slice_2/stack_1", "op": "Const", "attr": {"_output_shapes": {"list": {"shape": [{"dim": [{"size": "1"}]}]}}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "1"}]}, "intVal": [1]}}, "dtype": {"type": "DT_INT32"}}, "experimentalDebugInfo": {"originalNodeNames": ["StringNGrams/RaggedGetItem/strided_slice_2/stack_1"]}}, {"name": "StringNGrams/RaggedGetItem/strided_slice_2/stack_2", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "1"}]}, "intVal": [1]}}, "_output_shapes": {"list": {"shape": [{"dim": [{"size": "1"}]}]}}}, "experimentalDebugInfo": {"originalNodeNames": ["StringNGrams/RaggedGetItem/strided_slice_2/stack_2"]}}, {"name": "StringNGrams/RaggedGetItem/strided_slice_2", "op": "StridedSlice", "input": ["StringNGrams/RaggedGetItem/strided_slice:output:0", "StringNGrams/RaggedGetItem/strided_slice_2/stack:output:0", "StringNGrams/RaggedGetItem/strided_slice_2/stack_1:output:0", "StringNGrams/RaggedGetItem/strided_slice_2/stack_2:output:0"], "attr": {"Index": {"type": "DT_INT32"}, "T": {"type": "DT_INT64"}, "shrink_axis_mask": {"i": "1"}, "_output_shapes": {"list": {"shape": [{}]}}}, "experimentalDebugInfo": {"originalNodeNames": ["StringNGrams/RaggedGetItem/strided_slice_2"]}}, {"name": "StringNGrams/RaggedGetItem/strided_slice_3/stack", "op": "Const", "attr": {"_output_shapes": {"list": {"shape": [{"dim": [{"size": "1"}]}]}}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "1"}]}, "intVal": [0]}}, "dtype": {"type": "DT_INT32"}}, "experimentalDebugInfo": {"originalNodeNames": ["StringNGrams/RaggedGetItem/strided_slice_3/stack"]}}, {"name": "StringNGrams/RaggedGetItem/strided_slice_3/stack_1", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "1"}]}, "intVal": [1]}}, "_output_shapes": {"list": {"shape": [{"dim": [{"size": "1"}]}]}}, "dtype": {"type": "DT_INT32"}}, "experimentalDebugInfo": {"originalNodeNames": ["StringNGrams/RaggedGetItem/strided_slice_3/stack_1"]}}, {"name": "StringNGrams/RaggedGetItem/strided_slice_3/stack_2", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "1"}]}, "intVal": [1]}}, "_output_shapes": {"list": {"shape": [{"dim": [{"size": "1"}]}]}}}, "experimentalDebugInfo": {"originalNodeNames": ["StringNGrams/RaggedGetItem/strided_slice_3/stack_2"]}}, {"name": "StringNGrams/RaggedGetItem/strided_slice_3", "op": "StridedSlice", "input": ["StringNGrams/RaggedGetItem/strided_slice_1:output:0", "StringNGrams/RaggedGetItem/strided_slice_3/stack:output:0", "StringNGrams/RaggedGetItem/strided_slice_3/stack_1:output:0", "StringNGrams/RaggedGetItem/strided_slice_3/stack_2:output:0"], "attr": {"T": {"type": "DT_INT64"}, "shrink_axis_mask": {"i": "1"}, "Index": {"type": "DT_INT32"}, "_output_shapes": {"list": {"shape": [{}]}}}, "experimentalDebugInfo": {"originalNodeNames": ["StringNGrams/RaggedGetItem/strided_slice_3"]}}, {"name": "StringNGrams/RaggedGetItem/strided_slice_4/stack", "op": "Pack", "input": ["StringNGrams/RaggedGetItem/strided_slice_2:output:0"], "attr": {"T": {"type": "DT_INT64"}, "_output_shapes": {"list": {"shape": [{"dim": [{"size": "1"}]}]}}, "N": {"i": "1"}}, "experimentalDebugInfo": {"originalNodeNames": ["StringNGrams/RaggedGetItem/strided_slice_4/stack"]}}, {"name": "StringNGrams/RaggedGetItem/strided_slice_4/stack_1", "op": "Pack", "input": ["StringNGrams/RaggedGetItem/strided_slice_3:output:0"], "attr": {"T": {"type": "DT_INT64"}, "_output_shapes": {"list": {"shape": [{"dim": [{"size": "1"}]}]}}, "N": {"i": "1"}}, "experimentalDebugInfo": {"originalNodeNames": ["StringNGrams/RaggedGetItem/strided_slice_4/stack_1"]}}, {"name": "StringNGrams/RaggedGetItem/strided_slice_4/stack_2", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "1"}]}, "intVal": [1]}}, "_output_shapes": {"list": {"shape": [{"dim": [{"size": "1"}]}]}}}, "experimentalDebugInfo": {"originalNodeNames": ["StringNGrams/RaggedGetItem/strided_slice_4/stack_2"]}}, {"name": "StringNGrams/RaggedGetItem/strided_slice_4/Cast", "op": "Cast", "input": ["StringNGrams/RaggedGetItem/strided_slice_4/stack_2:output:0"], "attr": {"_output_shapes": {"list": {"shape": [{"dim": [{"size": "1"}]}]}}, "DstT": {"type": "DT_INT64"}, "SrcT": {"type": "DT_INT32"}}, "experimentalDebugInfo": {"originalNodeNames": ["StringNGrams/RaggedGetItem/strided_slice_4/Cast"]}}, {"name": "StringNGrams/RaggedGetItem/strided_slice_4", "op": "StridedSlice", "input": ["StringNGrams/StringNGrams/StringNGrams:ngrams:0", "StringNGrams/RaggedGetItem/strided_slice_4/stack:output:0", "StringNGrams/RaggedGetItem/strided_slice_4/stack_1:output:0", "StringNGrams/RaggedGetItem/strided_slice_4/Cast:y:0"], "attr": {"T": {"type": "DT_STRING"}, "Index": {"type": "DT_INT64"}, "_output_shapes": {"list": {"shape": [{"dim": [{"size": "-1"}]}]}}}, "experimentalDebugInfo": {"originalNodeNames": ["StringNGrams/RaggedGetItem/strided_slice_4"]}}, {"name": "StringNGrams/RaggedGetItem/strided_slice_5/Const", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{}]}}}, "_output_shapes": {"list": {"shape": [{"dim": [{}]}]}}}, "experimentalDebugInfo": {"originalNodeNames": ["StringNGrams/RaggedGetItem/strided_slice_5/Const"]}}, {"name": "StringNGrams/RaggedGetItem/strided_slice_5", "op": "StridedSlice", "input": ["StringNGrams/RaggedGetItem/strided_slice_4:output:0", "StringNGrams/RaggedGetItem/strided_slice_5/Const:output:0", "StringNGrams/RaggedGetItem/strided_slice_5/Const:output:0", "StringNGrams/RaggedGetItem/strided_slice_5/Const:output:0"], "attr": {"T": {"type": "DT_STRING"}, "Index": {"type": "DT_INT32"}, "_output_shapes": {"list": {"shape": [{"dim": [{"size": "-1"}]}]}}}, "experimentalDebugInfo": {"originalNodeNames": ["StringNGrams/RaggedGetItem/strided_slice_5"]}}, {"name": "concat/axis", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "_output_shapes": {"list": {"shape": [{}]}}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {}, "intVal": [0]}}}, "experimentalDebugInfo": {"originalNodeNames": ["concat/axis"]}}, {"name": "concat", "op": "ConcatV2", "input": ["StringNGrams/RaggedGetItem/strided_slice_5:output:0", "Const:output:0", "concat/axis:output:0"], "attr": {"_output_shapes": {"list": {"shape": [{"dim": [{"size": "-1"}]}]}}, "T": {"type": "DT_STRING"}, "N": {"i": "2"}}, "experimentalDebugInfo": {"originalNodeNames": ["concat"]}}, {"name": "strided_slice/stack", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "_output_shapes": {"list": {"shape": [{"dim": [{"size": "1"}]}]}}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "1"}]}, "intVal": [0]}}}, "experimentalDebugInfo": {"originalNodeNames": ["strided_slice/stack"]}}, {"name": "strided_slice/stack_1", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "_output_shapes": {"list": {"shape": [{"dim": [{"size": "1"}]}]}}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "1"}]}, "intVal": [10000]}}}, "experimentalDebugInfo": {"originalNodeNames": ["strided_slice/stack_1"]}}, {"name": "strided_slice/stack_2", "op": "Const", "attr": {"_output_shapes": {"list": {"shape": [{"dim": [{"size": "1"}]}]}}, "dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "1"}]}, "intVal": [1]}}}, "experimentalDebugInfo": {"originalNodeNames": ["strided_slice/stack_2"]}}, {"name": "strided_slice", "op": "StridedSlice", "input": ["concat:output:0", "strided_slice/stack:output:0", "strided_slice/stack_1:output:0", "strided_slice/stack_2:output:0"], "attr": {"Index": {"type": "DT_INT32"}, "begin_mask": {"i": "1"}, "T": {"type": "DT_STRING"}, "_output_shapes": {"list": {"shape": [{"dim": [{"size": "-1"}]}]}}}, "experimentalDebugInfo": {"originalNodeNames": ["strided_slice"]}}, {"name": "TensorArrayV2Write/TensorListSetItem", "op": "TensorListSetItem", "input": ["placeholder_1", "placeholder", "strided_slice:output:0"], "attr": {"element_dtype": {"type": "DT_STRING"}, "_output_shapes": {"list": {"shape": [{}]}}}, "experimentalDebugInfo": {"originalNodeNames": ["TensorArrayV2Write/TensorListSetItem"]}}, {"name": "add/y", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "_output_shapes": {"list": {"shape": [{}]}}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {}, "intVal": [1]}}}, "experimentalDebugInfo": {"originalNodeNames": ["add/y"]}}, {"name": "add_0", "op": "AddV2", "input": ["placeholder", "add/y:output:0"], "attr": {"T": {"type": "DT_INT32"}}, "experimentalDebugInfo": {"originalNodeNames": ["add"]}}, {"name": "add_1/y", "op": "Const", "attr": {"_output_shapes": {"list": {"shape": [{}]}}, "dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {}, "intVal": [1]}}}, "experimentalDebugInfo": {"originalNodeNames": ["add_1/y"]}}, {"name": "add_1_0", "op": "AddV2", "input": ["map_while_loop_counter", "add_1/y:output:0"], "attr": {"T": {"type": "DT_INT32"}}, "experimentalDebugInfo": {"originalNodeNames": ["add_1"]}}], "ret": {"tensorarrayv2read_tensorlistgetitem_map_tensorarrayunstack_tensorlistfromtensor": "tensorarrayv2read_tensorlistgetitem_map_tensorarrayunstack_tensorlistfromtensor_0", "tensorarrayv2write_tensorlistsetitem": "TensorArrayV2Write/TensorListSetItem:output_handle:0", "map_strided_slice": "map_strided_slice_0", "add_1": "add_1_0:z:0", "add": "add_0:z:0", "map_strided_slice_1": "map_strided_slice_1_0"}, "attr": {"_input_shapes": {"list": {"shape": [{}, {}, {}, {}, {}, {}]}}}, "argAttr": {"1": {"attr": {"_output_shapes": {"list": {"shape": [{}]}}}}, "3": {"attr": {"_output_shapes": {"list": {"shape": [{}]}}}}, "0": {"attr": {"_output_shapes": {"list": {"shape": [{}]}}}}, "5": {"attr": {"_output_shapes": {"list": {"shape": [{}]}}}}, "2": {"attr": {"_output_shapes": {"list": {"shape": [{}]}}}}, "4": {"attr": {"_output_shapes": {"list": {"shape": [{}]}}}}}}, {"signature": {"name": "zero_fraction_1_cond_false_49704", "inputArg": [{"name": "count_nonzero_notequal_linear_linear_model_linear_linear_model_linear_linear_model_weighted_sum", "type": "DT_FLOAT"}], "outputArg": [{"name": "count_nonzero_nonzero_count", "type": "DT_INT64"}]}, "nodeDef": [{"name": "count_nonzero/zeros", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "_output_shapes": {"list": {"shape": [{}]}}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}, "floatVal": [0.0]}}}, "experimentalDebugInfo": {"originalNodeNames": ["count_nonzero/zeros"]}}, {"name": "count_nonzero/NotEqual", "op": "NotEqual", "input": ["count_nonzero_notequal_linear_linear_model_linear_linear_model_linear_linear_model_weighted_sum", "count_nonzero/zeros:output:0"], "attr": {"_output_shapes": {"list": {"shape": [{"dim": [{"size": "-1"}, {"size": "54"}]}]}}, "T": {"type": "DT_FLOAT"}}, "experimentalDebugInfo": {"originalNodeNames": ["count_nonzero/NotEqual"]}}, {"name": "count_nonzero/Cast", "op": "Cast", "input": ["count_nonzero/NotEqual:z:0"], "attr": {"_output_shapes": {"list": {"shape": [{"dim": [{"size": "-1"}, {"size": "54"}]}]}}, "SrcT": {"type": "DT_BOOL"}, "DstT": {"type": "DT_INT64"}}, "experimentalDebugInfo": {"originalNodeNames": ["count_nonzero/Cast"]}}, {"name": "count_nonzero/Const", "op": "Const", "attr": {"_output_shapes": {"list": {"shape": [{"dim": [{"size": "2"}]}]}}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "2"}]}, "tensorContent": "AAAAAAEAAAA="}}, "dtype": {"type": "DT_INT32"}}, "experimentalDebugInfo": {"originalNodeNames": ["count_nonzero/Const"]}}, {"name": "count_nonzero/nonzero_count", "op": "Sum", "input": ["count_nonzero/Cast:y:0", "count_nonzero/Const:output:0"], "attr": {"_output_shapes": {"list": {"shape": [{}]}}, "T": {"type": "DT_INT64"}}, "experimentalDebugInfo": {"originalNodeNames": ["count_nonzero/nonzero_count"]}}], "ret": {"count_nonzero_nonzero_count": "count_nonzero/nonzero_count:output:0"}, "attr": {"_input_shapes": {"list": {"shape": [{"dim": [{"size": "-1"}, {"size": "54"}]}]}}}, "argAttr": {"0": {"attr": {"_output_shapes": {"list": {"shape": [{"dim": [{"size": "-1"}, {"size": "54"}]}]}}}}}}, {"signature": {"name": "dnn_zero_fraction_cond_true_49370", "inputArg": [{"name": "count_nonzero_notequal_dnn_hiddenlayer_0_relu", "type": "DT_FLOAT"}], "outputArg": [{"name": "cast", "type": "DT_INT64"}, {"name": "optionalfromvalue", "type": "DT_VARIANT"}, {"name": "optionalfromvalue_1", "type": "DT_VARIANT"}, {"name": "optionalfromvalue_2", "type": "DT_VARIANT"}, {"name": "optionalfromvalue_3", "type": "DT_VARIANT"}, {"name": "optionalfromvalue_4", "type": "DT_VARIANT"}]}, "nodeDef": [{"name": "count_nonzero/zeros", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "_output_shapes": {"list": {"shape": [{}]}}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}, "floatVal": [0.0]}}}, "experimentalDebugInfo": {"originalNodeNames": ["count_nonzero/zeros"]}}, {"name": "count_nonzero/NotEqual", "op": "NotEqual", "input": ["count_nonzero_notequal_dnn_hiddenlayer_0_relu", "count_nonzero/zeros:output:0"], "attr": {"_output_shapes": {"list": {"shape": [{"dim": [{"size": "-1"}, {"size": "512"}]}]}}, "T": {"type": "DT_FLOAT"}}, "experimentalDebugInfo": {"originalNodeNames": ["count_nonzero/NotEqual"]}}, {"name": "count_nonzero/Cast", "op": "Cast", "input": ["count_nonzero/NotEqual:z:0"], "attr": {"DstT": {"type": "DT_INT32"}, "SrcT": {"type": "DT_BOOL"}, "_output_shapes": {"list": {"shape": [{"dim": [{"size": "-1"}, {"size": "512"}]}]}}}, "experimentalDebugInfo": {"originalNodeNames": ["count_nonzero/Cast"]}}, {"name": "count_nonzero/Const", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "2"}]}, "tensorContent": "AAAAAAEAAAA="}}, "_output_shapes": {"list": {"shape": [{"dim": [{"size": "2"}]}]}}}, "experimentalDebugInfo": {"originalNodeNames": ["count_nonzero/Const"]}}, {"name": "count_nonzero/nonzero_count", "op": "Sum", "input": ["count_nonzero/Cast:y:0", "count_nonzero/Const:output:0"], "attr": {"_output_shapes": {"list": {"shape": [{}]}}, "T": {"type": "DT_INT32"}}, "experimentalDebugInfo": {"originalNodeNames": ["count_nonzero/nonzero_count"]}}, {"name": "Cast", "op": "Cast", "input": ["count_nonzero/nonzero_count:output:0"], "attr": {"_output_shapes": {"list": {"shape": [{}]}}, "DstT": {"type": "DT_INT64"}, "SrcT": {"type": "DT_INT32"}}, "experimentalDebugInfo": {"originalNodeNames": ["Cast"]}}, {"name": "OptionalFromValue", "op": "OptionalFromValue", "input": ["count_nonzero/zeros:output:0"], "attr": {"_output_shapes": {"list": {"shape": [{}]}}, "Toutput_types": {"list": {"type": ["DT_FLOAT"]}}}, "experimentalDebugInfo": {"originalNodeNames": ["OptionalFromValue"]}}, {"name": "OptionalFromValue_1", "op": "OptionalFromValue", "input": ["count_nonzero/NotEqual:z:0"], "attr": {"Toutput_types": {"list": {"type": ["DT_BOOL"]}}, "_output_shapes": {"list": {"shape": [{}]}}}, "experimentalDebugInfo": {"originalNodeNames": ["OptionalFromValue_1"]}}, {"name": "OptionalFromValue_2", "op": "OptionalFromValue", "input": ["count_nonzero/Cast:y:0"], "attr": {"_output_shapes": {"list": {"shape": [{}]}}, "Toutput_types": {"list": {"type": ["DT_INT32"]}}}, "experimentalDebugInfo": {"originalNodeNames": ["OptionalFromValue_2"]}}, {"name": "OptionalFromValue_3", "op": "OptionalFromValue", "input": ["count_nonzero/Const:output:0"], "attr": {"Toutput_types": {"list": {"type": ["DT_INT32"]}}, "_output_shapes": {"list": {"shape": [{}]}}}, "experimentalDebugInfo": {"originalNodeNames": ["OptionalFromValue_3"]}}, {"name": "OptionalFromValue_4", "op": "OptionalFromValue", "input": ["count_nonzero/nonzero_count:output:0"], "attr": {"_output_shapes": {"list": {"shape": [{}]}}, "Toutput_types": {"list": {"type": ["DT_INT32"]}}}, "experimentalDebugInfo": {"originalNodeNames": ["OptionalFromValue_4"]}}], "ret": {"optionalfromvalue_3": "OptionalFromValue_3:optional:0", "optionalfromvalue_1": "OptionalFromValue_1:optional:0", "cast": "Cast:y:0", "optionalfromvalue_4": "OptionalFromValue_4:optional:0", "optionalfromvalue": "OptionalFromValue:optional:0", "optionalfromvalue_2": "OptionalFromValue_2:optional:0"}, "attr": {"_input_shapes": {"list": {"shape": [{"dim": [{"size": "-1"}, {"size": "512"}]}]}}}, "argAttr": {"0": {"attr": {"_output_shapes": {"list": {"shape": [{"dim": [{"size": "-1"}, {"size": "512"}]}]}}}}}}, {"signature": {"name": "dnn_zero_fraction_1_cond_true_49440", "inputArg": [{"name": "count_nonzero_notequal_dnn_hiddenlayer_1_relu", "type": "DT_FLOAT"}], "outputArg": [{"name": "cast", "type": "DT_INT64"}, {"name": "optionalfromvalue", "type": "DT_VARIANT"}, {"name": "optionalfromvalue_1", "type": "DT_VARIANT"}, {"name": "optionalfromvalue_2", "type": "DT_VARIANT"}, {"name": "optionalfromvalue_3", "type": "DT_VARIANT"}, {"name": "optionalfromvalue_4", "type": "DT_VARIANT"}]}, "nodeDef": [{"name": "count_nonzero/zeros", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}, "floatVal": [0.0]}}, "_output_shapes": {"list": {"shape": [{}]}}, "dtype": {"type": "DT_FLOAT"}}, "experimentalDebugInfo": {"originalNodeNames": ["count_nonzero/zeros"]}}, {"name": "count_nonzero/NotEqual", "op": "NotEqual", "input": ["count_nonzero_notequal_dnn_hiddenlayer_1_relu", "count_nonzero/zeros:output:0"], "attr": {"_output_shapes": {"list": {"shape": [{"dim": [{"size": "-1"}, {"size": "32"}]}]}}, "T": {"type": "DT_FLOAT"}}, "experimentalDebugInfo": {"originalNodeNames": ["count_nonzero/NotEqual"]}}, {"name": "count_nonzero/Cast", "op": "Cast", "input": ["count_nonzero/NotEqual:z:0"], "attr": {"_output_shapes": {"list": {"shape": [{"dim": [{"size": "-1"}, {"size": "32"}]}]}}, "SrcT": {"type": "DT_BOOL"}, "DstT": {"type": "DT_INT32"}}, "experimentalDebugInfo": {"originalNodeNames": ["count_nonzero/Cast"]}}, {"name": "count_nonzero/Const", "op": "Const", "attr": {"_output_shapes": {"list": {"shape": [{"dim": [{"size": "2"}]}]}}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "2"}]}, "tensorContent": "AAAAAAEAAAA="}}, "dtype": {"type": "DT_INT32"}}, "experimentalDebugInfo": {"originalNodeNames": ["count_nonzero/Const"]}}, {"name": "count_nonzero/nonzero_count", "op": "Sum", "input": ["count_nonzero/Cast:y:0", "count_nonzero/Const:output:0"], "attr": {"T": {"type": "DT_INT32"}, "_output_shapes": {"list": {"shape": [{}]}}}, "experimentalDebugInfo": {"originalNodeNames": ["count_nonzero/nonzero_count"]}}, {"name": "Cast", "op": "Cast", "input": ["count_nonzero/nonzero_count:output:0"], "attr": {"_output_shapes": {"list": {"shape": [{}]}}, "SrcT": {"type": "DT_INT32"}, "DstT": {"type": "DT_INT64"}}, "experimentalDebugInfo": {"originalNodeNames": ["Cast"]}}, {"name": "OptionalFromValue", "op": "OptionalFromValue", "input": ["count_nonzero/zeros:output:0"], "attr": {"_output_shapes": {"list": {"shape": [{}]}}, "Toutput_types": {"list": {"type": ["DT_FLOAT"]}}}, "experimentalDebugInfo": {"originalNodeNames": ["OptionalFromValue"]}}, {"name": "OptionalFromValue_1", "op": "OptionalFromValue", "input": ["count_nonzero/NotEqual:z:0"], "attr": {"Toutput_types": {"list": {"type": ["DT_BOOL"]}}, "_output_shapes": {"list": {"shape": [{}]}}}, "experimentalDebugInfo": {"originalNodeNames": ["OptionalFromValue_1"]}}, {"name": "OptionalFromValue_2", "op": "OptionalFromValue", "input": ["count_nonzero/Cast:y:0"], "attr": {"Toutput_types": {"list": {"type": ["DT_INT32"]}}, "_output_shapes": {"list": {"shape": [{}]}}}, "experimentalDebugInfo": {"originalNodeNames": ["OptionalFromValue_2"]}}, {"name": "OptionalFromValue_3", "op": "OptionalFromValue", "input": ["count_nonzero/Const:output:0"], "attr": {"_output_shapes": {"list": {"shape": [{}]}}, "Toutput_types": {"list": {"type": ["DT_INT32"]}}}, "experimentalDebugInfo": {"originalNodeNames": ["OptionalFromValue_3"]}}, {"name": "OptionalFromValue_4", "op": "OptionalFromValue", "input": ["count_nonzero/nonzero_count:output:0"], "attr": {"_output_shapes": {"list": {"shape": [{}]}}, "Toutput_types": {"list": {"type": ["DT_INT32"]}}}, "experimentalDebugInfo": {"originalNodeNames": ["OptionalFromValue_4"]}}], "ret": {"optionalfromvalue_3": "OptionalFromValue_3:optional:0", "optionalfromvalue_2": "OptionalFromValue_2:optional:0", "optionalfromvalue": "OptionalFromValue:optional:0", "optionalfromvalue_1": "OptionalFromValue_1:optional:0", "cast": "Cast:y:0", "optionalfromvalue_4": "OptionalFromValue_4:optional:0"}, "attr": {"_input_shapes": {"list": {"shape": [{"dim": [{"size": "-1"}, {"size": "32"}]}]}}}, "argAttr": {"0": {"attr": {"_output_shapes": {"list": {"shape": [{"dim": [{"size": "-1"}, {"size": "32"}]}]}}}}}}, {"signature": {"name": "zero_fraction_cond_false_49665", "inputArg": [{"name": "count_nonzero_notequal_zero_fraction_readvariableop", "type": "DT_FLOAT"}], "outputArg": [{"name": "count_nonzero_nonzero_count", "type": "DT_INT64"}]}, "nodeDef": [{"name": "count_nonzero/zeros", "op": "Const", "attr": {"_output_shapes": {"list": {"shape": [{}]}}, "dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}, "floatVal": [0.0]}}}, "experimentalDebugInfo": {"originalNodeNames": ["count_nonzero/zeros"]}}, {"name": "count_nonzero/NotEqual", "op": "NotEqual", "input": ["count_nonzero_notequal_zero_fraction_readvariableop", "count_nonzero/zeros:output:0"], "attr": {"_output_shapes": {"list": {"shape": [{"dim": [{"size": "5000"}, {"size": "54"}]}]}}, "T": {"type": "DT_FLOAT"}}, "experimentalDebugInfo": {"originalNodeNames": ["count_nonzero/NotEqual"]}}, {"name": "count_nonzero/Cast", "op": "Cast", "input": ["count_nonzero/NotEqual:z:0"], "attr": {"SrcT": {"type": "DT_BOOL"}, "DstT": {"type": "DT_INT64"}, "_output_shapes": {"list": {"shape": [{"dim": [{"size": "5000"}, {"size": "54"}]}]}}}, "experimentalDebugInfo": {"originalNodeNames": ["count_nonzero/Cast"]}}, {"name": "count_nonzero/Const", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "2"}]}, "tensorContent": "AAAAAAEAAAA="}}, "_output_shapes": {"list": {"shape": [{"dim": [{"size": "2"}]}]}}, "dtype": {"type": "DT_INT32"}}, "experimentalDebugInfo": {"originalNodeNames": ["count_nonzero/Const"]}}, {"name": "count_nonzero/nonzero_count", "op": "Sum", "input": ["count_nonzero/Cast:y:0", "count_nonzero/Const:output:0"], "attr": {"T": {"type": "DT_INT64"}, "_output_shapes": {"list": {"shape": [{}]}}}, "experimentalDebugInfo": {"originalNodeNames": ["count_nonzero/nonzero_count"]}}], "ret": {"count_nonzero_nonzero_count": "count_nonzero/nonzero_count:output:0"}, "attr": {"_input_shapes": {"list": {"shape": [{"dim": [{"size": "5000"}, {"size": "54"}]}]}}}, "argAttr": {"0": {"attr": {"_output_shapes": {"list": {"shape": [{"dim": [{"size": "5000"}, {"size": "54"}]}]}}}}}}, {"signature": {"name": "zero_fraction_1_cond_true_49703", "inputArg": [{"name": "count_nonzero_notequal_linear_linear_model_linear_linear_model_linear_linear_model_weighted_sum", "type": "DT_FLOAT"}], "outputArg": [{"name": "cast", "type": "DT_INT64"}]}, "nodeDef": [{"name": "count_nonzero/zeros", "op": "Const", "attr": {"_output_shapes": {"list": {"shape": [{}]}}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}, "floatVal": [0.0]}}, "dtype": {"type": "DT_FLOAT"}}, "experimentalDebugInfo": {"originalNodeNames": ["count_nonzero/zeros"]}}, {"name": "count_nonzero/NotEqual", "op": "NotEqual", "input": ["count_nonzero_notequal_linear_linear_model_linear_linear_model_linear_linear_model_weighted_sum", "count_nonzero/zeros:output:0"], "attr": {"_output_shapes": {"list": {"shape": [{"dim": [{"size": "-1"}, {"size": "54"}]}]}}, "T": {"type": "DT_FLOAT"}}, "experimentalDebugInfo": {"originalNodeNames": ["count_nonzero/NotEqual"]}}, {"name": "count_nonzero/Cast", "op": "Cast", "input": ["count_nonzero/NotEqual:z:0"], "attr": {"_output_shapes": {"list": {"shape": [{"dim": [{"size": "-1"}, {"size": "54"}]}]}}, "DstT": {"type": "DT_INT32"}, "SrcT": {"type": "DT_BOOL"}}, "experimentalDebugInfo": {"originalNodeNames": ["count_nonzero/Cast"]}}, {"name": "count_nonzero/Const", "op": "Const", "attr": {"_output_shapes": {"list": {"shape": [{"dim": [{"size": "2"}]}]}}, "dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "2"}]}, "tensorContent": "AAAAAAEAAAA="}}}, "experimentalDebugInfo": {"originalNodeNames": ["count_nonzero/Const"]}}, {"name": "count_nonzero/nonzero_count", "op": "Sum", "input": ["count_nonzero/Cast:y:0", "count_nonzero/Const:output:0"], "attr": {"T": {"type": "DT_INT32"}, "_output_shapes": {"list": {"shape": [{}]}}}, "experimentalDebugInfo": {"originalNodeNames": ["count_nonzero/nonzero_count"]}}, {"name": "Cast", "op": "Cast", "input": ["count_nonzero/nonzero_count:output:0"], "attr": {"DstT": {"type": "DT_INT64"}, "SrcT": {"type": "DT_INT32"}, "_output_shapes": {"list": {"shape": [{}]}}}, "experimentalDebugInfo": {"originalNodeNames": ["Cast"]}}], "ret": {"cast": "Cast:y:0"}, "attr": {"_input_shapes": {"list": {"shape": [{"dim": [{"size": "-1"}, {"size": "54"}]}]}}}, "argAttr": {"0": {"attr": {"_output_shapes": {"list": {"shape": [{"dim": [{"size": "-1"}, {"size": "54"}]}]}}}}}}, {"signature": {"name": "zero_fraction_cond_true_49664", "inputArg": [{"name": "count_nonzero_notequal_zero_fraction_readvariableop", "type": "DT_FLOAT"}], "outputArg": [{"name": "cast", "type": "DT_INT64"}]}, "nodeDef": [{"name": "count_nonzero/zeros", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}, "floatVal": [0.0]}}, "dtype": {"type": "DT_FLOAT"}, "_output_shapes": {"list": {"shape": [{}]}}}, "experimentalDebugInfo": {"originalNodeNames": ["count_nonzero/zeros"]}}, {"name": "count_nonzero/NotEqual", "op": "NotEqual", "input": ["count_nonzero_notequal_zero_fraction_readvariableop", "count_nonzero/zeros:output:0"], "attr": {"T": {"type": "DT_FLOAT"}, "_output_shapes": {"list": {"shape": [{"dim": [{"size": "5000"}, {"size": "54"}]}]}}}, "experimentalDebugInfo": {"originalNodeNames": ["count_nonzero/NotEqual"]}}, {"name": "count_nonzero/Cast", "op": "Cast", "input": ["count_nonzero/NotEqual:z:0"], "attr": {"SrcT": {"type": "DT_BOOL"}, "_output_shapes": {"list": {"shape": [{"dim": [{"size": "5000"}, {"size": "54"}]}]}}, "DstT": {"type": "DT_INT32"}}, "experimentalDebugInfo": {"originalNodeNames": ["count_nonzero/Cast"]}}, {"name": "count_nonzero/Const", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "2"}]}, "tensorContent": "AAAAAAEAAAA="}}, "_output_shapes": {"list": {"shape": [{"dim": [{"size": "2"}]}]}}}, "experimentalDebugInfo": {"originalNodeNames": ["count_nonzero/Const"]}}, {"name": "count_nonzero/nonzero_count", "op": "Sum", "input": ["count_nonzero/Cast:y:0", "count_nonzero/Const:output:0"], "attr": {"_output_shapes": {"list": {"shape": [{}]}}, "T": {"type": "DT_INT32"}}, "experimentalDebugInfo": {"originalNodeNames": ["count_nonzero/nonzero_count"]}}, {"name": "Cast", "op": "Cast", "input": ["count_nonzero/nonzero_count:output:0"], "attr": {"_output_shapes": {"list": {"shape": [{}]}}, "SrcT": {"type": "DT_INT32"}, "DstT": {"type": "DT_INT64"}}, "experimentalDebugInfo": {"originalNodeNames": ["Cast"]}}], "ret": {"cast": "Cast:y:0"}, "attr": {"_input_shapes": {"list": {"shape": [{"dim": [{"size": "5000"}, {"size": "54"}]}]}}}, "argAttr": {"0": {"attr": {"_output_shapes": {"list": {"shape": [{"dim": [{"size": "5000"}, {"size": "54"}]}]}}}}}}, {"signature": {"name": "zero_fraction_total_zero_zero_count_true_49654", "inputArg": [{"name": "placeholder", "type": "DT_RESOURCE"}, {"name": "placeholder_1", "type": "DT_INT64"}], "outputArg": [{"name": "const", "type": "DT_FLOAT"}]}, "nodeDef": [{"name": "Const", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}, "floatVal": [0.0]}}, "_output_shapes": {"list": {"shape": [{}]}}}, "experimentalDebugInfo": {"originalNodeNames": ["Const"]}}], "ret": {"const": "Const:output:0"}, "attr": {"_input_shapes": {"list": {"shape": [{"unknownRank": true}, {}]}}}, "argAttr": {"1": {"attr": {"_output_shapes": {"list": {"shape": [{}]}}}}}}]}, "versions": {"minConsumer": 12}}, "weightsManifest": [{"paths": ["group1-shard1of1.bin"], "weights": [{"name": "head/ExpandDims/input", "shape": [54], "dtype": "string"}, {"name": "head/ExpandDims/dim", "shape": [], "dtype": "int32"}, {"name": "head/strided_slice/stack", "shape": [1], "dtype": "int32"}, {"name": "head/strided_slice/stack_1", "shape": [1], "dtype": "int32"}, {"name": "head/strided_slice/stack_2", "shape": [1], "dtype": "int32"}, {"name": "head/Tile/multiples/1", "shape": [], "dtype": "int32"}, {"name": "dnn/input_from_feature_columns/input_layer/content_embedding_1/content_embedding_weights/Reshape_1/shape", "shape": [2], "dtype": "int32"}, {"name": "dnn/input_from_feature_columns/input_layer/content_embedding_1/content_embedding_weights/stack/0", "shape": [], "dtype": "int32"}, {"name": "dnn/input_from_feature_columns/input_layer/content_embedding_1/content_embedding_weights/strided_slice/stack", "shape": [1], "dtype": "int32"}, {"name": "dnn/input_from_feature_columns/input_layer/content_embedding_1/content_embedding_weights/strided_slice/stack_1", "shape": [1], "dtype": "int32"}, {"name": "dnn/input_from_feature_columns/input_layer/content_embedding_1/content_embedding_weights/strided_slice/stack_2", "shape": [1], "dtype": "int32"}, {"name": "dnn/input_from_feature_columns/input_layer/content_embedding/embedding_weights", "shape": [5000, 70], "dtype": "float32", "quantization": {"dtype": "uint8", "min": -0.23829841794920903, "scale": 0.0018763654956630632, "original_dtype": "float32"}}, {"name": "dnn/input_from_feature_columns/input_layer/content_embedding_1/content_embedding_weights/GatherV2_1/axis", "shape": [], "dtype": "int32"}, {"name": "dnn/input_from_feature_columns/input_layer/content_embedding_1/content_embedding_weights/GreaterEqual/y", "shape": [], "dtype": "int32"}, {"name": "dnn/input_from_feature_columns/input_layer/content_embedding_1/content_embedding_weights/Reshape/shape", "shape": [1], "dtype": "int32"}, {"name": "dnn/input_from_feature_columns/input_layer/content_embedding_1/content_embedding_weights/GatherV2_2/axis", "shape": [], "dtype": "int32"}, {"name": "dnn/input_from_feature_columns/input_layer/content_embedding_1/to_sparse_input/ignore_value/x", "shape": [], "dtype": "string"}, {"name": "dnn/input_from_feature_columns/input_layer/content_embedding_1/content_embedding_weights/Slice/begin", "shape": [1], "dtype": "int32"}, {"name": "dnn/input_from_feature_columns/input_layer/content_embedding_1/content_embedding_weights/Slice/size", "shape": [1], "dtype": "int32"}, {"name": "dnn/input_from_feature_columns/input_layer/content_embedding_1/content_embedding_weights/Const", "shape": [1], "dtype": "int32"}, {"name": "dnn/input_from_feature_columns/input_layer/content_embedding_1/content_embedding_weights/GatherV2/indices", "shape": [], "dtype": "int32"}, {"name": "dnn/input_from_feature_columns/input_layer/content_embedding_1/content_embedding_weights/GatherV2/axis", "shape": [], "dtype": "int32"}, {"name": "dnn/input_from_feature_columns/input_layer/content_embedding_1/content_embedding_weights/SparseFillEmptyRows/Const", "shape": [], "dtype": "int32"}, {"name": "dnn/input_from_feature_columns/input_layer/content_embedding_1/content_embedding_weights/embedding_lookup_sparse/strided_slice/stack", "shape": [2], "dtype": "int32"}, {"name": "dnn/input_from_feature_columns/input_layer/content_embedding_1/content_embedding_weights/embedding_lookup_sparse/strided_slice/stack_1", "shape": [2], "dtype": "int32"}, {"name": "dnn/input_from_feature_columns/input_layer/content_embedding_1/content_embedding_weights/embedding_lookup_sparse/strided_slice/stack_2", "shape": [2], "dtype": "int32"}, {"name": "dnn/hiddenlayer_0/kernel", "shape": [70, 512], "dtype": "float32", "quantization": {"dtype": "uint8", "min": -0.10249130375245037, "scale": 0.0008070181397830738, "original_dtype": "float32"}}, {"name": "dnn/hiddenlayer_0/bias", "shape": [512], "dtype": "float32", "quantization": {"dtype": "uint8", "min": -0.021031223785351303, "scale": 0.00020029736938429813, "original_dtype": "float32"}}, {"name": "dnn/hiddenlayer_1/kernel", "shape": [512, 32], "dtype": "float32", "quantization": {"dtype": "uint8", "min": -0.10604567843324998, "scale": 0.000841632368517857, "original_dtype": "float32"}}, {"name": "dnn/hiddenlayer_1/bias", "shape": [32], "dtype": "float32", "quantization": {"dtype": "uint8", "min": -0.022284538547197977, "scale": 0.00032771380216467614, "original_dtype": "float32"}}, {"name": "dnn/logits/kernel", "shape": [32, 54], "dtype": "float32", "quantization": {"dtype": "uint8", "min": -0.27378906011581416, "scale": 0.002172929048538208, "original_dtype": "float32"}}, {"name": "dnn/logits/bias", "shape": [54], "dtype": "float32", "quantization": {"dtype": "uint8", "min": -0.11504998528489879, "scale": 0.0009508263246685851, "original_dtype": "float32"}}, {"name": "linear/linear_model/linear/linear_model/linear/linear_model/content/weighted_sum/Slice_1/begin", "shape": [1], "dtype": "int32"}, {"name": "linear/linear_model/linear/linear_model/linear/linear_model/content/weighted_sum/Slice_1/size", "shape": [1], "dtype": "int32"}, {"name": "linear/linear_model/linear/linear_model/linear/linear_model/content/weighted_sum/Reshape_1/shape", "shape": [2], "dtype": "int32"}, {"name": "linear/linear_model/linear/linear_model/linear/linear_model/content/weighted_sum/stack/0", "shape": [], "dtype": "int32"}, {"name": "linear/linear_model/linear/linear_model/linear/linear_model/content/weighted_sum/strided_slice/stack", "shape": [1], "dtype": "int32"}, {"name": "linear/linear_model/linear/linear_model/linear/linear_model/content/weighted_sum/strided_slice/stack_1", "shape": [1], "dtype": "int32"}, {"name": "linear/linear_model/linear/linear_model/linear/linear_model/content/weighted_sum/strided_slice/stack_2", "shape": [1], "dtype": "int32"}, {"name": "linear/linear_model/content/weights", "shape": [5000, 54], "dtype": "float32", "quantization": {"dtype": "uint8", "min": -0.3894611456814934, "scale": 0.003933950966479732, "original_dtype": "float32"}}, {"name": "linear/linear_model/linear/linear_model/linear/linear_model/content/weighted_sum/GatherV2_1/axis", "shape": [], "dtype": "int32"}, {"name": "linear/linear_model/linear/linear_model/linear/linear_model/content/weighted_sum/GreaterEqual/y", "shape": [], "dtype": "int32"}, {"name": "linear/linear_model/linear/linear_model/linear/linear_model/content/weighted_sum/Reshape/shape", "shape": [1], "dtype": "int32"}, {"name": "linear/linear_model/linear/linear_model/linear/linear_model/content/weighted_sum/GatherV2_2/axis", "shape": [], "dtype": "int32"}, {"name": "linear/linear_model/linear/linear_model/linear/linear_model/content/weighted_sum/Slice/begin", "shape": [1], "dtype": "int32"}, {"name": "linear/linear_model/linear/linear_model/linear/linear_model/content/weighted_sum/Slice/size", "shape": [1], "dtype": "int32"}, {"name": "linear/linear_model/linear/linear_model/linear/linear_model/content/weighted_sum/Const", "shape": [1], "dtype": "int32"}, {"name": "linear/linear_model/linear/linear_model/linear/linear_model/content/to_sparse_input/ignore_value/x", "shape": [], "dtype": "string"}, {"name": "map/while/loop_counter", "shape": [], "dtype": "int32"}, {"name": "map/Const", "shape": [], "dtype": "int32"}, {"name": "map/TensorArrayV2_1/element_shape", "shape": [], "dtype": "int32"}, {"name": "map/strided_slice/stack", "shape": [1], "dtype": "int32"}, {"name": "map/strided_slice/stack_1", "shape": [1], "dtype": "int32"}, {"name": "map/strided_slice/stack_2", "shape": [1], "dtype": "int32"}, {"name": "map/TensorArrayUnstack/Const", "shape": [0], "dtype": "int32"}, {"name": "map/TensorArrayV2Stack/TensorListStack/element_shape", "shape": [1], "dtype": "int32"}, {"name": "linear/linear_model/linear/linear_model/linear/linear_model/content/strided_slice/stack", "shape": [1], "dtype": "int32"}, {"name": "linear/linear_model/linear/linear_model/linear/linear_model/content/strided_slice/stack_1", "shape": [1], "dtype": "int32"}, {"name": "linear/linear_model/linear/linear_model/linear/linear_model/content/strided_slice/stack_2", "shape": [1], "dtype": "int32"}, {"name": "linear/linear_model/linear/linear_model/linear/linear_model/content/Cast/x/1", "shape": [], "dtype": "int32"}, {"name": "linear/linear_model/linear/linear_model/linear/linear_model/content/weighted_sum/GatherV2/indices", "shape": [], "dtype": "int32"}, {"name": "linear/linear_model/linear/linear_model/linear/linear_model/content/weighted_sum/GatherV2/axis", "shape": [], "dtype": "int32"}, {"name": "linear/linear_model/linear/linear_model/linear/linear_model/content/weighted_sum/SparseFillEmptyRows/Const", "shape": [], "dtype": "int32"}, {"name": "linear/linear_model/linear/linear_model/linear/linear_model/content/weighted_sum/embedding_lookup_sparse/strided_slice/stack", "shape": [2], "dtype": "int32"}, {"name": "linear/linear_model/linear/linear_model/linear/linear_model/content/weighted_sum/embedding_lookup_sparse/strided_slice/stack_1", "shape": [2], "dtype": "int32"}, {"name": "linear/linear_model/linear/linear_model/linear/linear_model/content/weighted_sum/embedding_lookup_sparse/strided_slice/stack_2", "shape": [2], "dtype": "int32"}, {"name": "linear/linear_model/linear/linear_model/linear/linear_model/content/weighted_sum/Slice_2/begin", "shape": [1], "dtype": "int32"}, {"name": "linear/linear_model/linear/linear_model/linear/linear_model/content/weighted_sum/Slice_2/size", "shape": [1], "dtype": "int32"}, {"name": "linear/linear_model/linear/linear_model/linear/linear_model/content/weighted_sum/concat/axis", "shape": [], "dtype": "int32"}, {"name": "linear/linear_model/bias_weights", "shape": [54], "dtype": "float32", "quantization": {"dtype": "uint8", "min": -0.5752425799182816, "scale": 0.004754070908415551, "original_dtype": "float32"}}, {"name": "map_while_body_49115/add_1/y", "shape": [], "dtype": "int32"}, {"name": "map_while_body_49115/add/y", "shape": [], "dtype": "int32"}, {"name": "map_while_body_49115/StringNGrams/RaggedGetItem/strided_slice/stack", "shape": [1], "dtype": "int32"}, {"name": "map_while_body_49115/StringNGrams/RaggedGetItem/strided_slice/stack_1", "shape": [1], "dtype": "int32"}, {"name": "map_while_body_49115/StringNGrams/RaggedGetItem/strided_slice/stack_2", "shape": [1], "dtype": "int32"}, {"name": "map_while_body_49115/StringNGrams/RaggedGetItem/strided_slice_2/stack", "shape": [1], "dtype": "int32"}, {"name": "map_while_body_49115/StringNGrams/RaggedGetItem/strided_slice_2/stack_1", "shape": [1], "dtype": "int32"}, {"name": "map_while_body_49115/StringNGrams/RaggedGetItem/strided_slice_2/stack_2", "shape": [1], "dtype": "int32"}, {"name": "map_while_body_49115/StringNGrams/RaggedFromRowStarts/RowPartitionFromRowStarts/row_starts", "shape": [1], "dtype": "int32"}, {"name": "map_while_body_49115/StringsByteSplit/RaggedGetItem/strided_slice/stack", "shape": [1], "dtype": "int32"}, {"name": "map_while_body_49115/StringsByteSplit/RaggedGetItem/strided_slice/stack_1", "shape": [1], "dtype": "int32"}, {"name": "map_while_body_49115/StringsByteSplit/RaggedGetItem/strided_slice/stack_2", "shape": [1], "dtype": "int32"}, {"name": "map_while_body_49115/StringsByteSplit/RaggedGetItem/strided_slice_2/stack", "shape": [1], "dtype": "int32"}, {"name": "map_while_body_49115/StringsByteSplit/RaggedGetItem/strided_slice_2/stack_1", "shape": [1], "dtype": "int32"}, {"name": "map_while_body_49115/StringsByteSplit/RaggedGetItem/strided_slice_2/stack_2", "shape": [1], "dtype": "int32"}, {"name": "map_while_body_49115/StringsByteSplit/StringsByteSplit/RaggedFromValueRowIds/RowPartitionFromValueRowIds/concat/values_0", "shape": [1], "dtype": "int32"}, {"name": "map_while_body_49115/StringsByteSplit/StringsByteSplit/strided_slice_1/stack", "shape": [1], "dtype": "int32"}, {"name": "map_while_body_49115/StringsByteSplit/StringsByteSplit/strided_slice_1/stack_1", "shape": [1], "dtype": "int32"}, {"name": "map_while_body_49115/StringsByteSplit/StringsByteSplit/strided_slice_1/stack_2", "shape": [1], "dtype": "int32"}, {"name": "map_while_body_49115/StringsByteSplit/StringsByteSplit/RaggedFromValueRowIds/RowPartitionFromValueRowIds/bincount/Const", "shape": [1], "dtype": "int32"}, {"name": "map_while_body_49115/StringsByteSplit/StringsByteSplit/RaggedFromValueRowIds/RowPartitionFromValueRowIds/bincount/Greater/y", "shape": [], "dtype": "int32"}, {"name": "map_while_body_49115/TensorArrayV2Read/Const", "shape": [0], "dtype": "int32"}, {"name": "map_while_body_49115/StringsByteSplit/StringsByteSplit/StringSplit/delimiter", "shape": [], "dtype": "string"}, {"name": "map_while_body_49115/StringsByteSplit/StringsByteSplit/strided_slice/stack", "shape": [2], "dtype": "int32"}, {"name": "map_while_body_49115/StringsByteSplit/StringsByteSplit/strided_slice/stack_1", "shape": [2], "dtype": "int32"}, {"name": "map_while_body_49115/StringsByteSplit/StringsByteSplit/strided_slice/stack_2", "shape": [2], "dtype": "int32"}, {"name": "map_while_body_49115/StringsByteSplit/StringsByteSplit/RaggedFromValueRowIds/RowPartitionFromValueRowIds/bincount/Const_1", "shape": [1], "dtype": "int32"}, {"name": "map_while_body_49115/StringsByteSplit/StringsByteSplit/RaggedFromValueRowIds/RowPartitionFromValueRowIds/bincount/add/y", "shape": [], "dtype": "int32"}, {"name": "map_while_body_49115/StringsByteSplit/StringsByteSplit/RaggedFromValueRowIds/RowPartitionFromValueRowIds/bincount/Const_2", "shape": [0], "dtype": "int32"}, {"name": "map_while_body_49115/StringsByteSplit/StringsByteSplit/RaggedFromValueRowIds/RowPartitionFromValueRowIds/Cumsum/axis", "shape": [], "dtype": "int32"}, {"name": "map_while_body_49115/StringsByteSplit/StringsByteSplit/RaggedFromValueRowIds/RowPartitionFromValueRowIds/concat/axis", "shape": [], "dtype": "int32"}, {"name": "map_while_body_49115/StringsByteSplit/RaggedGetItem/strided_slice_1/stack", "shape": [1], "dtype": "int32"}, {"name": "map_while_body_49115/StringsByteSplit/RaggedGetItem/strided_slice_1/stack_1", "shape": [1], "dtype": "int32"}, {"name": "map_while_body_49115/StringsByteSplit/RaggedGetItem/strided_slice_1/stack_2", "shape": [1], "dtype": "int32"}, {"name": "map_while_body_49115/StringsByteSplit/RaggedGetItem/strided_slice_3/stack", "shape": [1], "dtype": "int32"}, {"name": "map_while_body_49115/StringsByteSplit/RaggedGetItem/strided_slice_3/stack_1", "shape": [1], "dtype": "int32"}, {"name": "map_while_body_49115/StringsByteSplit/RaggedGetItem/strided_slice_3/stack_2", "shape": [1], "dtype": "int32"}, {"name": "map_while_body_49115/StringsByteSplit/RaggedGetItem/strided_slice_4/Cast", "shape": [1], "dtype": "int32"}, {"name": "map_while_body_49115/StringsByteSplit/RaggedGetItem/strided_slice_5/Const", "shape": [0], "dtype": "int32"}, {"name": "map_while_body_49115/StringNGrams/RaggedFromRowStarts/strided_slice/stack", "shape": [1], "dtype": "int32"}, {"name": "map_while_body_49115/StringNGrams/RaggedFromRowStarts/strided_slice/stack_1", "shape": [1], "dtype": "int32"}, {"name": "map_while_body_49115/StringNGrams/RaggedFromRowStarts/strided_slice/stack_2", "shape": [1], "dtype": "int32"}, {"name": "map_while_body_49115/StringNGrams/RaggedFromRowStarts/RowPartitionFromRowStarts/concat/axis", "shape": [], "dtype": "int32"}, {"name": "map_while_body_49115/StringNGrams/RaggedGetItem/strided_slice_1/stack", "shape": [1], "dtype": "int32"}, {"name": "map_while_body_49115/StringNGrams/RaggedGetItem/strided_slice_1/stack_1", "shape": [1], "dtype": "int32"}, {"name": "map_while_body_49115/StringNGrams/RaggedGetItem/strided_slice_1/stack_2", "shape": [1], "dtype": "int32"}, {"name": "map_while_body_49115/StringNGrams/RaggedGetItem/strided_slice_3/stack", "shape": [1], "dtype": "int32"}, {"name": "map_while_body_49115/StringNGrams/RaggedGetItem/strided_slice_3/stack_1", "shape": [1], "dtype": "int32"}, {"name": "map_while_body_49115/StringNGrams/RaggedGetItem/strided_slice_3/stack_2", "shape": [1], "dtype": "int32"}, {"name": "map_while_body_49115/StringNGrams/RaggedGetItem/strided_slice_4/Cast", "shape": [1], "dtype": "int32"}, {"name": "map_while_body_49115/StringNGrams/RaggedGetItem/strided_slice_5/Const", "shape": [0], "dtype": "int32"}, {"name": "map_while_body_49115/Const", "shape": [10000], "dtype": "string"}, {"name": "map_while_body_49115/concat/axis", "shape": [], "dtype": "int32"}, {"name": "map_while_body_49115/strided_slice/stack", "shape": [1], "dtype": "int32"}, {"name": "map_while_body_49115/strided_slice/stack_1", "shape": [1], "dtype": "int32"}, {"name": "map_while_body_49115/strided_slice/stack_2", "shape": [1], "dtype": "int32"}, {"name": "map_while_body_49115/ConstantFolding/StringsByteSplit/stack_const_axis", "shape": [], "dtype": "int32"}, {"name": "map_while_body_49115/ConstantFolding/StringsByteSplit/RaggedGetItem/strided_slice_4/stack_const_axis", "shape": [], "dtype": "int32"}, {"name": "map_while_body_49115/ConstantFolding/StringsByteSplit/RaggedGetItem/strided_slice_4/stack_1_const_axis", "shape": [], "dtype": "int32"}, {"name": "map_while_body_49115/ConstantFolding/StringNGrams/RaggedFromRowStarts/RowPartitionFromRowStarts/concat/values_1_const_axis", "shape": [], "dtype": "int32"}, {"name": "map_while_body_49115/ConstantFolding/StringNGrams/RaggedGetItem/strided_slice_4/stack_const_axis", "shape": [], "dtype": "int32"}, {"name": "map_while_body_49115/ConstantFolding/StringNGrams/RaggedGetItem/strided_slice_4/stack_1_const_axis", "shape": [], "dtype": "int32"}, {"name": "head/predictions/class_string_lookup/Const", "shape": [54], "dtype": "string"}, {"name": "head/predictions/class_string_lookup/Size", "shape": [], "dtype": "int32"}, {"name": "head/predictions/class_string_lookup/range/start", "shape": [], "dtype": "int32"}, {"name": "head/predictions/class_string_lookup/range/delta", "shape": [], "dtype": "int32"}]}]}