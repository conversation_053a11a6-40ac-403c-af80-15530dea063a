import os
from pathlib import Path


def get_portable_vscode_base() -> str:
    """
    Get the portable VSCode base directory.

    Returns:
        str: Path to the portable VSCode directory (relative to current working directory)
    """
    # Get the current script's directory and navigate to the vscode directory
    current_dir = Path(__file__).parent.parent.parent  # Go up from utils/paths.py to project root
    vscode_path = current_dir / "vscode"
    return str(vscode_path)


def get_home_dir() -> str:
    """
    Get the user's home directory across different platforms.

    Returns:
        str: Path to the user's home directory
    """
    return str(Path.home())


def get_app_data_dir() -> str:
    """
    Get the portable VSCode data directory.

    Returns:
        str: Path to the portable VSCode data directory
    """
    return os.path.join(get_portable_vscode_base(), "data", "user-data")


def get_storage_path() -> str:
    """
    Get the storage.json path for portable VSCode.

    Returns:
        str: Path to the storage.json file in portable VSCode
    """
    base_path = get_app_data_dir()
    return os.path.join(base_path, "User", "globalStorage", "storage.json")


def get_db_path() -> str:
    """
    Get the state.vscdb path for portable VSCode.

    Returns:
        str: Path to the state.vscdb file in portable VSCode
    """
    base_path = get_app_data_dir()
    return os.path.join(base_path, "User", "globalStorage", "state.vscdb")


def get_machine_id_path() -> str:
    """
    Get the machine ID file path for portable VSCode.

    Returns:
        str: Path to the machine ID file in portable VSCode
    """
    base_path = get_app_data_dir()
    return os.path.join(base_path, "User", "machineid")


def get_workspace_storage_path() -> str:
    """
    Get the workspaceStorage path for portable VSCode.

    Returns:
        str: Path to the workspaceStorage directory in portable VSCode
    """
    base_path = get_app_data_dir()
    return os.path.join(base_path, "User", "workspaceStorage")
