import{B as x,a2 as Y,S as F,i as B,s as N,d as m,a7 as K,t as d,q as h,o as O,p as M,c as v,F as C,h as g,N as e2,n as P,aa as t2,D as L,E as j,G as y,Q as S,V as I,e as A,P as n2,X as o2,R as q,Y as c2}from"./SpinnerAugment-VfHtkDdv.js";import{L as i2}from"./LanguageIcon-BXmH3Ek-.js";import{e as z,I as a2}from"./IconButtonAugment-BlRCK7lJ.js";import{C as s2}from"./next-edit-types-904A5ehg.js";var q2=function(t){var e=typeof t;return t!=null&&(e=="object"||e=="function")},l2=typeof x=="object"&&x&&x.Object===Object&&x,r2=l2,u2=typeof self=="object"&&self&&self.Object===Object&&self,f2=r2||u2||Function("return this")(),_=f2.Symbol,E=_,W=Object.prototype,g2=W.hasOwnProperty,h2=W.toString,$=E?E.toStringTag:void 0,d2=function(t){var e=g2.call(t,$),n=t[$];try{t[$]=void 0;var o=!0}catch{}var c=h2.call(t);return o&&(e?t[$]=n:delete t[$]),c},p2=Object.prototype.toString,m2=d2,v2=function(t){return p2.call(t)},H=_?_.toStringTag:void 0,z2=function(t){return t==null?t===void 0?"[object Undefined]":"[object Null]":H&&H in Object(t)?m2(t):v2(t)},E2=function(t){return t!=null&&typeof t=="object"};const G=(t,e,n)=>Math.min(Math.max(t,e),n),H2=(t,e=30,n=230,o=500)=>G(t*e,n,o);function V2(t,e={duration:600,easing:Y}){const n=t.getBoundingClientRect().height;return{...e,css:o=>`max-height: ${(o<.5?2*o:1)*n}px; opacity: ${1-(o>.5?1-2*(o-.5):1)};`}}function Z2(t){return`${t.lineRange.start+1}-${t.lineRange.stop+1}`}function C2(t,e){if(!e)return t.qualifiedPathName.relPath;const n=t.qualifiedPathName.rootPath,o=t.qualifiedPathName.relPath,c=n.includes("\\")||o.includes("\\")?"\\":"/";return n+(n.endsWith(c)?"":c)+o}function k2(t,e){if(!(e!=null&&e.result.suggestionId))return-1;let n=0;for(const o of X(t)){if(Q(o,e))return n;n++}return-1}function T2(t,e){const n=w2(t);return n[G(e,0,n.length-1)]}function Q(t,e){return(t==null?void 0:t.result.suggestionId)!==void 0&&(e==null?void 0:e.result.suggestionId)!==void 0&&t.result.suggestionId===e.result.suggestionId}function U2(t){return function(e){const n=e.length>1&&e.some(c=>c.qualifiedPathName.rootPath!==e[0].qualifiedPathName.rootPath),o=new Map;for(const c of e){const a=C2(c,n);o.has(a)?o.get(a).push(c):o.set(a,[c])}for(const[,c]of o)c.sort((a,i)=>a.lineRange.start-i.lineRange.start);return Array.from(o.entries()).sort(([c],[a])=>c.localeCompare(a))}(t.filter(e=>e.changeType!==s2.noop))}const X=t=>function*(...e){for(const n of e)yield*n}(...t.values()),w2=t=>Array.from(X(t)),V="data:image/svg+xml,%3csvg%20width='13'%20height='12'%20viewBox='0%200%2013%2012'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20id='&%23244;&%23128;&%23131;&%23179;'%20d='M2.37988%2011.9255C1.00879%2011.9255%200.297852%2011.2209%200.297852%209.86255V2.13745C0.297852%200.779053%201.00879%200.0744629%202.37988%200.0744629H10.0669C11.438%200.0744629%2012.1489%200.7854%2012.1489%202.13745V9.86255C12.1489%2011.2209%2011.438%2011.9255%2010.0669%2011.9255H2.37988ZM5.53467%209.13257C5.76953%209.13257%205.97266%209.01196%206.11865%208.79614L9.08936%204.16235C9.17822%204.02271%209.25439%203.86401%209.25439%203.71802C9.25439%203.38794%208.96875%203.16577%208.65137%203.16577C8.44824%203.16577%208.27051%203.27368%208.13721%203.49585L5.50928%207.698L4.28418%206.14282C4.12549%205.9397%203.97314%205.86353%203.77637%205.86353C3.44629%205.86353%203.19238%206.13013%203.19238%206.45386C3.19238%206.61255%203.24951%206.76489%203.36377%206.90454L4.91895%208.79614C5.09668%209.02466%205.29346%209.13257%205.53467%209.13257Z'%20fill='%23EBECF0'/%3e%3c/svg%3e",Z="data:image/svg+xml,%3csvg%20width='13'%20height='12'%20viewBox='0%200%2013%2012'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='M2.52881%2011.9255C1.15771%2011.9255%200.446777%2011.2209%200.446777%209.86255V2.13745C0.446777%200.779053%201.15771%200.0744629%202.52881%200.0744629H10.2158C11.5869%200.0744629%2012.2979%200.7854%2012.2979%202.13745V9.86255C12.2979%2011.2209%2011.5869%2011.9255%2010.2158%2011.9255H2.52881ZM4.30615%208.68823C4.47754%208.68823%204.62354%208.62476%204.74414%208.5105L6.38818%206.85376L8.04492%208.5105C8.15283%208.62476%208.30518%208.68823%208.47656%208.68823C8.81299%208.68823%209.07959%208.42163%209.07959%208.07886C9.07959%207.92017%209.01611%207.77417%208.90186%207.65991L7.24512%206.00317L8.9082%204.34009C9.02881%204.21948%209.07959%204.07983%209.07959%203.92114C9.07959%203.58472%208.81934%203.32446%208.48926%203.32446C8.31787%203.32446%208.18457%203.38159%208.06396%203.5022L6.38818%205.16528L4.7251%203.50854C4.61084%203.39429%204.47754%203.33716%204.30615%203.33716C3.96973%203.33716%203.70947%203.59106%203.70947%203.92749C3.70947%204.08618%203.77295%204.23218%203.88721%204.34644L5.54395%206.00317L3.88721%207.66626C3.77295%207.78052%203.70947%207.92651%203.70947%208.07886C3.70947%208.42163%203.96973%208.68823%204.30615%208.68823Z'%20fill='%23EBECF0'/%3e%3c/svg%3e",k="data:image/svg+xml,%3csvg%20xmlns='http://www.w3.org/2000/svg'%20viewBox='0%200%20512%20512'%3e%3c!--!%20Font%20Awesome%20Pro%206.7.2%20by%20@fontawesome%20-%20https://fontawesome.com%20License%20-%20https://fontawesome.com/license%20(Commercial%20License)%20Copyright%202024%20Fonticons,%20Inc.%20--%3e%3cpath%20d='M135%20369c9.4%209.4%2024.6%209.4%2033.9%200s9.4-24.6%200-33.9l-87-87L424%20248c22.1%200%2040%2017.9%2040%2040l0%20168c0%2013.3%2010.7%2024%2024%2024s24-10.7%2024-24l0-168c0-48.6-39.4-88-88-88L81.9%20200l87-87c9.4-9.4%209.4-24.6%200-33.9s-24.6-9.4-33.9%200L7%20207c-9.4%209.4-9.4%2024.6%200%2033.9L135%20369z'/%3e%3c/svg%3e";function D2(){}const J2=(...t)=>t.map(e=>$2[e]),$2={active:{action:"active",label:"Focus in Editor",icon:"data:image/svg+xml,%3csvg%20width='12'%20height='12'%20viewBox='0%200%2016%2016'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cg%20clip-path='url(%23clip0_2475_40556)'%3e%3crect%20width='16'%20height='16'%20/%3e%3cpath%20d='M1.97772%209.55176C1.78566%209.55176%201.62941%209.50781%201.50897%209.41992C1.38853%209.33203%201.29576%209.22298%201.23065%209.09277C1.1688%208.96257%201.12974%208.83561%201.11346%208.71191C1.08742%208.54264%201.06952%208.38477%201.05975%208.23828C1.04999%208.0918%201.0451%207.94857%201.0451%207.80859C1.0451%206.75716%201.29413%205.95638%201.79218%205.40625C2.29022%204.85612%203.06008%204.58105%204.10175%204.58105H6.758L7.82733%204.63965L6.9826%203.90234L5.88885%202.81348C5.81073%202.73535%205.74563%202.64746%205.69354%202.5498C5.64146%202.44889%205.61542%202.33171%205.61542%202.19824C5.61542%201.97038%205.69191%201.77995%205.84491%201.62695C5.9979%201.47396%206.20298%201.39746%206.46014%201.39746C6.67173%201.39746%206.86705%201.48535%207.04608%201.66113L10.1955%204.81055C10.3615%204.97331%2010.4445%205.18164%2010.4445%205.43555C10.4445%205.6862%2010.3615%205.89453%2010.1955%206.06055L7.04608%209.2002C6.86705%209.37923%206.67173%209.46875%206.46014%209.46875C6.20298%209.46875%205.9979%209.39062%205.84491%209.23438C5.69191%209.08138%205.61542%208.89095%205.61542%208.66309C5.61542%208.52962%205.64146%208.41406%205.69354%208.31641C5.74563%208.21875%205.81073%208.12923%205.88885%208.04785L6.9826%206.96387L7.82733%206.22168L6.758%206.28516H4.24823C3.8869%206.28516%203.59719%206.33724%203.37909%206.44141C3.16425%206.54232%203.00637%206.70508%202.90546%206.92969C2.8078%207.1543%202.75897%207.44564%202.75897%207.80371C2.75897%207.99902%202.76385%208.17155%202.77362%208.32129C2.78339%208.47103%202.78827%208.60286%202.78827%208.7168C2.78827%208.96745%202.71503%209.16927%202.56854%209.32227C2.42206%209.47526%202.22512%209.55176%201.97772%209.55176Z'%20fill='white'/%3e%3crect%20x='12'%20y='4.5'%20width='3'%20height='2'%20rx='1'%20fill='white'/%3e%3crect%20x='1'%20y='11.75'%20width='14'%20height='2'%20rx='1'%20fill='white'/%3e%3c/g%3e%3cdefs%3e%3cclipPath%20id='clip0_2475_40556'%3e%3crect%20width='16'%20height='16'%20fill='white'/%3e%3c/clipPath%3e%3c/defs%3e%3c/svg%3e"},accept:{action:"accept",label:"Accept",icon:V},reject:{action:"reject",label:"Reject",icon:Z},acceptAllInFile:{action:"acceptAllInFile",label:"Accept All",icon:V},rejectAllInFile:{action:"rejectAllInFile",label:"Reject All",icon:Z},undo:{action:"undo",label:"Undo",icon:k},undoAllInFile:{action:"undoAllInFile",label:"Undo All",icon:k},"|":"|"};function T(t,...e){return function(n){n.preventDefault(),n.stopPropagation(),n.stopImmediatePropagation(),t(...e)}}function W2(t,...e){return t.some(n=>e.some(o=>o!=null&&Q(o,n)))}function A2(t,e=!1){return t.split(`
`).length+(e&&t.endsWith(`
`)?-1:0)}function G2({lineRange:t,result:e}){let n=t.stop-t.start;return e.suggestedCode&&(n+=A2(e.suggestedCode,!0)),n}function Q2(t,e){return t===e||t.relPath===e.relPath&&t.rootPath===e.rootPath}function U(t,e,n){const o=t.slice();return o[4]=e[n],o}function b2(t){let e,n;return e=new a2({props:{variant:"ghost",color:"neutral",size:t[3]?1:2,title:t[4].label,$$slots:{default:[L2]},$$scope:{ctx:t}}}),e.$on("click",function(){t2(T(t[1],t[4].action,t[2]))&&T(t[1],t[4].action,t[2]).apply(this,arguments)}),{c(){y(e.$$.fragment)},m(o,c){j(e,o,c),n=!0},p(o,c){t=o;const a={};8&c&&(a.size=t[3]?1:2),1&c&&(a.title=t[4].label),129&c&&(a.$$scope={dirty:c,ctx:t}),e.$set(a)},i(o){n||(h(e.$$.fragment,o),n=!0)},o(o){d(e.$$.fragment,o),n=!1},d(o){L(e,o)}}}function x2(t){let e;return{c(){e=C("div"),g(e,"class","c-action-buttons__separator svelte-1ilhzlh")},m(n,o){v(n,e,o)},p:P,i:P,o:P,d(n){n&&m(e)}}}function L2(t){let e,n;return{c(){e=C("i"),n=I(),g(e,"class","c-action-buttons__icon svelte-1ilhzlh"),S(e,"--augment-action-button-icon-url","url("+JSON.stringify(t[4].icon)+")")},m(o,c){v(o,e,c),v(o,n,c)},p(o,c){1&c&&S(e,"--augment-action-button-icon-url","url("+JSON.stringify(o[4].icon)+")")},d(o){o&&(m(e),m(n))}}}function D(t){let e,n,o,c;const a=[x2,b2],i=[];function l(s,r){return s[4]==="|"?0:1}return e=l(t),n=i[e]=a[e](t),{c(){n.c(),o=e2()},m(s,r){i[e].m(s,r),v(s,o,r),c=!0},p(s,r){let w=e;e=l(s),e===w?i[e].p(s,r):(O(),d(i[w],1,1,()=>{i[w]=null}),M(),n=i[e],n?n.p(s,r):(n=i[e]=a[e](s),n.c()),h(n,1),n.m(o.parentNode,o))},i(s){c||(h(n),c=!0)},o(s){d(n),c=!1},d(s){s&&m(o),i[e].d(s)}}}function j2(t){let e,n,o=z(t[0]),c=[];for(let i=0;i<o.length;i+=1)c[i]=D(U(t,o,i));const a=i=>d(c[i],1,1,()=>{c[i]=null});return{c(){e=C("div");for(let i=0;i<c.length;i+=1)c[i].c();g(e,"class","c-action-buttons svelte-1ilhzlh")},m(i,l){v(i,e,l);for(let s=0;s<c.length;s+=1)c[s]&&c[s].m(e,null);n=!0},p(i,[l]){if(15&l){let s;for(o=z(i[0]),s=0;s<o.length;s+=1){const r=U(i,o,s);c[s]?(c[s].p(r,l),h(c[s],1)):(c[s]=D(r),c[s].c(),h(c[s],1),c[s].m(e,null))}for(O(),s=o.length;s<c.length;s+=1)a(s);M()}},i(i){if(!n){for(let l=0;l<o.length;l+=1)h(c[l]);n=!0}},o(i){c=c.filter(Boolean);for(let l=0;l<c.length;l+=1)d(c[l]);n=!1},d(i){i&&m(e),K(c,i)}}}function y2(t,e,n){let{actions:o}=e,{onAction:c}=e,{value:a}=e,{compact:i=!0}=e;return t.$$set=l=>{"actions"in l&&n(0,o=l.actions),"onAction"in l&&n(1,c=l.onAction),"value"in l&&n(2,a=l.value),"compact"in l&&n(3,i=l.compact)},[o,c,a,i]}class P2 extends F{constructor(e){super(),B(this,e,y2,j2,N,{actions:0,onAction:1,value:2,compact:3})}}function I2(t){let e,n,o,c;return o=new P2({props:{compact:!0,onAction:t[0],actions:t[2],value:t[1]}}),{c(){e=C("div"),n=C("div"),y(o.$$.fragment),g(n,"class","c-suggestion-tree-item-actions__inner svelte-jmxjvk"),g(e,"class","c-suggestion-tree-item-actions svelte-jmxjvk")},m(a,i){v(a,e,i),A(e,n),j(o,n,null),c=!0},p(a,[i]){const l={};1&i&&(l.onAction=a[0]),4&i&&(l.actions=a[2]),2&i&&(l.value=a[1]),o.$set(l)},i(a){c||(h(o.$$.fragment,a),c=!0)},o(a){d(o.$$.fragment,a),c=!1},d(a){a&&m(e),L(o)}}}function _2(t,e,n){let{onCodeAction:o}=e,{value:c}=e,{codeActions:a}=e;return t.$$set=i=>{"onCodeAction"in i&&n(0,o=i.onCodeAction),"value"in i&&n(1,c=i.value),"codeActions"in i&&n(2,a=i.codeActions)},[o,c,a]}class F2 extends F{constructor(e){super(),B(this,e,_2,I2,N,{onCodeAction:0,value:1,codeActions:2})}}function J(t){let e,n;return e=new F2({props:{onCodeAction:t[2],codeActions:t[4],value:t[1]}}),{c(){y(e.$$.fragment)},m(o,c){j(e,o,c),n=!0},p(o,c){const a={};4&c&&(a.onCodeAction=o[2]),16&c&&(a.codeActions=o[4]),2&c&&(a.value=o[1]),e.$set(a)},i(o){n||(h(e.$$.fragment,o),n=!0)},o(o){d(e.$$.fragment,o),n=!1},d(o){L(e,o)}}}function B2(t){let e,n,o,c,a,i,l,s,r,w,b=t[0].replace(/^\//,"")+"";n=new i2({props:{filename:t[0],class:"c-icon-file-path__lang-icon"}});let u=t[5]&&t[1].length&&t[4].length&&J(t);return{c(){e=C("div"),y(n.$$.fragment),o=I(),c=C("div"),a=c2(b),i=I(),u&&u.c(),g(c,"class","c-icon-file-path__path svelte-1o9auwr"),g(e,"class",l="c-icon-file-path "+t[3]+" svelte-1o9auwr"),g(e,"title",t[0]),g(e,"role","button"),g(e,"tabindex","0")},m(f,p){v(f,e,p),j(n,e,null),A(e,o),A(e,c),A(c,a),A(e,i),u&&u.m(e,null),s=!0,r||(w=[q(e,"mouseenter",t[6]),q(e,"mouseleave",t[7])],r=!0)},p(f,[p]){const R={};1&p&&(R.filename=f[0]),n.$set(R),(!s||1&p)&&b!==(b=f[0].replace(/^\//,"")+"")&&o2(a,b),f[5]&&f[1].length&&f[4].length?u?(u.p(f,p),50&p&&h(u,1)):(u=J(f),u.c(),h(u,1),u.m(e,null)):u&&(O(),d(u,1,1,()=>{u=null}),M()),(!s||8&p&&l!==(l="c-icon-file-path "+f[3]+" svelte-1o9auwr"))&&g(e,"class",l),(!s||1&p)&&g(e,"title",f[0])},i(f){s||(h(n.$$.fragment,f),h(u),s=!0)},o(f){d(n.$$.fragment,f),d(u),s=!1},d(f){f&&m(e),L(n),u&&u.d(),r=!1,n2(w)}}}function N2(t,e,n){let{filepath:o}=e,{value:c}=e,{onCodeAction:a}=e,{class:i=""}=e,l=!1,{codeActions:s=[]}=e;return t.$$set=r=>{"filepath"in r&&n(0,o=r.filepath),"value"in r&&n(1,c=r.value),"onCodeAction"in r&&n(2,a=r.onCodeAction),"class"in r&&n(3,i=r.class),"codeActions"in r&&n(4,s=r.codeActions)},[o,c,a,i,s,l,()=>n(5,l=!0),()=>n(5,l=!1)]}class X2 extends F{constructor(e){super(),B(this,e,N2,B2,N,{filepath:0,value:1,onCodeAction:2,class:3,codeActions:4})}}export{P2 as A,X2 as I,F2 as S,z2 as _,Q as a,Z2 as b,J2 as c,H2 as d,T2 as e,V2 as f,C2 as g,G2 as h,k2 as i,A2 as j,q2 as k,f2 as l,_ as m,D2 as n,E2 as o,l2 as p,Q2 as q,U2 as r,T as s,W2 as t};
