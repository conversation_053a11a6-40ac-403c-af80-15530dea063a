{"name": "@vscode/windows-registry", "version": "1.1.0", "description": "A native node module for accessing the windows registry", "main": "dist/index.js", "typings": "dist/index.d.ts", "scripts": {"compile": "tsc -p ./", "test": "mocha -r ts-node/register test/**/*.test.ts"}, "repository": {"type": "git", "url": "git+https://github.com/Microsoft/vscode-windows-registry.git"}, "author": "Microsoft Corporation", "license": "MIT", "bugs": {"url": "https://github.com/Microsoft/vscode-windows-registry/issues"}, "homepage": "https://github.com/Microsoft/vscode-windows-registry#readme", "devDependencies": {"@types/mocha": "^10.0.1", "@types/node": "^16.11.7", "mocha": "^10.2.0", "ts-node": "^10.9.1", "typescript": "^4.4.4"}}