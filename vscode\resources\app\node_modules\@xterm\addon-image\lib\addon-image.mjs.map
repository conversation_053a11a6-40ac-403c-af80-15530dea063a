{"version": 3, "sources": ["../node_modules/sixel/src/Colors.ts", "../node_modules/inwasm/src/index.ts", "../node_modules/xterm-wasm-parts/lib/base64/Base64Decoder.wasm.js", "../node_modules/sixel/src/wasm.ts", "../node_modules/sixel/src/Decoder.ts", "../src/ImageRenderer.ts", "../../../src/vs/base/common/errors.ts", "../../../src/vs/base/common/arraysFind.ts", "../../../src/vs/base/common/arrays.ts", "../../../src/vs/base/common/collections.ts", "../../../src/vs/base/common/map.ts", "../../../src/vs/base/common/functional.ts", "../../../src/vs/base/common/iterator.ts", "../../../src/vs/base/common/lifecycle.ts", "../src/ImageStorage.ts", "../src/IIPHandler.ts", "../src/IIPHeaderParser.ts", "../src/IIPMetrics.ts", "../src/SixelHandler.ts", "../src/ImageAddon.ts"], "sourcesContent": [null, null, "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\n/**\n * Copyright (c) 2023 The xterm.js authors. All rights reserved.\n * @license MIT\n */\nconst inwasm_1 = require(\"inwasm\");\n/**\n * wasm base64 decoder.\n */\nconst wasmDecode = (0, inwasm_1.InWasm)(/*inwasm#6ccf778dadd2ee40:rdef-start:\"decode\"*/{s:1,t:0,d:'AGFzbQEAAAABBQFgAAF/Ag8BA2VudgZtZW1vcnkCAAEDAwIAAAcNAgNkZWMAAANlbmQAAQqxAwKuAQEFf0GIKCgCAEGgKGohAUGEKCgCACIAQYAoKAIAQQFrQXxxIgJIBEAgAkGgKGohAyAAQaAoaiEAA0AgAC0AA0ECdCgCgCAgAC0AAkECdCgCgBggAC0AAUECdCgCgBAgAC0AAEECdCgCgAhycnIiBEH///8HSwRAQQEPCyABIAQ2AgAgAUEDaiEBIABBBGoiACADSQ0ACwtBhCggAjYCAEGIKCABQaAoazYCAEEAC/4BAQZ/AkBBgCgoAgAiAUGEKCgCACIAa0EFTgRAQQEhAxAADQFBgCgoAgAhAUGEKCgCACEAC0EBIQMgASAAayIEQQJIDQAgAEGhKGotAABBAnQoAoAQIABBoChqLQAAQQJ0KAKACHIhAQJAIARBAkYEQEEBIQIMAQtBASECIAAtAKIoIgVBPUcEQEECIQIgBUECdCgCgBggAXIhAQsgBEEERw0AIAAtAKMoIgBBPUYNACACQQFqIQIgAEECdCgCgCAgAXIhAQsgAUH///8HSw0AQYgoKAIAQaAoaiABNgIAQYgoQYgoKAIAIAJqIgA2AgAgAEGQKCgCAEchAwsgAwsAdglwcm9kdWNlcnMBDHByb2Nlc3NlZC1ieQEFY2xhbmdWMTguMC4wIChodHRwczovL2dpdGh1Yi5jb20vbGx2bS9sbHZtLXByb2plY3QgZDFlNjg1ZGY0NWRjNTk0NGI0M2QyNTQ3ZDAxMzhjZDRhM2VlNGVmZSkALA90YXJnZXRfZmVhdHVyZXMCKw9tdXRhYmxlLWdsb2JhbHMrCHNpZ24tZXh0'}/*inwasm#6ccf778dadd2ee40:rdef-end:\"decode\"*/);\n// base64 map\nconst MAP = new Uint8Array('ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/'\n    .split('')\n    .map(el => el.charCodeAt(0)));\n// init decoder maps in LE order\nconst D = new Uint32Array(1024);\nD.fill(0xFF000000);\nfor (let i = 0; i < MAP.length; ++i)\n    D[MAP[i]] = i << 2;\nfor (let i = 0; i < MAP.length; ++i)\n    D[256 + MAP[i]] = i >> 4 | ((i << 4) & 0xFF) << 8;\nfor (let i = 0; i < MAP.length; ++i)\n    D[512 + MAP[i]] = (i >> 2) << 8 | ((i << 6) & 0xFF) << 16;\nfor (let i = 0; i < MAP.length; ++i)\n    D[768 + MAP[i]] = i << 16;\nconst EMPTY = new Uint8Array(0);\n/**\n * base64 streamline inplace decoder.\n *\n * Features / assumptions:\n * - optimized uint32 read/write (only LE support!)\n * - lazy chunkwise decoding\n * - errors out on any non base64 chars (no support for NL formatted base64)\n * - decodes in wasm\n * - inplace decoding to save memory\n * - supports a keepSize for lazy memory release\n */\nclass Base64Decoder {\n    constructor(keepSize) {\n        this.keepSize = keepSize;\n    }\n    /**\n     * Currently decoded bytes (borrowed).\n     * Must be accessed before calling `release` or `init`.\n     */\n    get data8() {\n        return this._inst ? this._d.subarray(0, this._m32[1282 /* P32.STATE_DP */]) : EMPTY;\n    }\n    /**\n     * Release memory conditionally based on `keepSize`.\n     * If memory gets released, also the wasm instance will be freed and recreated on next `init`,\n     * otherwise the instance will be reused.\n     */\n    release() {\n        if (!this._inst)\n            return;\n        if (this._mem.buffer.byteLength > this.keepSize) {\n            this._inst = this._m32 = this._d = this._mem = null;\n        }\n        else {\n            this._m32[1280 /* P32.STATE_WP */] = 0;\n            this._m32[1281 /* P32.STATE_SP */] = 0;\n            this._m32[1282 /* P32.STATE_DP */] = 0;\n        }\n    }\n    /**\n     * Initializes the decoder for new base64 data.\n     * Must be called before doing any decoding attempts.\n     * `size` is the amount of decoded bytes to be expected.\n     * The method will either spawn a new wasm instance or grow\n     * the needed memory of an existing instance.\n     */\n    init(size) {\n        let m = this._m32;\n        const bytes = (Math.ceil(size / 3) + 1288 /* P32.STATE_DATA */) * 4;\n        if (!this._inst) {\n            this._mem = new WebAssembly.Memory({ initial: Math.ceil(bytes / 65536) });\n            this._inst = wasmDecode({ env: { memory: this._mem } });\n            m = new Uint32Array(this._mem.buffer, 0);\n            m.set(D, 256 /* P32.D0 */);\n            this._d = new Uint8Array(this._mem.buffer, 1288 /* P32.STATE_DATA */ * 4);\n        }\n        else if (this._mem.buffer.byteLength < bytes) {\n            this._mem.grow(Math.ceil((bytes - this._mem.buffer.byteLength) / 65536));\n            m = new Uint32Array(this._mem.buffer, 0);\n            this._d = new Uint8Array(this._mem.buffer, 1288 /* P32.STATE_DATA */ * 4);\n        }\n        m[1284 /* P32.STATE_BSIZE */] = size;\n        m[1283 /* P32.STATE_ESIZE */] = Math.ceil(size / 3) * 4;\n        m[1280 /* P32.STATE_WP */] = 0;\n        m[1281 /* P32.STATE_SP */] = 0;\n        m[1282 /* P32.STATE_DP */] = 0;\n        this._m32 = m;\n    }\n    /**\n     * Put bytes in `data` from `start` to `end` (exclusive) into the decoder.\n     * Also decodes base64 data inplace once the payload exceeds 2^17 bytes.\n     * Returns 1 on error, else 0.\n     */\n    put(data, start, end) {\n        if (!this._inst)\n            return 1;\n        const m = this._m32;\n        if (end - start + m[1280 /* P32.STATE_WP */] > m[1283 /* P32.STATE_ESIZE */])\n            return 1;\n        this._d.set(data.subarray(start, end), m[1280 /* P32.STATE_WP */]);\n        m[1280 /* P32.STATE_WP */] += end - start;\n        // max chunk in input handler is 2^17, try to run in \"tandem mode\"\n        // also assures that we dont run into illegal offsets in the wasm part\n        return m[1280 /* P32.STATE_WP */] - m[1281 /* P32.STATE_SP */] >= 131072 ? this._inst.exports.dec() : 0;\n    }\n    /**\n     * End the current decoding.\n     * Decodes leftover payload and finally checks for the correct amount of\n     * decoded bytes by comparing to the value given to `init`.\n     * Returns 1 on error, else 0.\n     */\n    end() {\n        return this._inst ? this._inst.exports.end() : 1;\n    }\n}\nexports.default = Base64Decoder;\n//# sourceMappingURL=Base64Decoder.wasm.js.map", null, null, "/**\n * Copyright (c) 2020 The xterm.js authors. All rights reserved.\n * @license MIT\n */\n\nimport { toRGBA8888 } from 'sixel/lib/Colors';\nimport { IDisposable } from '@xterm/xterm';\nimport { ICellSize, ITerminalExt, IImageSpec, IRenderDimensions, IRenderService } from './Types';\nimport { Disposable, MutableDisposable, toDisposable } from 'vs/base/common/lifecycle';\n\nconst PLACEHOLDER_LENGTH = 4096;\nconst PLACEHOLDER_HEIGHT = 24;\n\n/**\n * ImageRenderer - terminal frontend extension:\n * - provide primitives for canvas, ImageData, Bitmap (static)\n * - add canvas layer to DOM (browser only for now)\n * - draw image tiles onRender\n */\nexport class ImageRenderer extends Disposable implements IDisposable {\n  public canvas: HTMLCanvasElement | undefined;\n  private _ctx: CanvasRenderingContext2D | null | undefined;\n  private _placeholder: HTMLCanvasElement | undefined;\n  private _placeholderBitmap: ImageBitmap | undefined;\n  private _optionsRefresh = this._register(new MutableDisposable());\n  private _oldOpen: ((parent: HTMLElement) => void) | undefined;\n  private _renderService: IRenderService | undefined;\n  private _oldSetRenderer: ((renderer: any) => void) | undefined;\n\n  // drawing primitive - canvas\n  public static createCanvas(localDocument: Document | undefined, width: number, height: number): HTMLCanvasElement {\n    /**\n     * NOTE: We normally dont care, from which document the canvas\n     * gets created, so we can fall back to global document,\n     * if the terminal has no document associated yet.\n     * This way early image loads before calling .open keep working\n     * (still discouraged though, as the metrics will be screwed up).\n     * Only the DOM output canvas should be on the terminal's document,\n     * which gets explicitly checked in `insertLayerToDom`.\n     */\n    const canvas = (localDocument || document).createElement('canvas');\n    canvas.width = width | 0;\n    canvas.height = height | 0;\n    return canvas;\n  }\n\n  // drawing primitive - ImageData with optional buffer\n  public static createImageData(ctx: CanvasRenderingContext2D, width: number, height: number, buffer?: ArrayBuffer): ImageData {\n    if (typeof ImageData !== 'function') {\n      const imgData = ctx.createImageData(width, height);\n      if (buffer) {\n        imgData.data.set(new Uint8ClampedArray(buffer, 0, width * height * 4));\n      }\n      return imgData;\n    }\n    return buffer\n      ? new ImageData(new Uint8ClampedArray(buffer, 0, width * height * 4), width, height)\n      : new ImageData(width, height);\n  }\n\n  // drawing primitive - ImageBitmap\n  public static createImageBitmap(img: ImageBitmapSource): Promise<ImageBitmap | undefined> {\n    if (typeof createImageBitmap !== 'function') {\n      return Promise.resolve(undefined);\n    }\n    return createImageBitmap(img);\n  }\n\n\n  constructor(private _terminal: ITerminalExt) {\n    super();\n    this._oldOpen = this._terminal._core.open;\n    this._terminal._core.open = (parent: HTMLElement): void => {\n      this._oldOpen?.call(this._terminal._core, parent);\n      this._open();\n    };\n    if (this._terminal._core.screenElement) {\n      this._open();\n    }\n    // hack to spot fontSize changes\n    this._optionsRefresh.value = this._terminal._core.optionsService.onOptionChange(option => {\n      if (option === 'fontSize') {\n        this.rescaleCanvas();\n        this._renderService?.refreshRows(0, this._terminal.rows);\n      }\n    });\n    this._register(toDisposable(() => {\n      this.removeLayerFromDom();\n      if (this._terminal._core && this._oldOpen) {\n        this._terminal._core.open = this._oldOpen;\n        this._oldOpen = undefined;\n      }\n      if (this._renderService && this._oldSetRenderer) {\n        this._renderService.setRenderer = this._oldSetRenderer;\n        this._oldSetRenderer = undefined;\n      }\n      this._renderService = undefined;\n      this.canvas = undefined;\n      this._ctx = undefined;\n      this._placeholderBitmap?.close();\n      this._placeholderBitmap = undefined;\n      this._placeholder = undefined;\n    }));\n  }\n\n  /**\n   * Enable the placeholder.\n   */\n  public showPlaceholder(value: boolean): void {\n    if (value) {\n      if (!this._placeholder && this.cellSize.height !== -1) {\n        this._createPlaceHolder(Math.max(this.cellSize.height + 1, PLACEHOLDER_HEIGHT));\n      }\n    } else {\n      this._placeholderBitmap?.close();\n      this._placeholderBitmap = undefined;\n      this._placeholder = undefined;\n    }\n    this._renderService?.refreshRows(0, this._terminal.rows);\n  }\n\n  /**\n   * Dimensions of the terminal.\n   * Forwarded from internal render service.\n   */\n  public get dimensions(): IRenderDimensions | undefined {\n    return this._renderService?.dimensions;\n  }\n\n  /**\n   * Current cell size (float).\n   */\n  public get cellSize(): ICellSize {\n    return {\n      width: this.dimensions?.css.cell.width || -1,\n      height: this.dimensions?.css.cell.height || -1\n    };\n  }\n\n  /**\n   * Clear a region of the image layer canvas.\n   */\n  public clearLines(start: number, end: number): void {\n    this._ctx?.clearRect(\n      0,\n      start * (this.dimensions?.css.cell.height || 0),\n      this.dimensions?.css.canvas.width || 0,\n      (++end - start) * (this.dimensions?.css.cell.height || 0)\n    );\n  }\n\n  /**\n   * Clear whole image canvas.\n   */\n  public clearAll(): void {\n    this._ctx?.clearRect(0, 0, this.canvas?.width || 0, this.canvas?.height || 0);\n  }\n\n  /**\n   * Draw neighboring tiles on the image layer canvas.\n   */\n  public draw(imgSpec: IImageSpec, tileId: number, col: number, row: number, count: number = 1): void {\n    if (!this._ctx) {\n      return;\n    }\n    const { width, height } = this.cellSize;\n\n    // Don't try to draw anything, if we cannot get valid renderer metrics.\n    if (width === -1 || height === -1) {\n      return;\n    }\n\n    this._rescaleImage(imgSpec, width, height);\n    const img = imgSpec.actual!;\n    const cols = Math.ceil(img.width / width);\n\n    const sx = (tileId % cols) * width;\n    const sy = Math.floor(tileId / cols) * height;\n    const dx = col * width;\n    const dy = row * height;\n\n    // safari bug: never access image source out of bounds\n    const finalWidth = count * width + sx > img.width ? img.width - sx : count * width;\n    const finalHeight = sy + height > img.height ? img.height - sy : height;\n\n    // Floor all pixel offsets to get stable tile mapping without any overflows.\n    // Note: For not pixel perfect aligned cells like in the DOM renderer\n    // this will move a tile slightly to the top/left (subpixel range, thus ignore it).\n    // FIX #34: avoid striping on displays with pixelDeviceRatio != 1 by ceiling height and width\n    this._ctx.drawImage(\n      img,\n      Math.floor(sx), Math.floor(sy), Math.ceil(finalWidth), Math.ceil(finalHeight),\n      Math.floor(dx), Math.floor(dy), Math.ceil(finalWidth), Math.ceil(finalHeight)\n    );\n  }\n\n  /**\n   * Extract a single tile from an image.\n   */\n  public extractTile(imgSpec: IImageSpec, tileId: number): HTMLCanvasElement | undefined {\n    const { width, height } = this.cellSize;\n    // Don't try to draw anything, if we cannot get valid renderer metrics.\n    if (width === -1 || height === -1) {\n      return;\n    }\n    this._rescaleImage(imgSpec, width, height);\n    const img = imgSpec.actual!;\n    const cols = Math.ceil(img.width / width);\n    const sx = (tileId % cols) * width;\n    const sy = Math.floor(tileId / cols) * height;\n    const finalWidth = width + sx > img.width ? img.width - sx : width;\n    const finalHeight = sy + height > img.height ? img.height - sy : height;\n\n    const canvas = ImageRenderer.createCanvas(this.document, finalWidth, finalHeight);\n    const ctx = canvas.getContext('2d');\n    if (ctx) {\n      ctx.drawImage(\n        img,\n        Math.floor(sx), Math.floor(sy), Math.floor(finalWidth), Math.floor(finalHeight),\n        0, 0, Math.floor(finalWidth), Math.floor(finalHeight)\n      );\n      return canvas;\n    }\n  }\n\n  /**\n   * Draw a line with placeholder on the image layer canvas.\n   */\n  public drawPlaceholder(col: number, row: number, count: number = 1): void {\n    if (this._ctx) {\n      const { width, height } = this.cellSize;\n\n      // Don't try to draw anything, if we cannot get valid renderer metrics.\n      if (width === -1 || height === -1) {\n        return;\n      }\n\n      if (!this._placeholder) {\n        this._createPlaceHolder(Math.max(height + 1, PLACEHOLDER_HEIGHT));\n      } else if (height >= this._placeholder!.height) {\n        this._createPlaceHolder(height + 1);\n      }\n      if (!this._placeholder) return;\n      this._ctx.drawImage(\n        this._placeholderBitmap || this._placeholder!,\n        col * width,\n        (row * height) % 2 ? 0 : 1,  // needs %2 offset correction\n        width * count,\n        height,\n        col * width,\n        row * height,\n        width * count,\n        height\n      );\n    }\n  }\n\n  /**\n   * Rescale image layer canvas if needed.\n   * Checked once from `ImageStorage.render`.\n   */\n  public rescaleCanvas(): void {\n    if (!this.canvas) {\n      return;\n    }\n    if (this.canvas.width !== this.dimensions!.css.canvas.width || this.canvas.height !== this.dimensions!.css.canvas.height) {\n      this.canvas.width = this.dimensions!.css.canvas.width || 0;\n      this.canvas.height = this.dimensions!.css.canvas.height || 0;\n    }\n  }\n\n  /**\n   * Rescale image in storage if needed.\n   */\n  private _rescaleImage(spec: IImageSpec, currentWidth: number, currentHeight: number): void {\n    if (currentWidth === spec.actualCellSize.width && currentHeight === spec.actualCellSize.height) {\n      return;\n    }\n    const { width: originalWidth, height: originalHeight } = spec.origCellSize;\n    if (currentWidth === originalWidth && currentHeight === originalHeight) {\n      spec.actual = spec.orig;\n      spec.actualCellSize.width = originalWidth;\n      spec.actualCellSize.height = originalHeight;\n      return;\n    }\n    const canvas = ImageRenderer.createCanvas(\n      this.document,\n      Math.ceil(spec.orig!.width * currentWidth / originalWidth),\n      Math.ceil(spec.orig!.height * currentHeight / originalHeight)\n    );\n    const ctx = canvas.getContext('2d');\n    if (ctx) {\n      ctx.drawImage(spec.orig!, 0, 0, canvas.width, canvas.height);\n      spec.actual = canvas;\n      spec.actualCellSize.width = currentWidth;\n      spec.actualCellSize.height = currentHeight;\n    }\n  }\n\n  /**\n   * Lazy init for the renderer.\n   */\n  private _open(): void {\n    this._renderService = this._terminal._core._renderService;\n    this._oldSetRenderer = this._renderService.setRenderer.bind(this._renderService);\n    this._renderService.setRenderer = (renderer: any) => {\n      this.removeLayerFromDom();\n      this._oldSetRenderer?.call(this._renderService, renderer);\n    };\n  }\n\n  public insertLayerToDom(): void {\n    // make sure that the terminal is attached to a document and to DOM\n    if (this.document && this._terminal._core.screenElement) {\n      if (!this.canvas) {\n        this.canvas = ImageRenderer.createCanvas(\n          this.document, this.dimensions?.css.canvas.width || 0,\n          this.dimensions?.css.canvas.height || 0\n        );\n        this.canvas.classList.add('xterm-image-layer');\n        this._terminal._core.screenElement.appendChild(this.canvas);\n        this._ctx = this.canvas.getContext('2d', { alpha: true, desynchronized: true });\n        this.clearAll();\n      }\n    } else {\n      console.warn('image addon: cannot insert output canvas to DOM, missing document or screenElement');\n    }\n  }\n\n  public removeLayerFromDom(): void {\n    if (this.canvas) {\n      this._ctx = undefined;\n      this.canvas.remove();\n      this.canvas = undefined;\n    }\n  }\n\n  private _createPlaceHolder(height: number = PLACEHOLDER_HEIGHT): void {\n    this._placeholderBitmap?.close();\n    this._placeholderBitmap = undefined;\n\n    // create blueprint to fill placeholder with\n    const bWidth = 32;  // must be 2^n\n    const blueprint = ImageRenderer.createCanvas(this.document, bWidth, height);\n    const ctx = blueprint.getContext('2d', { alpha: false });\n    if (!ctx) return;\n    const imgData = ImageRenderer.createImageData(ctx, bWidth, height);\n    const d32 = new Uint32Array(imgData.data.buffer);\n    const black = toRGBA8888(0, 0, 0);\n    const white = toRGBA8888(255, 255, 255);\n    d32.fill(black);\n    for (let y = 0; y < height; ++y) {\n      const shift = y % 2;\n      const offset = y * bWidth;\n      for (let x = 0; x < bWidth; x += 2) {\n        d32[offset + x + shift] = white;\n      }\n    }\n    ctx.putImageData(imgData, 0, 0);\n\n    // create placeholder line, width aligned to blueprint width\n    const width = (screen.width + bWidth - 1) & ~(bWidth - 1) || PLACEHOLDER_LENGTH;\n    this._placeholder = ImageRenderer.createCanvas(this.document, width, height);\n    const ctx2 = this._placeholder.getContext('2d', { alpha: false });\n    if (!ctx2) {\n      this._placeholder = undefined;\n      return;\n    }\n    for (let i = 0; i < width; i += bWidth) {\n      ctx2.drawImage(blueprint, i, 0);\n    }\n    ImageRenderer.createImageBitmap(this._placeholder).then(bitmap => this._placeholderBitmap = bitmap);\n  }\n\n  public get document(): Document | undefined {\n    return this._terminal._core._coreBrowserService?.window.document;\n  }\n}\n", "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\nexport interface ErrorListenerCallback {\n\t(error: any): void;\n}\n\nexport interface ErrorListenerUnbind {\n\t(): void;\n}\n\n// Avoid circular dependency on EventEmitter by implementing a subset of the interface.\nexport class ErrorHandler {\n\tprivate unexpectedErrorHandler: (e: any) => void;\n\tprivate listeners: ErrorListenerCallback[];\n\n\tconstructor() {\n\n\t\tthis.listeners = [];\n\n\t\tthis.unexpectedErrorHandler = function (e: any) {\n\t\t\tsetTimeout(() => {\n\t\t\t\tif (e.stack) {\n\t\t\t\t\tif (ErrorNoTelemetry.isErrorNoTelemetry(e)) {\n\t\t\t\t\t\tthrow new ErrorNoTelemetry(e.message + '\\n\\n' + e.stack);\n\t\t\t\t\t}\n\n\t\t\t\t\tthrow new Error(e.message + '\\n\\n' + e.stack);\n\t\t\t\t}\n\n\t\t\t\tthrow e;\n\t\t\t}, 0);\n\t\t};\n\t}\n\n\taddListener(listener: ErrorListenerCallback): ErrorListenerUnbind {\n\t\tthis.listeners.push(listener);\n\n\t\treturn () => {\n\t\t\tthis._removeListener(listener);\n\t\t};\n\t}\n\n\tprivate emit(e: any): void {\n\t\tthis.listeners.forEach((listener) => {\n\t\t\tlistener(e);\n\t\t});\n\t}\n\n\tprivate _removeListener(listener: ErrorListenerCallback): void {\n\t\tthis.listeners.splice(this.listeners.indexOf(listener), 1);\n\t}\n\n\tsetUnexpectedErrorHandler(newUnexpectedErrorHandler: (e: any) => void): void {\n\t\tthis.unexpectedErrorHandler = newUnexpectedErrorHandler;\n\t}\n\n\tgetUnexpectedErrorHandler(): (e: any) => void {\n\t\treturn this.unexpectedErrorHandler;\n\t}\n\n\tonUnexpectedError(e: any): void {\n\t\tthis.unexpectedErrorHandler(e);\n\t\tthis.emit(e);\n\t}\n\n\t// For external errors, we don't want the listeners to be called\n\tonUnexpectedExternalError(e: any): void {\n\t\tthis.unexpectedErrorHandler(e);\n\t}\n}\n\nexport const errorHandler = new ErrorHandler();\n\n/** @skipMangle */\nexport function setUnexpectedErrorHandler(newUnexpectedErrorHandler: (e: any) => void): void {\n\terrorHandler.setUnexpectedErrorHandler(newUnexpectedErrorHandler);\n}\n\n/**\n * Returns if the error is a SIGPIPE error. SIGPIPE errors should generally be\n * logged at most once, to avoid a loop.\n *\n * @see https://github.com/microsoft/vscode-remote-release/issues/6481\n */\nexport function isSigPipeError(e: unknown): e is Error {\n\tif (!e || typeof e !== 'object') {\n\t\treturn false;\n\t}\n\n\tconst cast = e as Record<string, string | undefined>;\n\treturn cast.code === 'EPIPE' && cast.syscall?.toUpperCase() === 'WRITE';\n}\n\nexport function onUnexpectedError(e: any): undefined {\n\t// ignore errors from cancelled promises\n\tif (!isCancellationError(e)) {\n\t\terrorHandler.onUnexpectedError(e);\n\t}\n\treturn undefined;\n}\n\nexport function onUnexpectedExternalError(e: any): undefined {\n\t// ignore errors from cancelled promises\n\tif (!isCancellationError(e)) {\n\t\terrorHandler.onUnexpectedExternalError(e);\n\t}\n\treturn undefined;\n}\n\nexport interface SerializedError {\n\treadonly $isError: true;\n\treadonly name: string;\n\treadonly message: string;\n\treadonly stack: string;\n\treadonly noTelemetry: boolean;\n}\n\nexport function transformErrorForSerialization(error: Error): SerializedError;\nexport function transformErrorForSerialization(error: any): any;\nexport function transformErrorForSerialization(error: any): any {\n\tif (error instanceof Error) {\n\t\tconst { name, message } = error;\n\t\tconst stack: string = (<any>error).stacktrace || (<any>error).stack;\n\t\treturn {\n\t\t\t$isError: true,\n\t\t\tname,\n\t\t\tmessage,\n\t\t\tstack,\n\t\t\tnoTelemetry: ErrorNoTelemetry.isErrorNoTelemetry(error)\n\t\t};\n\t}\n\n\t// return as is\n\treturn error;\n}\n\nexport function transformErrorFromSerialization(data: SerializedError): Error {\n\tlet error: Error;\n\tif (data.noTelemetry) {\n\t\terror = new ErrorNoTelemetry();\n\t} else {\n\t\terror = new Error();\n\t\terror.name = data.name;\n\t}\n\terror.message = data.message;\n\terror.stack = data.stack;\n\treturn error;\n}\n\n// see https://github.com/v8/v8/wiki/Stack%20Trace%20API#basic-stack-traces\nexport interface V8CallSite {\n\tgetThis(): unknown;\n\tgetTypeName(): string | null;\n\tgetFunction(): Function | undefined;\n\tgetFunctionName(): string | null;\n\tgetMethodName(): string | null;\n\tgetFileName(): string | null;\n\tgetLineNumber(): number | null;\n\tgetColumnNumber(): number | null;\n\tgetEvalOrigin(): string | undefined;\n\tisToplevel(): boolean;\n\tisEval(): boolean;\n\tisNative(): boolean;\n\tisConstructor(): boolean;\n\ttoString(): string;\n}\n\nconst canceledName = 'Canceled';\n\n/**\n * Checks if the given error is a promise in canceled state\n */\nexport function isCancellationError(error: any): boolean {\n\tif (error instanceof CancellationError) {\n\t\treturn true;\n\t}\n\treturn error instanceof Error && error.name === canceledName && error.message === canceledName;\n}\n\n// !!!IMPORTANT!!!\n// Do NOT change this class because it is also used as an API-type.\nexport class CancellationError extends Error {\n\tconstructor() {\n\t\tsuper(canceledName);\n\t\tthis.name = this.message;\n\t}\n}\n\n/**\n * @deprecated use {@link CancellationError `new CancellationError()`} instead\n */\nexport function canceled(): Error {\n\tconst error = new Error(canceledName);\n\terror.name = error.message;\n\treturn error;\n}\n\nexport function illegalArgument(name?: string): Error {\n\tif (name) {\n\t\treturn new Error(`Illegal argument: ${name}`);\n\t} else {\n\t\treturn new Error('Illegal argument');\n\t}\n}\n\nexport function illegalState(name?: string): Error {\n\tif (name) {\n\t\treturn new Error(`Illegal state: ${name}`);\n\t} else {\n\t\treturn new Error('Illegal state');\n\t}\n}\n\nexport class ReadonlyError extends TypeError {\n\tconstructor(name?: string) {\n\t\tsuper(name ? `${name} is read-only and cannot be changed` : 'Cannot change read-only property');\n\t}\n}\n\nexport function getErrorMessage(err: any): string {\n\tif (!err) {\n\t\treturn 'Error';\n\t}\n\n\tif (err.message) {\n\t\treturn err.message;\n\t}\n\n\tif (err.stack) {\n\t\treturn err.stack.split('\\n')[0];\n\t}\n\n\treturn String(err);\n}\n\nexport class NotImplementedError extends Error {\n\tconstructor(message?: string) {\n\t\tsuper('NotImplemented');\n\t\tif (message) {\n\t\t\tthis.message = message;\n\t\t}\n\t}\n}\n\nexport class NotSupportedError extends Error {\n\tconstructor(message?: string) {\n\t\tsuper('NotSupported');\n\t\tif (message) {\n\t\t\tthis.message = message;\n\t\t}\n\t}\n}\n\nexport class ExpectedError extends Error {\n\treadonly isExpected = true;\n}\n\n/**\n * Error that when thrown won't be logged in telemetry as an unhandled error.\n */\nexport class ErrorNoTelemetry extends Error {\n\toverride readonly name: string;\n\n\tconstructor(msg?: string) {\n\t\tsuper(msg);\n\t\tthis.name = 'CodeExpectedError';\n\t}\n\n\tpublic static fromError(err: Error): ErrorNoTelemetry {\n\t\tif (err instanceof ErrorNoTelemetry) {\n\t\t\treturn err;\n\t\t}\n\n\t\tconst result = new ErrorNoTelemetry();\n\t\tresult.message = err.message;\n\t\tresult.stack = err.stack;\n\t\treturn result;\n\t}\n\n\tpublic static isErrorNoTelemetry(err: Error): err is ErrorNoTelemetry {\n\t\treturn err.name === 'CodeExpectedError';\n\t}\n}\n\n/**\n * This error indicates a bug.\n * Do not throw this for invalid user input.\n * Only catch this error to recover gracefully from bugs.\n */\nexport class BugIndicatingError extends Error {\n\tconstructor(message?: string) {\n\t\tsuper(message || 'An unexpected bug occurred.');\n\t\tObject.setPrototypeOf(this, BugIndicatingError.prototype);\n\n\t\t// Because we know for sure only buggy code throws this,\n\t\t// we definitely want to break here and fix the bug.\n\t\t// eslint-disable-next-line no-debugger\n\t\t// debugger;\n\t}\n}\n", "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\nimport { Comparator } from './arrays';\n\nexport function findLast<T>(array: readonly T[], predicate: (item: T) => boolean): T | undefined {\n\tconst idx = findLastIdx(array, predicate);\n\tif (idx === -1) {\n\t\treturn undefined;\n\t}\n\treturn array[idx];\n}\n\nexport function findLastIdx<T>(array: readonly T[], predicate: (item: T) => boolean, fromIndex = array.length - 1): number {\n\tfor (let i = fromIndex; i >= 0; i--) {\n\t\tconst element = array[i];\n\n\t\tif (predicate(element)) {\n\t\t\treturn i;\n\t\t}\n\t}\n\n\treturn -1;\n}\n\n/**\n * Finds the last item where predicate is true using binary search.\n * `predicate` must be monotonous, i.e. `arr.map(predicate)` must be like `[true, ..., true, false, ..., false]`!\n *\n * @returns `undefined` if no item matches, otherwise the last item that matches the predicate.\n */\nexport function findLastMonotonous<T>(array: readonly T[], predicate: (item: T) => boolean): T | undefined {\n\tconst idx = findLastIdxMonotonous(array, predicate);\n\treturn idx === -1 ? undefined : array[idx];\n}\n\n/**\n * Finds the last item where predicate is true using binary search.\n * `predicate` must be monotonous, i.e. `arr.map(predicate)` must be like `[true, ..., true, false, ..., false]`!\n *\n * @returns `startIdx - 1` if predicate is false for all items, otherwise the index of the last item that matches the predicate.\n */\nexport function findLastIdxMonotonous<T>(array: readonly T[], predicate: (item: T) => boolean, startIdx = 0, endIdxEx = array.length): number {\n\tlet i = startIdx;\n\tlet j = endIdxEx;\n\twhile (i < j) {\n\t\tconst k = Math.floor((i + j) / 2);\n\t\tif (predicate(array[k])) {\n\t\t\ti = k + 1;\n\t\t} else {\n\t\t\tj = k;\n\t\t}\n\t}\n\treturn i - 1;\n}\n\n/**\n * Finds the first item where predicate is true using binary search.\n * `predicate` must be monotonous, i.e. `arr.map(predicate)` must be like `[false, ..., false, true, ..., true]`!\n *\n * @returns `undefined` if no item matches, otherwise the first item that matches the predicate.\n */\nexport function findFirstMonotonous<T>(array: readonly T[], predicate: (item: T) => boolean): T | undefined {\n\tconst idx = findFirstIdxMonotonousOrArrLen(array, predicate);\n\treturn idx === array.length ? undefined : array[idx];\n}\n\n/**\n * Finds the first item where predicate is true using binary search.\n * `predicate` must be monotonous, i.e. `arr.map(predicate)` must be like `[false, ..., false, true, ..., true]`!\n *\n * @returns `endIdxEx` if predicate is false for all items, otherwise the index of the first item that matches the predicate.\n */\nexport function findFirstIdxMonotonousOrArrLen<T>(array: readonly T[], predicate: (item: T) => boolean, startIdx = 0, endIdxEx = array.length): number {\n\tlet i = startIdx;\n\tlet j = endIdxEx;\n\twhile (i < j) {\n\t\tconst k = Math.floor((i + j) / 2);\n\t\tif (predicate(array[k])) {\n\t\t\tj = k;\n\t\t} else {\n\t\t\ti = k + 1;\n\t\t}\n\t}\n\treturn i;\n}\n\nexport function findFirstIdxMonotonous<T>(array: readonly T[], predicate: (item: T) => boolean, startIdx = 0, endIdxEx = array.length): number {\n\tconst idx = findFirstIdxMonotonousOrArrLen(array, predicate, startIdx, endIdxEx);\n\treturn idx === array.length ? -1 : idx;\n}\n\n/**\n * Use this when\n * * You have a sorted array\n * * You query this array with a monotonous predicate to find the last item that has a certain property.\n * * You query this array multiple times with monotonous predicates that get weaker and weaker.\n */\nexport class MonotonousArray<T> {\n\tpublic static assertInvariants = false;\n\n\tprivate _findLastMonotonousLastIdx = 0;\n\tprivate _prevFindLastPredicate: ((item: T) => boolean) | undefined;\n\n\tconstructor(private readonly _array: readonly T[]) {\n\t}\n\n\t/**\n\t * The predicate must be monotonous, i.e. `arr.map(predicate)` must be like `[true, ..., true, false, ..., false]`!\n\t * For subsequent calls, current predicate must be weaker than (or equal to) the previous predicate, i.e. more entries must be `true`.\n\t */\n\tfindLastMonotonous(predicate: (item: T) => boolean): T | undefined {\n\t\tif (MonotonousArray.assertInvariants) {\n\t\t\tif (this._prevFindLastPredicate) {\n\t\t\t\tfor (const item of this._array) {\n\t\t\t\t\tif (this._prevFindLastPredicate(item) && !predicate(item)) {\n\t\t\t\t\t\tthrow new Error('MonotonousArray: current predicate must be weaker than (or equal to) the previous predicate.');\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\tthis._prevFindLastPredicate = predicate;\n\t\t}\n\n\t\tconst idx = findLastIdxMonotonous(this._array, predicate, this._findLastMonotonousLastIdx);\n\t\tthis._findLastMonotonousLastIdx = idx + 1;\n\t\treturn idx === -1 ? undefined : this._array[idx];\n\t}\n}\n\n/**\n * Returns the first item that is equal to or greater than every other item.\n*/\nexport function findFirstMax<T>(array: readonly T[], comparator: Comparator<T>): T | undefined {\n\tif (array.length === 0) {\n\t\treturn undefined;\n\t}\n\n\tlet max = array[0];\n\tfor (let i = 1; i < array.length; i++) {\n\t\tconst item = array[i];\n\t\tif (comparator(item, max) > 0) {\n\t\t\tmax = item;\n\t\t}\n\t}\n\treturn max;\n}\n\n/**\n * Returns the last item that is equal to or greater than every other item.\n*/\nexport function findLastMax<T>(array: readonly T[], comparator: Comparator<T>): T | undefined {\n\tif (array.length === 0) {\n\t\treturn undefined;\n\t}\n\n\tlet max = array[0];\n\tfor (let i = 1; i < array.length; i++) {\n\t\tconst item = array[i];\n\t\tif (comparator(item, max) >= 0) {\n\t\t\tmax = item;\n\t\t}\n\t}\n\treturn max;\n}\n\n/**\n * Returns the first item that is equal to or less than every other item.\n*/\nexport function findFirstMin<T>(array: readonly T[], comparator: Comparator<T>): T | undefined {\n\treturn findFirstMax(array, (a, b) => -comparator(a, b));\n}\n\nexport function findMaxIdx<T>(array: readonly T[], comparator: Comparator<T>): number {\n\tif (array.length === 0) {\n\t\treturn -1;\n\t}\n\n\tlet maxIdx = 0;\n\tfor (let i = 1; i < array.length; i++) {\n\t\tconst item = array[i];\n\t\tif (comparator(item, array[maxIdx]) > 0) {\n\t\t\tmaxIdx = i;\n\t\t}\n\t}\n\treturn maxIdx;\n}\n\n/**\n * Returns the first mapped value of the array which is not undefined.\n */\nexport function mapFindFirst<T, R>(items: Iterable<T>, mapFn: (value: T) => R | undefined): R | undefined {\n\tfor (const value of items) {\n\t\tconst mapped = mapFn(value);\n\t\tif (mapped !== undefined) {\n\t\t\treturn mapped;\n\t\t}\n\t}\n\n\treturn undefined;\n}\n", "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\nimport { CancellationToken } from 'vs/base/common/cancellation';\nimport { CancellationError } from 'vs/base/common/errors';\nimport { ISplice } from 'vs/base/common/sequence';\nimport { findFirstIdxMonotonousOrArrLen } from './arraysFind';\n\n/**\n * Returns the last element of an array.\n * @param array The array.\n * @param n Which element from the end (default is zero).\n */\nexport function tail<T>(array: ArrayLike<T>, n: number = 0): T | undefined {\n\treturn array[array.length - (1 + n)];\n}\n\nexport function tail2<T>(arr: T[]): [T[], T] {\n\tif (arr.length === 0) {\n\t\tthrow new Error('Invalid tail call');\n\t}\n\n\treturn [arr.slice(0, arr.length - 1), arr[arr.length - 1]];\n}\n\nexport function equals<T>(one: ReadonlyArray<T> | undefined, other: ReadonlyArray<T> | undefined, itemEquals: (a: T, b: T) => boolean = (a, b) => a === b): boolean {\n\tif (one === other) {\n\t\treturn true;\n\t}\n\n\tif (!one || !other) {\n\t\treturn false;\n\t}\n\n\tif (one.length !== other.length) {\n\t\treturn false;\n\t}\n\n\tfor (let i = 0, len = one.length; i < len; i++) {\n\t\tif (!itemEquals(one[i], other[i])) {\n\t\t\treturn false;\n\t\t}\n\t}\n\n\treturn true;\n}\n\n/**\n * Remove the element at `index` by replacing it with the last element. This is faster than `splice`\n * but changes the order of the array\n */\nexport function removeFastWithoutKeepingOrder<T>(array: T[], index: number) {\n\tconst last = array.length - 1;\n\tif (index < last) {\n\t\tarray[index] = array[last];\n\t}\n\tarray.pop();\n}\n\n/**\n * Performs a binary search algorithm over a sorted array.\n *\n * @param array The array being searched.\n * @param key The value we search for.\n * @param comparator A function that takes two array elements and returns zero\n *   if they are equal, a negative number if the first element precedes the\n *   second one in the sorting order, or a positive number if the second element\n *   precedes the first one.\n * @return See {@link binarySearch2}\n */\nexport function binarySearch<T>(array: ReadonlyArray<T>, key: T, comparator: (op1: T, op2: T) => number): number {\n\treturn binarySearch2(array.length, i => comparator(array[i], key));\n}\n\n/**\n * Performs a binary search algorithm over a sorted collection. Useful for cases\n * when we need to perform a binary search over something that isn't actually an\n * array, and converting data to an array would defeat the use of binary search\n * in the first place.\n *\n * @param length The collection length.\n * @param compareToKey A function that takes an index of an element in the\n *   collection and returns zero if the value at this index is equal to the\n *   search key, a negative number if the value precedes the search key in the\n *   sorting order, or a positive number if the search key precedes the value.\n * @return A non-negative index of an element, if found. If not found, the\n *   result is -(n+1) (or ~n, using bitwise notation), where n is the index\n *   where the key should be inserted to maintain the sorting order.\n */\nexport function binarySearch2(length: number, compareToKey: (index: number) => number): number {\n\tlet low = 0,\n\t\thigh = length - 1;\n\n\twhile (low <= high) {\n\t\tconst mid = ((low + high) / 2) | 0;\n\t\tconst comp = compareToKey(mid);\n\t\tif (comp < 0) {\n\t\t\tlow = mid + 1;\n\t\t} else if (comp > 0) {\n\t\t\thigh = mid - 1;\n\t\t} else {\n\t\t\treturn mid;\n\t\t}\n\t}\n\treturn -(low + 1);\n}\n\ntype Compare<T> = (a: T, b: T) => number;\n\n\nexport function quickSelect<T>(nth: number, data: T[], compare: Compare<T>): T {\n\n\tnth = nth | 0;\n\n\tif (nth >= data.length) {\n\t\tthrow new TypeError('invalid index');\n\t}\n\n\tconst pivotValue = data[Math.floor(data.length * Math.random())];\n\tconst lower: T[] = [];\n\tconst higher: T[] = [];\n\tconst pivots: T[] = [];\n\n\tfor (const value of data) {\n\t\tconst val = compare(value, pivotValue);\n\t\tif (val < 0) {\n\t\t\tlower.push(value);\n\t\t} else if (val > 0) {\n\t\t\thigher.push(value);\n\t\t} else {\n\t\t\tpivots.push(value);\n\t\t}\n\t}\n\n\tif (nth < lower.length) {\n\t\treturn quickSelect(nth, lower, compare);\n\t} else if (nth < lower.length + pivots.length) {\n\t\treturn pivots[0];\n\t} else {\n\t\treturn quickSelect(nth - (lower.length + pivots.length), higher, compare);\n\t}\n}\n\nexport function groupBy<T>(data: ReadonlyArray<T>, compare: (a: T, b: T) => number): T[][] {\n\tconst result: T[][] = [];\n\tlet currentGroup: T[] | undefined = undefined;\n\tfor (const element of data.slice(0).sort(compare)) {\n\t\tif (!currentGroup || compare(currentGroup[0], element) !== 0) {\n\t\t\tcurrentGroup = [element];\n\t\t\tresult.push(currentGroup);\n\t\t} else {\n\t\t\tcurrentGroup.push(element);\n\t\t}\n\t}\n\treturn result;\n}\n\n/**\n * Splits the given items into a list of (non-empty) groups.\n * `shouldBeGrouped` is used to decide if two consecutive items should be in the same group.\n * The order of the items is preserved.\n */\nexport function* groupAdjacentBy<T>(items: Iterable<T>, shouldBeGrouped: (item1: T, item2: T) => boolean): Iterable<T[]> {\n\tlet currentGroup: T[] | undefined;\n\tlet last: T | undefined;\n\tfor (const item of items) {\n\t\tif (last !== undefined && shouldBeGrouped(last, item)) {\n\t\t\tcurrentGroup!.push(item);\n\t\t} else {\n\t\t\tif (currentGroup) {\n\t\t\t\tyield currentGroup;\n\t\t\t}\n\t\t\tcurrentGroup = [item];\n\t\t}\n\t\tlast = item;\n\t}\n\tif (currentGroup) {\n\t\tyield currentGroup;\n\t}\n}\n\nexport function forEachAdjacent<T>(arr: T[], f: (item1: T | undefined, item2: T | undefined) => void): void {\n\tfor (let i = 0; i <= arr.length; i++) {\n\t\tf(i === 0 ? undefined : arr[i - 1], i === arr.length ? undefined : arr[i]);\n\t}\n}\n\nexport function forEachWithNeighbors<T>(arr: T[], f: (before: T | undefined, element: T, after: T | undefined) => void): void {\n\tfor (let i = 0; i < arr.length; i++) {\n\t\tf(i === 0 ? undefined : arr[i - 1], arr[i], i + 1 === arr.length ? undefined : arr[i + 1]);\n\t}\n}\n\ninterface IMutableSplice<T> extends ISplice<T> {\n\treadonly toInsert: T[];\n\tdeleteCount: number;\n}\n\n/**\n * Diffs two *sorted* arrays and computes the splices which apply the diff.\n */\nexport function sortedDiff<T>(before: ReadonlyArray<T>, after: ReadonlyArray<T>, compare: (a: T, b: T) => number): ISplice<T>[] {\n\tconst result: IMutableSplice<T>[] = [];\n\n\tfunction pushSplice(start: number, deleteCount: number, toInsert: T[]): void {\n\t\tif (deleteCount === 0 && toInsert.length === 0) {\n\t\t\treturn;\n\t\t}\n\n\t\tconst latest = result[result.length - 1];\n\n\t\tif (latest && latest.start + latest.deleteCount === start) {\n\t\t\tlatest.deleteCount += deleteCount;\n\t\t\tlatest.toInsert.push(...toInsert);\n\t\t} else {\n\t\t\tresult.push({ start, deleteCount, toInsert });\n\t\t}\n\t}\n\n\tlet beforeIdx = 0;\n\tlet afterIdx = 0;\n\n\twhile (true) {\n\t\tif (beforeIdx === before.length) {\n\t\t\tpushSplice(beforeIdx, 0, after.slice(afterIdx));\n\t\t\tbreak;\n\t\t}\n\t\tif (afterIdx === after.length) {\n\t\t\tpushSplice(beforeIdx, before.length - beforeIdx, []);\n\t\t\tbreak;\n\t\t}\n\n\t\tconst beforeElement = before[beforeIdx];\n\t\tconst afterElement = after[afterIdx];\n\t\tconst n = compare(beforeElement, afterElement);\n\t\tif (n === 0) {\n\t\t\t// equal\n\t\t\tbeforeIdx += 1;\n\t\t\tafterIdx += 1;\n\t\t} else if (n < 0) {\n\t\t\t// beforeElement is smaller -> before element removed\n\t\t\tpushSplice(beforeIdx, 1, []);\n\t\t\tbeforeIdx += 1;\n\t\t} else if (n > 0) {\n\t\t\t// beforeElement is greater -> after element added\n\t\t\tpushSplice(beforeIdx, 0, [afterElement]);\n\t\t\tafterIdx += 1;\n\t\t}\n\t}\n\n\treturn result;\n}\n\n/**\n * Takes two *sorted* arrays and computes their delta (removed, added elements).\n * Finishes in `Math.min(before.length, after.length)` steps.\n */\nexport function delta<T>(before: ReadonlyArray<T>, after: ReadonlyArray<T>, compare: (a: T, b: T) => number): { removed: T[]; added: T[] } {\n\tconst splices = sortedDiff(before, after, compare);\n\tconst removed: T[] = [];\n\tconst added: T[] = [];\n\n\tfor (const splice of splices) {\n\t\tremoved.push(...before.slice(splice.start, splice.start + splice.deleteCount));\n\t\tadded.push(...splice.toInsert);\n\t}\n\n\treturn { removed, added };\n}\n\n/**\n * Returns the top N elements from the array.\n *\n * Faster than sorting the entire array when the array is a lot larger than N.\n *\n * @param array The unsorted array.\n * @param compare A sort function for the elements.\n * @param n The number of elements to return.\n * @return The first n elements from array when sorted with compare.\n */\nexport function top<T>(array: ReadonlyArray<T>, compare: (a: T, b: T) => number, n: number): T[] {\n\tif (n === 0) {\n\t\treturn [];\n\t}\n\tconst result = array.slice(0, n).sort(compare);\n\ttopStep(array, compare, result, n, array.length);\n\treturn result;\n}\n\n/**\n * Asynchronous variant of `top()` allowing for splitting up work in batches between which the event loop can run.\n *\n * Returns the top N elements from the array.\n *\n * Faster than sorting the entire array when the array is a lot larger than N.\n *\n * @param array The unsorted array.\n * @param compare A sort function for the elements.\n * @param n The number of elements to return.\n * @param batch The number of elements to examine before yielding to the event loop.\n * @return The first n elements from array when sorted with compare.\n */\nexport function topAsync<T>(array: T[], compare: (a: T, b: T) => number, n: number, batch: number, token?: CancellationToken): Promise<T[]> {\n\tif (n === 0) {\n\t\treturn Promise.resolve([]);\n\t}\n\n\treturn new Promise((resolve, reject) => {\n\t\t(async () => {\n\t\t\tconst o = array.length;\n\t\t\tconst result = array.slice(0, n).sort(compare);\n\t\t\tfor (let i = n, m = Math.min(n + batch, o); i < o; i = m, m = Math.min(m + batch, o)) {\n\t\t\t\tif (i > n) {\n\t\t\t\t\tawait new Promise(resolve => setTimeout(resolve)); // any other delay function would starve I/O\n\t\t\t\t}\n\t\t\t\tif (token && token.isCancellationRequested) {\n\t\t\t\t\tthrow new CancellationError();\n\t\t\t\t}\n\t\t\t\ttopStep(array, compare, result, i, m);\n\t\t\t}\n\t\t\treturn result;\n\t\t})()\n\t\t\t.then(resolve, reject);\n\t});\n}\n\nfunction topStep<T>(array: ReadonlyArray<T>, compare: (a: T, b: T) => number, result: T[], i: number, m: number): void {\n\tfor (const n = result.length; i < m; i++) {\n\t\tconst element = array[i];\n\t\tif (compare(element, result[n - 1]) < 0) {\n\t\t\tresult.pop();\n\t\t\tconst j = findFirstIdxMonotonousOrArrLen(result, e => compare(element, e) < 0);\n\t\t\tresult.splice(j, 0, element);\n\t\t}\n\t}\n}\n\n/**\n * @returns New array with all falsy values removed. The original array IS NOT modified.\n */\nexport function coalesce<T>(array: ReadonlyArray<T | undefined | null>): T[] {\n\treturn array.filter((e): e is T => !!e);\n}\n\n/**\n * Remove all falsy values from `array`. The original array IS modified.\n */\nexport function coalesceInPlace<T>(array: Array<T | undefined | null>): asserts array is Array<T> {\n\tlet to = 0;\n\tfor (let i = 0; i < array.length; i++) {\n\t\tif (!!array[i]) {\n\t\t\tarray[to] = array[i];\n\t\t\tto += 1;\n\t\t}\n\t}\n\tarray.length = to;\n}\n\n/**\n * @deprecated Use `Array.copyWithin` instead\n */\nexport function move(array: any[], from: number, to: number): void {\n\tarray.splice(to, 0, array.splice(from, 1)[0]);\n}\n\n/**\n * @returns false if the provided object is an array and not empty.\n */\nexport function isFalsyOrEmpty(obj: any): boolean {\n\treturn !Array.isArray(obj) || obj.length === 0;\n}\n\n/**\n * @returns True if the provided object is an array and has at least one element.\n */\nexport function isNonEmptyArray<T>(obj: T[] | undefined | null): obj is T[];\nexport function isNonEmptyArray<T>(obj: readonly T[] | undefined | null): obj is readonly T[];\nexport function isNonEmptyArray<T>(obj: T[] | readonly T[] | undefined | null): obj is T[] | readonly T[] {\n\treturn Array.isArray(obj) && obj.length > 0;\n}\n\n/**\n * Removes duplicates from the given array. The optional keyFn allows to specify\n * how elements are checked for equality by returning an alternate value for each.\n */\nexport function distinct<T>(array: ReadonlyArray<T>, keyFn: (value: T) => any = value => value): T[] {\n\tconst seen = new Set<any>();\n\n\treturn array.filter(element => {\n\t\tconst key = keyFn!(element);\n\t\tif (seen.has(key)) {\n\t\t\treturn false;\n\t\t}\n\t\tseen.add(key);\n\t\treturn true;\n\t});\n}\n\nexport function uniqueFilter<T, R>(keyFn: (t: T) => R): (t: T) => boolean {\n\tconst seen = new Set<R>();\n\n\treturn element => {\n\t\tconst key = keyFn(element);\n\n\t\tif (seen.has(key)) {\n\t\t\treturn false;\n\t\t}\n\n\t\tseen.add(key);\n\t\treturn true;\n\t};\n}\n\nexport function firstOrDefault<T, NotFound = T>(array: ReadonlyArray<T>, notFoundValue: NotFound): T | NotFound;\nexport function firstOrDefault<T>(array: ReadonlyArray<T>): T | undefined;\nexport function firstOrDefault<T, NotFound = T>(array: ReadonlyArray<T>, notFoundValue?: NotFound): T | NotFound | undefined {\n\treturn array.length > 0 ? array[0] : notFoundValue;\n}\n\nexport function lastOrDefault<T, NotFound = T>(array: ReadonlyArray<T>, notFoundValue: NotFound): T | NotFound;\nexport function lastOrDefault<T>(array: ReadonlyArray<T>): T | undefined;\nexport function lastOrDefault<T, NotFound = T>(array: ReadonlyArray<T>, notFoundValue?: NotFound): T | NotFound | undefined {\n\treturn array.length > 0 ? array[array.length - 1] : notFoundValue;\n}\n\nexport function commonPrefixLength<T>(one: ReadonlyArray<T>, other: ReadonlyArray<T>, equals: (a: T, b: T) => boolean = (a, b) => a === b): number {\n\tlet result = 0;\n\n\tfor (let i = 0, len = Math.min(one.length, other.length); i < len && equals(one[i], other[i]); i++) {\n\t\tresult++;\n\t}\n\n\treturn result;\n}\n\nexport function range(to: number): number[];\nexport function range(from: number, to: number): number[];\nexport function range(arg: number, to?: number): number[] {\n\tlet from = typeof to === 'number' ? arg : 0;\n\n\tif (typeof to === 'number') {\n\t\tfrom = arg;\n\t} else {\n\t\tfrom = 0;\n\t\tto = arg;\n\t}\n\n\tconst result: number[] = [];\n\n\tif (from <= to) {\n\t\tfor (let i = from; i < to; i++) {\n\t\t\tresult.push(i);\n\t\t}\n\t} else {\n\t\tfor (let i = from; i > to; i--) {\n\t\t\tresult.push(i);\n\t\t}\n\t}\n\n\treturn result;\n}\n\nexport function index<T>(array: ReadonlyArray<T>, indexer: (t: T) => string): { [key: string]: T };\nexport function index<T, R>(array: ReadonlyArray<T>, indexer: (t: T) => string, mapper: (t: T) => R): { [key: string]: R };\nexport function index<T, R>(array: ReadonlyArray<T>, indexer: (t: T) => string, mapper?: (t: T) => R): { [key: string]: R } {\n\treturn array.reduce((r, t) => {\n\t\tr[indexer(t)] = mapper ? mapper(t) : t;\n\t\treturn r;\n\t}, Object.create(null));\n}\n\n/**\n * Inserts an element into an array. Returns a function which, when\n * called, will remove that element from the array.\n *\n * @deprecated In almost all cases, use a `Set<T>` instead.\n */\nexport function insert<T>(array: T[], element: T): () => void {\n\tarray.push(element);\n\n\treturn () => remove(array, element);\n}\n\n/**\n * Removes an element from an array if it can be found.\n *\n * @deprecated In almost all cases, use a `Set<T>` instead.\n */\nexport function remove<T>(array: T[], element: T): T | undefined {\n\tconst index = array.indexOf(element);\n\tif (index > -1) {\n\t\tarray.splice(index, 1);\n\n\t\treturn element;\n\t}\n\n\treturn undefined;\n}\n\n/**\n * Insert `insertArr` inside `target` at `insertIndex`.\n * Please don't touch unless you understand https://jsperf.com/inserting-an-array-within-an-array\n */\nexport function arrayInsert<T>(target: T[], insertIndex: number, insertArr: T[]): T[] {\n\tconst before = target.slice(0, insertIndex);\n\tconst after = target.slice(insertIndex);\n\treturn before.concat(insertArr, after);\n}\n\n/**\n * Uses Fisher-Yates shuffle to shuffle the given array\n */\nexport function shuffle<T>(array: T[], _seed?: number): void {\n\tlet rand: () => number;\n\n\tif (typeof _seed === 'number') {\n\t\tlet seed = _seed;\n\t\t// Seeded random number generator in JS. Modified from:\n\t\t// https://stackoverflow.com/questions/521295/seeding-the-random-number-generator-in-javascript\n\t\trand = () => {\n\t\t\tconst x = Math.sin(seed++) * 179426549; // throw away most significant digits and reduce any potential bias\n\t\t\treturn x - Math.floor(x);\n\t\t};\n\t} else {\n\t\trand = Math.random;\n\t}\n\n\tfor (let i = array.length - 1; i > 0; i -= 1) {\n\t\tconst j = Math.floor(rand() * (i + 1));\n\t\tconst temp = array[i];\n\t\tarray[i] = array[j];\n\t\tarray[j] = temp;\n\t}\n}\n\n/**\n * Pushes an element to the start of the array, if found.\n */\nexport function pushToStart<T>(arr: T[], value: T): void {\n\tconst index = arr.indexOf(value);\n\n\tif (index > -1) {\n\t\tarr.splice(index, 1);\n\t\tarr.unshift(value);\n\t}\n}\n\n/**\n * Pushes an element to the end of the array, if found.\n */\nexport function pushToEnd<T>(arr: T[], value: T): void {\n\tconst index = arr.indexOf(value);\n\n\tif (index > -1) {\n\t\tarr.splice(index, 1);\n\t\tarr.push(value);\n\t}\n}\n\nexport function pushMany<T>(arr: T[], items: ReadonlyArray<T>): void {\n\tfor (const item of items) {\n\t\tarr.push(item);\n\t}\n}\n\nexport function mapArrayOrNot<T, U>(items: T | T[], fn: (_: T) => U): U | U[] {\n\treturn Array.isArray(items) ?\n\t\titems.map(fn) :\n\t\tfn(items);\n}\n\nexport function asArray<T>(x: T | T[]): T[];\nexport function asArray<T>(x: T | readonly T[]): readonly T[];\nexport function asArray<T>(x: T | T[]): T[] {\n\treturn Array.isArray(x) ? x : [x];\n}\n\nexport function getRandomElement<T>(arr: T[]): T | undefined {\n\treturn arr[Math.floor(Math.random() * arr.length)];\n}\n\n/**\n * Insert the new items in the array.\n * @param array The original array.\n * @param start The zero-based location in the array from which to start inserting elements.\n * @param newItems The items to be inserted\n */\nexport function insertInto<T>(array: T[], start: number, newItems: T[]): void {\n\tconst startIdx = getActualStartIndex(array, start);\n\tconst originalLength = array.length;\n\tconst newItemsLength = newItems.length;\n\tarray.length = originalLength + newItemsLength;\n\t// Move the items after the start index, start from the end so that we don't overwrite any value.\n\tfor (let i = originalLength - 1; i >= startIdx; i--) {\n\t\tarray[i + newItemsLength] = array[i];\n\t}\n\n\tfor (let i = 0; i < newItemsLength; i++) {\n\t\tarray[i + startIdx] = newItems[i];\n\t}\n}\n\n/**\n * Removes elements from an array and inserts new elements in their place, returning the deleted elements. Alternative to the native Array.splice method, it\n * can only support limited number of items due to the maximum call stack size limit.\n * @param array The original array.\n * @param start The zero-based location in the array from which to start removing elements.\n * @param deleteCount The number of elements to remove.\n * @returns An array containing the elements that were deleted.\n */\nexport function splice<T>(array: T[], start: number, deleteCount: number, newItems: T[]): T[] {\n\tconst index = getActualStartIndex(array, start);\n\tlet result = array.splice(index, deleteCount);\n\tif (result === undefined) {\n\t\t// see https://bugs.webkit.org/show_bug.cgi?id=261140\n\t\tresult = [];\n\t}\n\tinsertInto(array, index, newItems);\n\treturn result;\n}\n\n/**\n * Determine the actual start index (same logic as the native splice() or slice())\n * If greater than the length of the array, start will be set to the length of the array. In this case, no element will be deleted but the method will behave as an adding function, adding as many element as item[n*] provided.\n * If negative, it will begin that many elements from the end of the array. (In this case, the origin -1, meaning -n is the index of the nth last element, and is therefore equivalent to the index of array.length - n.) If array.length + start is less than 0, it will begin from index 0.\n * @param array The target array.\n * @param start The operation index.\n */\nfunction getActualStartIndex<T>(array: T[], start: number): number {\n\treturn start < 0 ? Math.max(start + array.length, 0) : Math.min(start, array.length);\n}\n\n/**\n * When comparing two values,\n * a negative number indicates that the first value is less than the second,\n * a positive number indicates that the first value is greater than the second,\n * and zero indicates that neither is the case.\n*/\nexport type CompareResult = number;\n\nexport namespace CompareResult {\n\texport function isLessThan(result: CompareResult): boolean {\n\t\treturn result < 0;\n\t}\n\n\texport function isLessThanOrEqual(result: CompareResult): boolean {\n\t\treturn result <= 0;\n\t}\n\n\texport function isGreaterThan(result: CompareResult): boolean {\n\t\treturn result > 0;\n\t}\n\n\texport function isNeitherLessOrGreaterThan(result: CompareResult): boolean {\n\t\treturn result === 0;\n\t}\n\n\texport const greaterThan = 1;\n\texport const lessThan = -1;\n\texport const neitherLessOrGreaterThan = 0;\n}\n\n/**\n * A comparator `c` defines a total order `<=` on `T` as following:\n * `c(a, b) <= 0` iff `a` <= `b`.\n * We also have `c(a, b) == 0` iff `c(b, a) == 0`.\n*/\nexport type Comparator<T> = (a: T, b: T) => CompareResult;\n\nexport function compareBy<TItem, TCompareBy>(selector: (item: TItem) => TCompareBy, comparator: Comparator<TCompareBy>): Comparator<TItem> {\n\treturn (a, b) => comparator(selector(a), selector(b));\n}\n\nexport function tieBreakComparators<TItem>(...comparators: Comparator<TItem>[]): Comparator<TItem> {\n\treturn (item1, item2) => {\n\t\tfor (const comparator of comparators) {\n\t\t\tconst result = comparator(item1, item2);\n\t\t\tif (!CompareResult.isNeitherLessOrGreaterThan(result)) {\n\t\t\t\treturn result;\n\t\t\t}\n\t\t}\n\t\treturn CompareResult.neitherLessOrGreaterThan;\n\t};\n}\n\n/**\n * The natural order on numbers.\n*/\nexport const numberComparator: Comparator<number> = (a, b) => a - b;\n\nexport const booleanComparator: Comparator<boolean> = (a, b) => numberComparator(a ? 1 : 0, b ? 1 : 0);\n\nexport function reverseOrder<TItem>(comparator: Comparator<TItem>): Comparator<TItem> {\n\treturn (a, b) => -comparator(a, b);\n}\n\nexport class ArrayQueue<T> {\n\tprivate firstIdx = 0;\n\tprivate lastIdx = this.items.length - 1;\n\n\t/**\n\t * Constructs a queue that is backed by the given array. Runtime is O(1).\n\t*/\n\tconstructor(private readonly items: readonly T[]) { }\n\n\tget length(): number {\n\t\treturn this.lastIdx - this.firstIdx + 1;\n\t}\n\n\t/**\n\t * Consumes elements from the beginning of the queue as long as the predicate returns true.\n\t * If no elements were consumed, `null` is returned. Has a runtime of O(result.length).\n\t*/\n\ttakeWhile(predicate: (value: T) => boolean): T[] | null {\n\t\t// P(k) := k <= this.lastIdx && predicate(this.items[k])\n\t\t// Find s := min { k | k >= this.firstIdx && !P(k) } and return this.data[this.firstIdx...s)\n\n\t\tlet startIdx = this.firstIdx;\n\t\twhile (startIdx < this.items.length && predicate(this.items[startIdx])) {\n\t\t\tstartIdx++;\n\t\t}\n\t\tconst result = startIdx === this.firstIdx ? null : this.items.slice(this.firstIdx, startIdx);\n\t\tthis.firstIdx = startIdx;\n\t\treturn result;\n\t}\n\n\t/**\n\t * Consumes elements from the end of the queue as long as the predicate returns true.\n\t * If no elements were consumed, `null` is returned.\n\t * The result has the same order as the underlying array!\n\t*/\n\ttakeFromEndWhile(predicate: (value: T) => boolean): T[] | null {\n\t\t// P(k) := this.firstIdx >= k && predicate(this.items[k])\n\t\t// Find s := max { k | k <= this.lastIdx && !P(k) } and return this.data(s...this.lastIdx]\n\n\t\tlet endIdx = this.lastIdx;\n\t\twhile (endIdx >= 0 && predicate(this.items[endIdx])) {\n\t\t\tendIdx--;\n\t\t}\n\t\tconst result = endIdx === this.lastIdx ? null : this.items.slice(endIdx + 1, this.lastIdx + 1);\n\t\tthis.lastIdx = endIdx;\n\t\treturn result;\n\t}\n\n\tpeek(): T | undefined {\n\t\tif (this.length === 0) {\n\t\t\treturn undefined;\n\t\t}\n\t\treturn this.items[this.firstIdx];\n\t}\n\n\tpeekLast(): T | undefined {\n\t\tif (this.length === 0) {\n\t\t\treturn undefined;\n\t\t}\n\t\treturn this.items[this.lastIdx];\n\t}\n\n\tdequeue(): T | undefined {\n\t\tconst result = this.items[this.firstIdx];\n\t\tthis.firstIdx++;\n\t\treturn result;\n\t}\n\n\tremoveLast(): T | undefined {\n\t\tconst result = this.items[this.lastIdx];\n\t\tthis.lastIdx--;\n\t\treturn result;\n\t}\n\n\ttakeCount(count: number): T[] {\n\t\tconst result = this.items.slice(this.firstIdx, this.firstIdx + count);\n\t\tthis.firstIdx += count;\n\t\treturn result;\n\t}\n}\n\n/**\n * This class is faster than an iterator and array for lazy computed data.\n*/\nexport class CallbackIterable<T> {\n\tpublic static readonly empty = new CallbackIterable<never>(_callback => { });\n\n\tconstructor(\n\t\t/**\n\t\t * Calls the callback for every item.\n\t\t * Stops when the callback returns false.\n\t\t*/\n\t\tpublic readonly iterate: (callback: (item: T) => boolean) => void\n\t) {\n\t}\n\n\tforEach(handler: (item: T) => void) {\n\t\tthis.iterate(item => { handler(item); return true; });\n\t}\n\n\ttoArray(): T[] {\n\t\tconst result: T[] = [];\n\t\tthis.iterate(item => { result.push(item); return true; });\n\t\treturn result;\n\t}\n\n\tfilter(predicate: (item: T) => boolean): CallbackIterable<T> {\n\t\treturn new CallbackIterable(cb => this.iterate(item => predicate(item) ? cb(item) : true));\n\t}\n\n\tmap<TResult>(mapFn: (item: T) => TResult): CallbackIterable<TResult> {\n\t\treturn new CallbackIterable<TResult>(cb => this.iterate(item => cb(mapFn(item))));\n\t}\n\n\tsome(predicate: (item: T) => boolean): boolean {\n\t\tlet result = false;\n\t\tthis.iterate(item => { result = predicate(item); return !result; });\n\t\treturn result;\n\t}\n\n\tfindFirst(predicate: (item: T) => boolean): T | undefined {\n\t\tlet result: T | undefined;\n\t\tthis.iterate(item => {\n\t\t\tif (predicate(item)) {\n\t\t\t\tresult = item;\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\treturn true;\n\t\t});\n\t\treturn result;\n\t}\n\n\tfindLast(predicate: (item: T) => boolean): T | undefined {\n\t\tlet result: T | undefined;\n\t\tthis.iterate(item => {\n\t\t\tif (predicate(item)) {\n\t\t\t\tresult = item;\n\t\t\t}\n\t\t\treturn true;\n\t\t});\n\t\treturn result;\n\t}\n\n\tfindLastMaxBy(comparator: Comparator<T>): T | undefined {\n\t\tlet result: T | undefined;\n\t\tlet first = true;\n\t\tthis.iterate(item => {\n\t\t\tif (first || CompareResult.isGreaterThan(comparator(item, result!))) {\n\t\t\t\tfirst = false;\n\t\t\t\tresult = item;\n\t\t\t}\n\t\t\treturn true;\n\t\t});\n\t\treturn result;\n\t}\n}\n\n/**\n * Represents a re-arrangement of items in an array.\n */\nexport class Permutation {\n\tconstructor(private readonly _indexMap: readonly number[]) { }\n\n\t/**\n\t * Returns a permutation that sorts the given array according to the given compare function.\n\t */\n\tpublic static createSortPermutation<T>(arr: readonly T[], compareFn: (a: T, b: T) => number): Permutation {\n\t\tconst sortIndices = Array.from(arr.keys()).sort((index1, index2) => compareFn(arr[index1], arr[index2]));\n\t\treturn new Permutation(sortIndices);\n\t}\n\n\t/**\n\t * Returns a new array with the elements of the given array re-arranged according to this permutation.\n\t */\n\tapply<T>(arr: readonly T[]): T[] {\n\t\treturn arr.map((_, index) => arr[this._indexMap[index]]);\n\t}\n\n\t/**\n\t * Returns a new permutation that undoes the re-arrangement of this permutation.\n\t*/\n\tinverse(): Permutation {\n\t\tconst inverseIndexMap = this._indexMap.slice();\n\t\tfor (let i = 0; i < this._indexMap.length; i++) {\n\t\t\tinverseIndexMap[this._indexMap[i]] = i;\n\t\t}\n\t\treturn new Permutation(inverseIndexMap);\n\t}\n}\n", "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\n/**\n * An interface for a JavaScript object that\n * acts a dictionary. The keys are strings.\n */\nexport type IStringDictionary<V> = Record<string, V>;\n\n/**\n * An interface for a JavaScript object that\n * acts a dictionary. The keys are numbers.\n */\nexport type INumberDictionary<V> = Record<number, V>;\n\n/**\n * Groups the collection into a dictionary based on the provided\n * group function.\n */\nexport function groupBy<K extends string | number | symbol, V>(data: V[], groupFn: (element: V) => K): Record<K, V[]> {\n\tconst result: Record<K, V[]> = Object.create(null);\n\tfor (const element of data) {\n\t\tconst key = groupFn(element);\n\t\tlet target = result[key];\n\t\tif (!target) {\n\t\t\ttarget = result[key] = [];\n\t\t}\n\t\ttarget.push(element);\n\t}\n\treturn result;\n}\n\nexport function diffSets<T>(before: Set<T>, after: Set<T>): { removed: T[]; added: T[] } {\n\tconst removed: T[] = [];\n\tconst added: T[] = [];\n\tfor (const element of before) {\n\t\tif (!after.has(element)) {\n\t\t\tremoved.push(element);\n\t\t}\n\t}\n\tfor (const element of after) {\n\t\tif (!before.has(element)) {\n\t\t\tadded.push(element);\n\t\t}\n\t}\n\treturn { removed, added };\n}\n\nexport function diffMaps<K, V>(before: Map<K, V>, after: Map<K, V>): { removed: V[]; added: V[] } {\n\tconst removed: V[] = [];\n\tconst added: V[] = [];\n\tfor (const [index, value] of before) {\n\t\tif (!after.has(index)) {\n\t\t\tremoved.push(value);\n\t\t}\n\t}\n\tfor (const [index, value] of after) {\n\t\tif (!before.has(index)) {\n\t\t\tadded.push(value);\n\t\t}\n\t}\n\treturn { removed, added };\n}\n\n/**\n * Computes the intersection of two sets.\n *\n * @param setA - The first set.\n * @param setB - The second iterable.\n * @returns A new set containing the elements that are in both `setA` and `setB`.\n */\nexport function intersection<T>(setA: Set<T>, setB: Iterable<T>): Set<T> {\n\tconst result = new Set<T>();\n\tfor (const elem of setB) {\n\t\tif (setA.has(elem)) {\n\t\t\tresult.add(elem);\n\t\t}\n\t}\n\treturn result;\n}\n\nexport class SetWithKey<T> implements Set<T> {\n\tprivate _map = new Map<any, T>();\n\n\tconstructor(values: T[], private toKey: (t: T) => any) {\n\t\tfor (const value of values) {\n\t\t\tthis.add(value);\n\t\t}\n\t}\n\n\tget size(): number {\n\t\treturn this._map.size;\n\t}\n\n\tadd(value: T): this {\n\t\tconst key = this.toKey(value);\n\t\tthis._map.set(key, value);\n\t\treturn this;\n\t}\n\n\tdelete(value: T): boolean {\n\t\treturn this._map.delete(this.toKey(value));\n\t}\n\n\thas(value: T): boolean {\n\t\treturn this._map.has(this.toKey(value));\n\t}\n\n\t*entries(): IterableIterator<[T, T]> {\n\t\tfor (const entry of this._map.values()) {\n\t\t\tyield [entry, entry];\n\t\t}\n\t}\n\n\tkeys(): IterableIterator<T> {\n\t\treturn this.values();\n\t}\n\n\t*values(): IterableIterator<T> {\n\t\tfor (const entry of this._map.values()) {\n\t\t\tyield entry;\n\t\t}\n\t}\n\n\tclear(): void {\n\t\tthis._map.clear();\n\t}\n\n\tforEach(callbackfn: (value: T, value2: T, set: Set<T>) => void, thisArg?: any): void {\n\t\tthis._map.forEach(entry => callbackfn.call(thisArg, entry, entry, this));\n\t}\n\n\t[Symbol.iterator](): IterableIterator<T> {\n\t\treturn this.values();\n\t}\n\n\t[Symbol.toStringTag]: string = 'SetWithKey';\n}\n", "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\nexport function getOrSet<K, V>(map: Map<K, V>, key: K, value: V): V {\n\tlet result = map.get(key);\n\tif (result === undefined) {\n\t\tresult = value;\n\t\tmap.set(key, result);\n\t}\n\n\treturn result;\n}\n\nexport function mapToString<K, V>(map: Map<K, V>): string {\n\tconst entries: string[] = [];\n\tmap.forEach((value, key) => {\n\t\tentries.push(`${key} => ${value}`);\n\t});\n\n\treturn `Map(${map.size}) {${entries.join(', ')}}`;\n}\n\nexport function setToString<K>(set: Set<K>): string {\n\tconst entries: K[] = [];\n\tset.forEach(value => {\n\t\tentries.push(value);\n\t});\n\n\treturn `Set(${set.size}) {${entries.join(', ')}}`;\n}\n\nexport const enum Touch {\n\tNone = 0,\n\tAsOld = 1,\n\tAsNew = 2\n}\n\nexport class CounterSet<T> {\n\n\tprivate map = new Map<T, number>();\n\n\tadd(value: T): CounterSet<T> {\n\t\tthis.map.set(value, (this.map.get(value) || 0) + 1);\n\t\treturn this;\n\t}\n\n\tdelete(value: T): boolean {\n\t\tlet counter = this.map.get(value) || 0;\n\n\t\tif (counter === 0) {\n\t\t\treturn false;\n\t\t}\n\n\t\tcounter--;\n\n\t\tif (counter === 0) {\n\t\t\tthis.map.delete(value);\n\t\t} else {\n\t\t\tthis.map.set(value, counter);\n\t\t}\n\n\t\treturn true;\n\t}\n\n\thas(value: T): boolean {\n\t\treturn this.map.has(value);\n\t}\n}\n\n/**\n * A map that allows access both by keys and values.\n * **NOTE**: values need to be unique.\n */\nexport class BidirectionalMap<K, V> {\n\n\tprivate readonly _m1 = new Map<K, V>();\n\tprivate readonly _m2 = new Map<V, K>();\n\n\tconstructor(entries?: readonly (readonly [K, V])[]) {\n\t\tif (entries) {\n\t\t\tfor (const [key, value] of entries) {\n\t\t\t\tthis.set(key, value);\n\t\t\t}\n\t\t}\n\t}\n\n\tclear(): void {\n\t\tthis._m1.clear();\n\t\tthis._m2.clear();\n\t}\n\n\tset(key: K, value: V): void {\n\t\tthis._m1.set(key, value);\n\t\tthis._m2.set(value, key);\n\t}\n\n\tget(key: K): V | undefined {\n\t\treturn this._m1.get(key);\n\t}\n\n\tgetKey(value: V): K | undefined {\n\t\treturn this._m2.get(value);\n\t}\n\n\tdelete(key: K): boolean {\n\t\tconst value = this._m1.get(key);\n\t\tif (value === undefined) {\n\t\t\treturn false;\n\t\t}\n\t\tthis._m1.delete(key);\n\t\tthis._m2.delete(value);\n\t\treturn true;\n\t}\n\n\tforEach(callbackfn: (value: V, key: K, map: BidirectionalMap<K, V>) => void, thisArg?: any): void {\n\t\tthis._m1.forEach((value, key) => {\n\t\t\tcallbackfn.call(thisArg, value, key, this);\n\t\t});\n\t}\n\n\tkeys(): IterableIterator<K> {\n\t\treturn this._m1.keys();\n\t}\n\n\tvalues(): IterableIterator<V> {\n\t\treturn this._m1.values();\n\t}\n}\n\nexport class SetMap<K, V> {\n\n\tprivate map = new Map<K, Set<V>>();\n\n\tadd(key: K, value: V): void {\n\t\tlet values = this.map.get(key);\n\n\t\tif (!values) {\n\t\t\tvalues = new Set<V>();\n\t\t\tthis.map.set(key, values);\n\t\t}\n\n\t\tvalues.add(value);\n\t}\n\n\tdelete(key: K, value: V): void {\n\t\tconst values = this.map.get(key);\n\n\t\tif (!values) {\n\t\t\treturn;\n\t\t}\n\n\t\tvalues.delete(value);\n\n\t\tif (values.size === 0) {\n\t\t\tthis.map.delete(key);\n\t\t}\n\t}\n\n\tforEach(key: K, fn: (value: V) => void): void {\n\t\tconst values = this.map.get(key);\n\n\t\tif (!values) {\n\t\t\treturn;\n\t\t}\n\n\t\tvalues.forEach(fn);\n\t}\n\n\tget(key: K): ReadonlySet<V> {\n\t\tconst values = this.map.get(key);\n\t\tif (!values) {\n\t\t\treturn new Set<V>();\n\t\t}\n\t\treturn values;\n\t}\n}\n\nexport function mapsStrictEqualIgnoreOrder(a: Map<unknown, unknown>, b: Map<unknown, unknown>): boolean {\n\tif (a === b) {\n\t\treturn true;\n\t}\n\n\tif (a.size !== b.size) {\n\t\treturn false;\n\t}\n\n\tfor (const [key, value] of a) {\n\t\tif (!b.has(key) || b.get(key) !== value) {\n\t\t\treturn false;\n\t\t}\n\t}\n\n\tfor (const [key] of b) {\n\t\tif (!a.has(key)) {\n\t\t\treturn false;\n\t\t}\n\t}\n\n\treturn true;\n}\n", "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\n/**\n * Given a function, returns a function that is only calling that function once.\n */\nexport function createSingleCallFunction<T extends Function>(this: unknown, fn: T, fnDidRunCallback?: () => void): T {\n\tconst _this = this;\n\tlet didCall = false;\n\tlet result: unknown;\n\n\treturn function () {\n\t\tif (didCall) {\n\t\t\treturn result;\n\t\t}\n\n\t\tdidCall = true;\n\t\tif (fnDidRunCallback) {\n\t\t\ttry {\n\t\t\t\tresult = fn.apply(_this, arguments);\n\t\t\t} finally {\n\t\t\t\tfnDidRunCallback();\n\t\t\t}\n\t\t} else {\n\t\t\tresult = fn.apply(_this, arguments);\n\t\t}\n\n\t\treturn result;\n\t} as unknown as T;\n}\n", "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\nexport namespace Iterable {\n\n\texport function is<T = any>(thing: any): thing is Iterable<T> {\n\t\treturn thing && typeof thing === 'object' && typeof thing[Symbol.iterator] === 'function';\n\t}\n\n\tconst _empty: Iterable<any> = Object.freeze([]);\n\texport function empty<T = any>(): Iterable<T> {\n\t\treturn _empty;\n\t}\n\n\texport function* single<T>(element: T): Iterable<T> {\n\t\tyield element;\n\t}\n\n\texport function wrap<T>(iterableOrElement: Iterable<T> | T): Iterable<T> {\n\t\tif (is(iterableOrElement)) {\n\t\t\treturn iterableOrElement;\n\t\t} else {\n\t\t\treturn single(iterableOrElement);\n\t\t}\n\t}\n\n\texport function from<T>(iterable: Iterable<T> | undefined | null): Iterable<T> {\n\t\treturn iterable || _empty;\n\t}\n\n\texport function* reverse<T>(array: Array<T>): Iterable<T> {\n\t\tfor (let i = array.length - 1; i >= 0; i--) {\n\t\t\tyield array[i];\n\t\t}\n\t}\n\n\texport function isEmpty<T>(iterable: Iterable<T> | undefined | null): boolean {\n\t\treturn !iterable || iterable[Symbol.iterator]().next().done === true;\n\t}\n\n\texport function first<T>(iterable: Iterable<T>): T | undefined {\n\t\treturn iterable[Symbol.iterator]().next().value;\n\t}\n\n\texport function some<T>(iterable: Iterable<T>, predicate: (t: T, i: number) => unknown): boolean {\n\t\tlet i = 0;\n\t\tfor (const element of iterable) {\n\t\t\tif (predicate(element, i++)) {\n\t\t\t\treturn true;\n\t\t\t}\n\t\t}\n\t\treturn false;\n\t}\n\n\texport function find<T, R extends T>(iterable: Iterable<T>, predicate: (t: T) => t is R): R | undefined;\n\texport function find<T>(iterable: Iterable<T>, predicate: (t: T) => boolean): T | undefined;\n\texport function find<T>(iterable: Iterable<T>, predicate: (t: T) => boolean): T | undefined {\n\t\tfor (const element of iterable) {\n\t\t\tif (predicate(element)) {\n\t\t\t\treturn element;\n\t\t\t}\n\t\t}\n\n\t\treturn undefined;\n\t}\n\n\texport function filter<T, R extends T>(iterable: Iterable<T>, predicate: (t: T) => t is R): Iterable<R>;\n\texport function filter<T>(iterable: Iterable<T>, predicate: (t: T) => boolean): Iterable<T>;\n\texport function* filter<T>(iterable: Iterable<T>, predicate: (t: T) => boolean): Iterable<T> {\n\t\tfor (const element of iterable) {\n\t\t\tif (predicate(element)) {\n\t\t\t\tyield element;\n\t\t\t}\n\t\t}\n\t}\n\n\texport function* map<T, R>(iterable: Iterable<T>, fn: (t: T, index: number) => R): Iterable<R> {\n\t\tlet index = 0;\n\t\tfor (const element of iterable) {\n\t\t\tyield fn(element, index++);\n\t\t}\n\t}\n\n\texport function* flatMap<T, R>(iterable: Iterable<T>, fn: (t: T, index: number) => Iterable<R>): Iterable<R> {\n\t\tlet index = 0;\n\t\tfor (const element of iterable) {\n\t\t\tyield* fn(element, index++);\n\t\t}\n\t}\n\n\texport function* concat<T>(...iterables: Iterable<T>[]): Iterable<T> {\n\t\tfor (const iterable of iterables) {\n\t\t\tyield* iterable;\n\t\t}\n\t}\n\n\texport function reduce<T, R>(iterable: Iterable<T>, reducer: (previousValue: R, currentValue: T) => R, initialValue: R): R {\n\t\tlet value = initialValue;\n\t\tfor (const element of iterable) {\n\t\t\tvalue = reducer(value, element);\n\t\t}\n\t\treturn value;\n\t}\n\n\t/**\n\t * Returns an iterable slice of the array, with the same semantics as `array.slice()`.\n\t */\n\texport function* slice<T>(arr: ReadonlyArray<T>, from: number, to = arr.length): Iterable<T> {\n\t\tif (from < 0) {\n\t\t\tfrom += arr.length;\n\t\t}\n\n\t\tif (to < 0) {\n\t\t\tto += arr.length;\n\t\t} else if (to > arr.length) {\n\t\t\tto = arr.length;\n\t\t}\n\n\t\tfor (; from < to; from++) {\n\t\t\tyield arr[from];\n\t\t}\n\t}\n\n\t/**\n\t * Consumes `atMost` elements from iterable and returns the consumed elements,\n\t * and an iterable for the rest of the elements.\n\t */\n\texport function consume<T>(iterable: Iterable<T>, atMost: number = Number.POSITIVE_INFINITY): [T[], Iterable<T>] {\n\t\tconst consumed: T[] = [];\n\n\t\tif (atMost === 0) {\n\t\t\treturn [consumed, iterable];\n\t\t}\n\n\t\tconst iterator = iterable[Symbol.iterator]();\n\n\t\tfor (let i = 0; i < atMost; i++) {\n\t\t\tconst next = iterator.next();\n\n\t\t\tif (next.done) {\n\t\t\t\treturn [consumed, Iterable.empty()];\n\t\t\t}\n\n\t\t\tconsumed.push(next.value);\n\t\t}\n\n\t\treturn [consumed, { [Symbol.iterator]() { return iterator; } }];\n\t}\n\n\texport async function asyncToArray<T>(iterable: AsyncIterable<T>): Promise<T[]> {\n\t\tconst result: T[] = [];\n\t\tfor await (const item of iterable) {\n\t\t\tresult.push(item);\n\t\t}\n\t\treturn Promise.resolve(result);\n\t}\n}\n", "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\nimport { compareBy, numberComparator } from 'vs/base/common/arrays';\nimport { groupBy } from 'vs/base/common/collections';\nimport { SetMap } from './map';\nimport { createSingleCallFunction } from 'vs/base/common/functional';\nimport { Iterable } from 'vs/base/common/iterator';\n\n// #region Disposable Tracking\n\n/**\n * Enables logging of potentially leaked disposables.\n *\n * A disposable is considered leaked if it is not disposed or not registered as the child of\n * another disposable. This tracking is very simple an only works for classes that either\n * extend Disposable or use a DisposableStore. This means there are a lot of false positives.\n */\nconst TRACK_DISPOSABLES = false;\nlet disposableTracker: IDisposableTracker | null = null;\n\nexport interface IDisposableTracker {\n\t/**\n\t * Is called on construction of a disposable.\n\t*/\n\ttrackDisposable(disposable: IDisposable): void;\n\n\t/**\n\t * Is called when a disposable is registered as child of another disposable (e.g. {@link DisposableStore}).\n\t * If parent is `null`, the disposable is removed from its former parent.\n\t*/\n\tsetParent(child: IDisposable, parent: IDisposable | null): void;\n\n\t/**\n\t * Is called after a disposable is disposed.\n\t*/\n\tmarkAsDisposed(disposable: IDisposable): void;\n\n\t/**\n\t * Indicates that the given object is a singleton which does not need to be disposed.\n\t*/\n\tmarkAsSingleton(disposable: IDisposable): void;\n}\n\nexport interface DisposableInfo {\n\tvalue: IDisposable;\n\tsource: string | null;\n\tparent: IDisposable | null;\n\tisSingleton: boolean;\n\tidx: number;\n}\n\nexport class DisposableTracker implements IDisposableTracker {\n\tprivate static idx = 0;\n\n\tprivate readonly livingDisposables = new Map<IDisposable, DisposableInfo>();\n\n\tprivate getDisposableData(d: IDisposable): DisposableInfo {\n\t\tlet val = this.livingDisposables.get(d);\n\t\tif (!val) {\n\t\t\tval = { parent: null, source: null, isSingleton: false, value: d, idx: DisposableTracker.idx++ };\n\t\t\tthis.livingDisposables.set(d, val);\n\t\t}\n\t\treturn val;\n\t}\n\n\ttrackDisposable(d: IDisposable): void {\n\t\tconst data = this.getDisposableData(d);\n\t\tif (!data.source) {\n\t\t\tdata.source =\n\t\t\t\tnew Error().stack!;\n\t\t}\n\t}\n\n\tsetParent(child: IDisposable, parent: IDisposable | null): void {\n\t\tconst data = this.getDisposableData(child);\n\t\tdata.parent = parent;\n\t}\n\n\tmarkAsDisposed(x: IDisposable): void {\n\t\tthis.livingDisposables.delete(x);\n\t}\n\n\tmarkAsSingleton(disposable: IDisposable): void {\n\t\tthis.getDisposableData(disposable).isSingleton = true;\n\t}\n\n\tprivate getRootParent(data: DisposableInfo, cache: Map<DisposableInfo, DisposableInfo>): DisposableInfo {\n\t\tconst cacheValue = cache.get(data);\n\t\tif (cacheValue) {\n\t\t\treturn cacheValue;\n\t\t}\n\n\t\tconst result = data.parent ? this.getRootParent(this.getDisposableData(data.parent), cache) : data;\n\t\tcache.set(data, result);\n\t\treturn result;\n\t}\n\n\tgetTrackedDisposables(): IDisposable[] {\n\t\tconst rootParentCache = new Map<DisposableInfo, DisposableInfo>();\n\n\t\tconst leaking = [...this.livingDisposables.entries()]\n\t\t\t.filter(([, v]) => v.source !== null && !this.getRootParent(v, rootParentCache).isSingleton)\n\t\t\t.flatMap(([k]) => k);\n\n\t\treturn leaking;\n\t}\n\n\tcomputeLeakingDisposables(maxReported = 10, preComputedLeaks?: DisposableInfo[]): { leaks: DisposableInfo[]; details: string } | undefined {\n\t\tlet uncoveredLeakingObjs: DisposableInfo[] | undefined;\n\t\tif (preComputedLeaks) {\n\t\t\tuncoveredLeakingObjs = preComputedLeaks;\n\t\t} else {\n\t\t\tconst rootParentCache = new Map<DisposableInfo, DisposableInfo>();\n\n\t\t\tconst leakingObjects = [...this.livingDisposables.values()]\n\t\t\t\t.filter((info) => info.source !== null && !this.getRootParent(info, rootParentCache).isSingleton);\n\n\t\t\tif (leakingObjects.length === 0) {\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tconst leakingObjsSet = new Set(leakingObjects.map(o => o.value));\n\n\t\t\t// Remove all objects that are a child of other leaking objects. Assumes there are no cycles.\n\t\t\tuncoveredLeakingObjs = leakingObjects.filter(l => {\n\t\t\t\treturn !(l.parent && leakingObjsSet.has(l.parent));\n\t\t\t});\n\n\t\t\tif (uncoveredLeakingObjs.length === 0) {\n\t\t\t\tthrow new Error('There are cyclic diposable chains!');\n\t\t\t}\n\t\t}\n\n\t\tif (!uncoveredLeakingObjs) {\n\t\t\treturn undefined;\n\t\t}\n\n\t\tfunction getStackTracePath(leaking: DisposableInfo): string[] {\n\t\t\tfunction removePrefix(array: string[], linesToRemove: (string | RegExp)[]) {\n\t\t\t\twhile (array.length > 0 && linesToRemove.some(regexp => typeof regexp === 'string' ? regexp === array[0] : array[0].match(regexp))) {\n\t\t\t\t\tarray.shift();\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tconst lines = leaking.source!.split('\\n').map(p => p.trim().replace('at ', '')).filter(l => l !== '');\n\t\t\tremovePrefix(lines, ['Error', /^trackDisposable \\(.*\\)$/, /^DisposableTracker.trackDisposable \\(.*\\)$/]);\n\t\t\treturn lines.reverse();\n\t\t}\n\n\t\tconst stackTraceStarts = new SetMap<string, DisposableInfo>();\n\t\tfor (const leaking of uncoveredLeakingObjs) {\n\t\t\tconst stackTracePath = getStackTracePath(leaking);\n\t\t\tfor (let i = 0; i <= stackTracePath.length; i++) {\n\t\t\t\tstackTraceStarts.add(stackTracePath.slice(0, i).join('\\n'), leaking);\n\t\t\t}\n\t\t}\n\n\t\t// Put earlier leaks first\n\t\tuncoveredLeakingObjs.sort(compareBy(l => l.idx, numberComparator));\n\n\t\tlet message = '';\n\n\t\tlet i = 0;\n\t\tfor (const leaking of uncoveredLeakingObjs.slice(0, maxReported)) {\n\t\t\ti++;\n\t\t\tconst stackTracePath = getStackTracePath(leaking);\n\t\t\tconst stackTraceFormattedLines = [];\n\n\t\t\tfor (let i = 0; i < stackTracePath.length; i++) {\n\t\t\t\tlet line = stackTracePath[i];\n\t\t\t\tconst starts = stackTraceStarts.get(stackTracePath.slice(0, i + 1).join('\\n'));\n\t\t\t\tline = `(shared with ${starts.size}/${uncoveredLeakingObjs.length} leaks) at ${line}`;\n\n\t\t\t\tconst prevStarts = stackTraceStarts.get(stackTracePath.slice(0, i).join('\\n'));\n\t\t\t\tconst continuations = groupBy([...prevStarts].map(d => getStackTracePath(d)[i]), v => v);\n\t\t\t\tdelete continuations[stackTracePath[i]];\n\t\t\t\tfor (const [cont, set] of Object.entries(continuations)) {\n\t\t\t\t\tstackTraceFormattedLines.unshift(`    - stacktraces of ${set.length} other leaks continue with ${cont}`);\n\t\t\t\t}\n\n\t\t\t\tstackTraceFormattedLines.unshift(line);\n\t\t\t}\n\n\t\t\tmessage += `\\n\\n\\n==================== Leaking disposable ${i}/${uncoveredLeakingObjs.length}: ${leaking.value.constructor.name} ====================\\n${stackTraceFormattedLines.join('\\n')}\\n============================================================\\n\\n`;\n\t\t}\n\n\t\tif (uncoveredLeakingObjs.length > maxReported) {\n\t\t\tmessage += `\\n\\n\\n... and ${uncoveredLeakingObjs.length - maxReported} more leaking disposables\\n\\n`;\n\t\t}\n\n\t\treturn { leaks: uncoveredLeakingObjs, details: message };\n\t}\n}\n\nexport function setDisposableTracker(tracker: IDisposableTracker | null): void {\n\tdisposableTracker = tracker;\n}\n\nif (TRACK_DISPOSABLES) {\n\tconst __is_disposable_tracked__ = '__is_disposable_tracked__';\n\tsetDisposableTracker(new class implements IDisposableTracker {\n\t\ttrackDisposable(x: IDisposable): void {\n\t\t\tconst stack = new Error('Potentially leaked disposable').stack!;\n\t\t\tsetTimeout(() => {\n\t\t\t\tif (!(x as any)[__is_disposable_tracked__]) {\n\t\t\t\t\tconsole.log(stack);\n\t\t\t\t}\n\t\t\t}, 3000);\n\t\t}\n\n\t\tsetParent(child: IDisposable, parent: IDisposable | null): void {\n\t\t\tif (child && child !== Disposable.None) {\n\t\t\t\ttry {\n\t\t\t\t\t(child as any)[__is_disposable_tracked__] = true;\n\t\t\t\t} catch {\n\t\t\t\t\t// noop\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\tmarkAsDisposed(disposable: IDisposable): void {\n\t\t\tif (disposable && disposable !== Disposable.None) {\n\t\t\t\ttry {\n\t\t\t\t\t(disposable as any)[__is_disposable_tracked__] = true;\n\t\t\t\t} catch {\n\t\t\t\t\t// noop\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\tmarkAsSingleton(disposable: IDisposable): void { }\n\t});\n}\n\nexport function trackDisposable<T extends IDisposable>(x: T): T {\n\tdisposableTracker?.trackDisposable(x);\n\treturn x;\n}\n\nexport function markAsDisposed(disposable: IDisposable): void {\n\tdisposableTracker?.markAsDisposed(disposable);\n}\n\nfunction setParentOfDisposable(child: IDisposable, parent: IDisposable | null): void {\n\tdisposableTracker?.setParent(child, parent);\n}\n\nfunction setParentOfDisposables(children: IDisposable[], parent: IDisposable | null): void {\n\tif (!disposableTracker) {\n\t\treturn;\n\t}\n\tfor (const child of children) {\n\t\tdisposableTracker.setParent(child, parent);\n\t}\n}\n\n/**\n * Indicates that the given object is a singleton which does not need to be disposed.\n*/\nexport function markAsSingleton<T extends IDisposable>(singleton: T): T {\n\tdisposableTracker?.markAsSingleton(singleton);\n\treturn singleton;\n}\n\n// #endregion\n\n/**\n * An object that performs a cleanup operation when `.dispose()` is called.\n *\n * Some examples of how disposables are used:\n *\n * - An event listener that removes itself when `.dispose()` is called.\n * - A resource such as a file system watcher that cleans up the resource when `.dispose()` is called.\n * - The return value from registering a provider. When `.dispose()` is called, the provider is unregistered.\n */\nexport interface IDisposable {\n\tdispose(): void;\n}\n\n/**\n * Check if `thing` is {@link IDisposable disposable}.\n */\nexport function isDisposable<E extends any>(thing: E): thing is E & IDisposable {\n\treturn typeof thing === 'object' && thing !== null && typeof (<IDisposable><any>thing).dispose === 'function' && (<IDisposable><any>thing).dispose.length === 0;\n}\n\n/**\n * Disposes of the value(s) passed in.\n */\nexport function dispose<T extends IDisposable>(disposable: T): T;\nexport function dispose<T extends IDisposable>(disposable: T | undefined): T | undefined;\nexport function dispose<T extends IDisposable, A extends Iterable<T> = Iterable<T>>(disposables: A): A;\nexport function dispose<T extends IDisposable>(disposables: Array<T>): Array<T>;\nexport function dispose<T extends IDisposable>(disposables: ReadonlyArray<T>): ReadonlyArray<T>;\nexport function dispose<T extends IDisposable>(arg: T | Iterable<T> | undefined): any {\n\tif (Iterable.is(arg)) {\n\t\tconst errors: any[] = [];\n\n\t\tfor (const d of arg) {\n\t\t\tif (d) {\n\t\t\t\ttry {\n\t\t\t\t\td.dispose();\n\t\t\t\t} catch (e) {\n\t\t\t\t\terrors.push(e);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\tif (errors.length === 1) {\n\t\t\tthrow errors[0];\n\t\t} else if (errors.length > 1) {\n\t\t\tthrow new AggregateError(errors, 'Encountered errors while disposing of store');\n\t\t}\n\n\t\treturn Array.isArray(arg) ? [] : arg;\n\t} else if (arg) {\n\t\targ.dispose();\n\t\treturn arg;\n\t}\n}\n\nexport function disposeIfDisposable<T extends IDisposable | object>(disposables: Array<T>): Array<T> {\n\tfor (const d of disposables) {\n\t\tif (isDisposable(d)) {\n\t\t\td.dispose();\n\t\t}\n\t}\n\treturn [];\n}\n\n/**\n * Combine multiple disposable values into a single {@link IDisposable}.\n */\nexport function combinedDisposable(...disposables: IDisposable[]): IDisposable {\n\tconst parent = toDisposable(() => dispose(disposables));\n\tsetParentOfDisposables(disposables, parent);\n\treturn parent;\n}\n\n/**\n * Turn a function that implements dispose into an {@link IDisposable}.\n *\n * @param fn Clean up function, guaranteed to be called only **once**.\n */\nexport function toDisposable(fn: () => void): IDisposable {\n\tconst self = trackDisposable({\n\t\tdispose: createSingleCallFunction(() => {\n\t\t\tmarkAsDisposed(self);\n\t\t\tfn();\n\t\t})\n\t});\n\treturn self;\n}\n\n/**\n * Manages a collection of disposable values.\n *\n * This is the preferred way to manage multiple disposables. A `DisposableStore` is safer to work with than an\n * `IDisposable[]` as it considers edge cases, such as registering the same value multiple times or adding an item to a\n * store that has already been disposed of.\n */\nexport class DisposableStore implements IDisposable {\n\n\tstatic DISABLE_DISPOSED_WARNING = false;\n\n\tprivate readonly _toDispose = new Set<IDisposable>();\n\tprivate _isDisposed = false;\n\n\tconstructor() {\n\t\ttrackDisposable(this);\n\t}\n\n\t/**\n\t * Dispose of all registered disposables and mark this object as disposed.\n\t *\n\t * Any future disposables added to this object will be disposed of on `add`.\n\t */\n\tpublic dispose(): void {\n\t\tif (this._isDisposed) {\n\t\t\treturn;\n\t\t}\n\n\t\tmarkAsDisposed(this);\n\t\tthis._isDisposed = true;\n\t\tthis.clear();\n\t}\n\n\t/**\n\t * @return `true` if this object has been disposed of.\n\t */\n\tpublic get isDisposed(): boolean {\n\t\treturn this._isDisposed;\n\t}\n\n\t/**\n\t * Dispose of all registered disposables but do not mark this object as disposed.\n\t */\n\tpublic clear(): void {\n\t\tif (this._toDispose.size === 0) {\n\t\t\treturn;\n\t\t}\n\n\t\ttry {\n\t\t\tdispose(this._toDispose);\n\t\t} finally {\n\t\t\tthis._toDispose.clear();\n\t\t}\n\t}\n\n\t/**\n\t * Add a new {@link IDisposable disposable} to the collection.\n\t */\n\tpublic add<T extends IDisposable>(o: T): T {\n\t\tif (!o) {\n\t\t\treturn o;\n\t\t}\n\t\tif ((o as unknown as DisposableStore) === this) {\n\t\t\tthrow new Error('Cannot register a disposable on itself!');\n\t\t}\n\n\t\tsetParentOfDisposable(o, this);\n\t\tif (this._isDisposed) {\n\t\t\tif (!DisposableStore.DISABLE_DISPOSED_WARNING) {\n\t\t\t\tconsole.warn(new Error('Trying to add a disposable to a DisposableStore that has already been disposed of. The added object will be leaked!').stack);\n\t\t\t}\n\t\t} else {\n\t\t\tthis._toDispose.add(o);\n\t\t}\n\n\t\treturn o;\n\t}\n\n\t/**\n\t * Deletes a disposable from store and disposes of it. This will not throw or warn and proceed to dispose the\n\t * disposable even when the disposable is not part in the store.\n\t */\n\tpublic delete<T extends IDisposable>(o: T): void {\n\t\tif (!o) {\n\t\t\treturn;\n\t\t}\n\t\tif ((o as unknown as DisposableStore) === this) {\n\t\t\tthrow new Error('Cannot dispose a disposable on itself!');\n\t\t}\n\t\tthis._toDispose.delete(o);\n\t\to.dispose();\n\t}\n\n\t/**\n\t * Deletes the value from the store, but does not dispose it.\n\t */\n\tpublic deleteAndLeak<T extends IDisposable>(o: T): void {\n\t\tif (!o) {\n\t\t\treturn;\n\t\t}\n\t\tif (this._toDispose.has(o)) {\n\t\t\tthis._toDispose.delete(o);\n\t\t\tsetParentOfDisposable(o, null);\n\t\t}\n\t}\n}\n\n/**\n * Abstract base class for a {@link IDisposable disposable} object.\n *\n * Subclasses can {@linkcode _register} disposables that will be automatically cleaned up when this object is disposed of.\n */\nexport abstract class Disposable implements IDisposable {\n\n\t/**\n\t * A disposable that does nothing when it is disposed of.\n\t *\n\t * TODO: This should not be a static property.\n\t */\n\tstatic readonly None = Object.freeze<IDisposable>({ dispose() { } });\n\n\tprotected readonly _store = new DisposableStore();\n\n\tconstructor() {\n\t\ttrackDisposable(this);\n\t\tsetParentOfDisposable(this._store, this);\n\t}\n\n\tpublic dispose(): void {\n\t\tmarkAsDisposed(this);\n\n\t\tthis._store.dispose();\n\t}\n\n\t/**\n\t * Adds `o` to the collection of disposables managed by this object.\n\t */\n\tprotected _register<T extends IDisposable>(o: T): T {\n\t\tif ((o as unknown as Disposable) === this) {\n\t\t\tthrow new Error('Cannot register a disposable on itself!');\n\t\t}\n\t\treturn this._store.add(o);\n\t}\n}\n\n/**\n * Manages the lifecycle of a disposable value that may be changed.\n *\n * This ensures that when the disposable value is changed, the previously held disposable is disposed of. You can\n * also register a `MutableDisposable` on a `Disposable` to ensure it is automatically cleaned up.\n */\nexport class MutableDisposable<T extends IDisposable> implements IDisposable {\n\tprivate _value?: T;\n\tprivate _isDisposed = false;\n\n\tconstructor() {\n\t\ttrackDisposable(this);\n\t}\n\n\tget value(): T | undefined {\n\t\treturn this._isDisposed ? undefined : this._value;\n\t}\n\n\tset value(value: T | undefined) {\n\t\tif (this._isDisposed || value === this._value) {\n\t\t\treturn;\n\t\t}\n\n\t\tthis._value?.dispose();\n\t\tif (value) {\n\t\t\tsetParentOfDisposable(value, this);\n\t\t}\n\t\tthis._value = value;\n\t}\n\n\t/**\n\t * Resets the stored value and disposed of the previously stored value.\n\t */\n\tclear(): void {\n\t\tthis.value = undefined;\n\t}\n\n\tdispose(): void {\n\t\tthis._isDisposed = true;\n\t\tmarkAsDisposed(this);\n\t\tthis._value?.dispose();\n\t\tthis._value = undefined;\n\t}\n\n\t/**\n\t * Clears the value, but does not dispose it.\n\t * The old value is returned.\n\t*/\n\tclearAndLeak(): T | undefined {\n\t\tconst oldValue = this._value;\n\t\tthis._value = undefined;\n\t\tif (oldValue) {\n\t\t\tsetParentOfDisposable(oldValue, null);\n\t\t}\n\t\treturn oldValue;\n\t}\n}\n\n/**\n * Manages the lifecycle of a disposable value that may be changed like {@link MutableDisposable}, but the value must\n * exist and cannot be undefined.\n */\nexport class MandatoryMutableDisposable<T extends IDisposable> implements IDisposable {\n\tprivate readonly _disposable = new MutableDisposable<T>();\n\tprivate _isDisposed = false;\n\n\tconstructor(initialValue: T) {\n\t\tthis._disposable.value = initialValue;\n\t}\n\n\tget value(): T {\n\t\treturn this._disposable.value!;\n\t}\n\n\tset value(value: T) {\n\t\tif (this._isDisposed || value === this._disposable.value) {\n\t\t\treturn;\n\t\t}\n\t\tthis._disposable.value = value;\n\t}\n\n\tdispose() {\n\t\tthis._isDisposed = true;\n\t\tthis._disposable.dispose();\n\t}\n}\n\nexport class RefCountedDisposable {\n\n\tprivate _counter: number = 1;\n\n\tconstructor(\n\t\tprivate readonly _disposable: IDisposable,\n\t) { }\n\n\tacquire() {\n\t\tthis._counter++;\n\t\treturn this;\n\t}\n\n\trelease() {\n\t\tif (--this._counter === 0) {\n\t\t\tthis._disposable.dispose();\n\t\t}\n\t\treturn this;\n\t}\n}\n\n/**\n * A safe disposable can be `unset` so that a leaked reference (listener)\n * can be cut-off.\n */\nexport class SafeDisposable implements IDisposable {\n\n\tdispose: () => void = () => { };\n\tunset: () => void = () => { };\n\tisset: () => boolean = () => false;\n\n\tconstructor() {\n\t\ttrackDisposable(this);\n\t}\n\n\tset(fn: Function) {\n\t\tlet callback: Function | undefined = fn;\n\t\tthis.unset = () => callback = undefined;\n\t\tthis.isset = () => callback !== undefined;\n\t\tthis.dispose = () => {\n\t\t\tif (callback) {\n\t\t\t\tcallback();\n\t\t\t\tcallback = undefined;\n\t\t\t\tmarkAsDisposed(this);\n\t\t\t}\n\t\t};\n\t\treturn this;\n\t}\n}\n\nexport interface IReference<T> extends IDisposable {\n\treadonly object: T;\n}\n\nexport abstract class ReferenceCollection<T> {\n\n\tprivate readonly references: Map<string, { readonly object: T; counter: number }> = new Map();\n\n\tacquire(key: string, ...args: any[]): IReference<T> {\n\t\tlet reference = this.references.get(key);\n\n\t\tif (!reference) {\n\t\t\treference = { counter: 0, object: this.createReferencedObject(key, ...args) };\n\t\t\tthis.references.set(key, reference);\n\t\t}\n\n\t\tconst { object } = reference;\n\t\tconst dispose = createSingleCallFunction(() => {\n\t\t\tif (--reference.counter === 0) {\n\t\t\t\tthis.destroyReferencedObject(key, reference.object);\n\t\t\t\tthis.references.delete(key);\n\t\t\t}\n\t\t});\n\n\t\treference.counter++;\n\n\t\treturn { object, dispose };\n\t}\n\n\tprotected abstract createReferencedObject(key: string, ...args: any[]): T;\n\tprotected abstract destroyReferencedObject(key: string, object: T): void;\n}\n\n/**\n * Unwraps a reference collection of promised values. Makes sure\n * references are disposed whenever promises get rejected.\n */\nexport class AsyncReferenceCollection<T> {\n\n\tconstructor(private referenceCollection: ReferenceCollection<Promise<T>>) { }\n\n\tasync acquire(key: string, ...args: any[]): Promise<IReference<T>> {\n\t\tconst ref = this.referenceCollection.acquire(key, ...args);\n\n\t\ttry {\n\t\t\tconst object = await ref.object;\n\n\t\t\treturn {\n\t\t\t\tobject,\n\t\t\t\tdispose: () => ref.dispose()\n\t\t\t};\n\t\t} catch (error) {\n\t\t\tref.dispose();\n\t\t\tthrow error;\n\t\t}\n\t}\n}\n\nexport class ImmortalReference<T> implements IReference<T> {\n\tconstructor(public object: T) { }\n\tdispose(): void { /* noop */ }\n}\n\nexport function disposeOnReturn(fn: (store: DisposableStore) => void): void {\n\tconst store = new DisposableStore();\n\ttry {\n\t\tfn(store);\n\t} finally {\n\t\tstore.dispose();\n\t}\n}\n\n/**\n * A map the manages the lifecycle of the values that it stores.\n */\nexport class DisposableMap<K, V extends IDisposable = IDisposable> implements IDisposable {\n\n\tprivate readonly _store = new Map<K, V>();\n\tprivate _isDisposed = false;\n\n\tconstructor() {\n\t\ttrackDisposable(this);\n\t}\n\n\t/**\n\t * Disposes of all stored values and mark this object as disposed.\n\t *\n\t * Trying to use this object after it has been disposed of is an error.\n\t */\n\tdispose(): void {\n\t\tmarkAsDisposed(this);\n\t\tthis._isDisposed = true;\n\t\tthis.clearAndDisposeAll();\n\t}\n\n\t/**\n\t * Disposes of all stored values and clear the map, but DO NOT mark this object as disposed.\n\t */\n\tclearAndDisposeAll(): void {\n\t\tif (!this._store.size) {\n\t\t\treturn;\n\t\t}\n\n\t\ttry {\n\t\t\tdispose(this._store.values());\n\t\t} finally {\n\t\t\tthis._store.clear();\n\t\t}\n\t}\n\n\thas(key: K): boolean {\n\t\treturn this._store.has(key);\n\t}\n\n\tget size(): number {\n\t\treturn this._store.size;\n\t}\n\n\tget(key: K): V | undefined {\n\t\treturn this._store.get(key);\n\t}\n\n\tset(key: K, value: V, skipDisposeOnOverwrite = false): void {\n\t\tif (this._isDisposed) {\n\t\t\tconsole.warn(new Error('Trying to add a disposable to a DisposableMap that has already been disposed of. The added object will be leaked!').stack);\n\t\t}\n\n\t\tif (!skipDisposeOnOverwrite) {\n\t\t\tthis._store.get(key)?.dispose();\n\t\t}\n\n\t\tthis._store.set(key, value);\n\t}\n\n\t/**\n\t * Delete the value stored for `key` from this map and also dispose of it.\n\t */\n\tdeleteAndDispose(key: K): void {\n\t\tthis._store.get(key)?.dispose();\n\t\tthis._store.delete(key);\n\t}\n\n\t/**\n\t * Delete the value stored for `key` from this map but return it. The caller is\n\t * responsible for disposing of the value.\n\t */\n\tdeleteAndLeak(key: K): V | undefined {\n\t\tconst value = this._store.get(key);\n\t\tthis._store.delete(key);\n\t\treturn value;\n\t}\n\n\tkeys(): IterableIterator<K> {\n\t\treturn this._store.keys();\n\t}\n\n\tvalues(): IterableIterator<V> {\n\t\treturn this._store.values();\n\t}\n\n\t[Symbol.iterator](): IterableIterator<[K, V]> {\n\t\treturn this._store[Symbol.iterator]();\n\t}\n}\n", "/**\n * Copyright (c) 2020 The xterm.js authors. All rights reserved.\n * @license MIT\n */\n\nimport { IDisposable } from '@xterm/xterm';\nimport { ImageRenderer } from './ImageRenderer';\nimport { ITerminalExt, IExtendedAttrsImage, IImageAddonOptions, IImageSpec, IBufferLineExt, BgFlags, Cell, Content, ICellSize, ExtFlags, Attributes, UnderlineStyle } from './Types';\n\n\n// fallback default cell size\nexport const CELL_SIZE_DEFAULT: ICellSize = {\n  width: 7,\n  height: 14\n};\n\n/**\n * Extend extended attribute to also hold image tile information.\n *\n * Object definition is copied from base repo to fully mimick its behavior.\n * Image data is added as additional public properties `imageId` and `tileId`.\n */\nclass ExtendedAttrsImage implements IExtendedAttrsImage {\n  private _ext: number = 0;\n  public get ext(): number {\n    if (this._urlId) {\n      return (\n        (this._ext & ~ExtFlags.UNDERLINE_STYLE) |\n        (this.underlineStyle << 26)\n      );\n    }\n    return this._ext;\n  }\n  public set ext(value: number) { this._ext = value; }\n\n  public get underlineStyle(): UnderlineStyle {\n    // Always return the URL style if it has one\n    if (this._urlId) {\n      return UnderlineStyle.DASHED;\n    }\n    return (this._ext & ExtFlags.UNDERLINE_STYLE) >> 26;\n  }\n  public set underlineStyle(value: UnderlineStyle) {\n    this._ext &= ~ExtFlags.UNDERLINE_STYLE;\n    this._ext |= (value << 26) & ExtFlags.UNDERLINE_STYLE;\n  }\n\n  public get underlineColor(): number {\n    return this._ext & (Attributes.CM_MASK | Attributes.RGB_MASK);\n  }\n  public set underlineColor(value: number) {\n    this._ext &= ~(Attributes.CM_MASK | Attributes.RGB_MASK);\n    this._ext |= value & (Attributes.CM_MASK | Attributes.RGB_MASK);\n  }\n\n  public get underlineVariantOffset(): number {\n    const val = (this._ext & ExtFlags.VARIANT_OFFSET) >> 29;\n    if (val < 0) {\n      return val ^ 0xFFFFFFF8;\n    }\n    return val;\n  }\n  public set underlineVariantOffset(value: number) {\n    this._ext &= ~ExtFlags.VARIANT_OFFSET;\n    this._ext |= (value << 29) & ExtFlags.VARIANT_OFFSET;\n  }\n\n  private _urlId: number = 0;\n  public get urlId(): number {\n    return this._urlId;\n  }\n  public set urlId(value: number) {\n    this._urlId = value;\n  }\n\n  constructor(\n    ext: number = 0,\n    urlId: number = 0,\n    public imageId = -1,\n    public tileId = -1\n  ) {\n    this._ext = ext;\n    this._urlId = urlId;\n  }\n\n  public clone(): IExtendedAttrsImage {\n    /**\n     * Technically we dont need a clone variant of ExtendedAttrsImage,\n     * as we never clone a cell holding image data.\n     * Note: Clone is only meant to be used by the InputHandler for\n     * sticky attributes, which is never the case for image data.\n     * We still provide a proper clone method to reflect the full ext attr\n     * state in case there are future use cases for clone.\n     */\n    return new ExtendedAttrsImage(this._ext, this._urlId, this.imageId, this.tileId);\n  }\n\n  public isEmpty(): boolean {\n    return this.underlineStyle === UnderlineStyle.NONE && this._urlId === 0 && this.imageId === -1;\n  }\n}\nconst EMPTY_ATTRS = new ExtendedAttrsImage();\n\n\n/**\n * ImageStorage - extension of CoreTerminal:\n * - hold image data\n * - write/read image data to/from buffer\n *\n * TODO: image composition for overwrites\n */\nexport class ImageStorage implements IDisposable {\n  // storage\n  private _images: Map<number, IImageSpec> = new Map();\n  // last used id\n  private _lastId = 0;\n  // last evicted id\n  private _lowestId = 0;\n  // whether a full clear happened before\n  private _fullyCleared = false;\n  // whether render should do a full clear\n  private _needsFullClear = false;\n  // hard limit of stored pixels (fallback limit of 10 MB)\n  private _pixelLimit: number = 2500000;\n\n  private _viewportMetrics: { cols: number, rows: number };\n\n  constructor(\n    private _terminal: ITerminalExt,\n    private _renderer: ImageRenderer,\n    private _opts: IImageAddonOptions\n  ) {\n    try {\n      this.setLimit(this._opts.storageLimit);\n    } catch (e: any) {\n      console.error(e.message);\n      console.warn(`storageLimit is set to ${this.getLimit()} MB`);\n    }\n    this._viewportMetrics = {\n      cols: this._terminal.cols,\n      rows: this._terminal.rows\n    };\n  }\n\n  public dispose(): void {\n    this.reset();\n  }\n\n  public reset(): void {\n    for (const spec of this._images.values()) {\n      spec.marker?.dispose();\n    }\n    // NOTE: marker.dispose above already calls ImageBitmap.close\n    // therefore we can just wipe the map here\n    this._images.clear();\n    this._renderer.clearAll();\n  }\n\n  public getLimit(): number {\n    return this._pixelLimit * 4 / 1000000;\n  }\n\n  public setLimit(value: number): void {\n    if (value < 0.5 || value > 1000) {\n      throw RangeError('invalid storageLimit, should be at least 0.5 MB and not exceed 1G');\n    }\n    this._pixelLimit = (value / 4 * 1000000) >>> 0;\n    this._evictOldest(0);\n  }\n\n  public getUsage(): number {\n    return this._getStoredPixels() * 4 / 1000000;\n  }\n\n  private _getStoredPixels(): number {\n    let storedPixels = 0;\n    for (const spec of this._images.values()) {\n      if (spec.orig) {\n        storedPixels += spec.orig.width * spec.orig.height;\n        if (spec.actual && spec.actual !== spec.orig) {\n          storedPixels += spec.actual.width * spec.actual.height;\n        }\n      }\n    }\n    return storedPixels;\n  }\n\n  private _delImg(id: number): void {\n    const spec = this._images.get(id);\n    this._images.delete(id);\n    // FIXME: really ugly workaround to get bitmaps deallocated :(\n    if (spec && window.ImageBitmap && spec.orig instanceof ImageBitmap) {\n      spec.orig.close();\n    }\n  }\n\n  /**\n   * Wipe canvas and images on alternate buffer.\n   */\n  public wipeAlternate(): void {\n    // remove all alternate tagged images\n    const zero = [];\n    for (const [id, spec] of this._images.entries()) {\n      if (spec.bufferType === 'alternate') {\n        spec.marker?.dispose();\n        zero.push(id);\n      }\n    }\n    for (const id of zero) {\n      this._delImg(id);\n    }\n    // mark canvas to be wiped on next render\n    this._needsFullClear = true;\n    this._fullyCleared = false;\n  }\n\n  /**\n   * Only advance text cursor.\n   * This is an edge case from empty sixels carrying only a height but no pixels.\n   * Partially fixes https://github.com/jerch/xterm-addon-image/issues/37.\n   */\n  public advanceCursor(height: number): void {\n    if (this._opts.sixelScrolling) {\n      let cellSize = this._renderer.cellSize;\n      if (cellSize.width === -1 || cellSize.height === -1) {\n        cellSize = CELL_SIZE_DEFAULT;\n      }\n      const rows = Math.ceil(height / cellSize.height);\n      for (let i = 1; i < rows; ++i) {\n        this._terminal._core._inputHandler.lineFeed();\n      }\n    }\n  }\n\n  /**\n   * Method to add an image to the storage.\n   */\n  public addImage(img: HTMLCanvasElement | ImageBitmap): void {\n    // never allow storage to exceed memory limit\n    this._evictOldest(img.width * img.height);\n\n    // calc rows x cols needed to display the image\n    let cellSize = this._renderer.cellSize;\n    if (cellSize.width === -1 || cellSize.height === -1) {\n      cellSize = CELL_SIZE_DEFAULT;\n    }\n    const cols = Math.ceil(img.width / cellSize.width);\n    const rows = Math.ceil(img.height / cellSize.height);\n\n    const imageId = ++this._lastId;\n\n    const buffer = this._terminal._core.buffer;\n    const termCols = this._terminal.cols;\n    const termRows = this._terminal.rows;\n    const originX = buffer.x;\n    const originY = buffer.y;\n    let offset = originX;\n    let tileCount = 0;\n\n    if (!this._opts.sixelScrolling) {\n      buffer.x = 0;\n      buffer.y = 0;\n      offset = 0;\n    }\n\n    this._terminal._core._inputHandler._dirtyRowTracker.markDirty(buffer.y);\n    for (let row = 0; row < rows; ++row) {\n      const line = buffer.lines.get(buffer.y + buffer.ybase);\n      for (let col = 0; col < cols; ++col) {\n        if (offset + col >= termCols) break;\n        this._writeToCell(line as IBufferLineExt, offset + col, imageId, row * cols + col);\n        tileCount++;\n      }\n      if (this._opts.sixelScrolling) {\n        if (row < rows - 1) this._terminal._core._inputHandler.lineFeed();\n      } else {\n        if (++buffer.y >= termRows) break;\n      }\n      buffer.x = offset;\n    }\n    this._terminal._core._inputHandler._dirtyRowTracker.markDirty(buffer.y);\n\n    // cursor positioning modes\n    if (this._opts.sixelScrolling) {\n      buffer.x = offset;\n    } else {\n      buffer.x = originX;\n      buffer.y = originY;\n    }\n\n    // deleted images with zero tile count\n    const zero = [];\n    for (const [id, spec] of this._images.entries()) {\n      if (spec.tileCount < 1) {\n        spec.marker?.dispose();\n        zero.push(id);\n      }\n    }\n    for (const id of zero) {\n      this._delImg(id);\n    }\n\n    // eviction marker:\n    // delete the image when the marker gets disposed\n    const endMarker = this._terminal.registerMarker(0);\n    endMarker?.onDispose(() => {\n      const spec = this._images.get(imageId);\n      if (spec) {\n        this._delImg(imageId);\n      }\n    });\n\n    // since markers do not work on alternate for some reason,\n    // we evict images here manually\n    if (this._terminal.buffer.active.type === 'alternate') {\n      this._evictOnAlternate();\n    }\n\n    // create storage entry\n    const imgSpec: IImageSpec = {\n      orig: img,\n      origCellSize: cellSize,\n      actual: img,\n      actualCellSize: { ...cellSize },  // clone needed, since later modified\n      marker: endMarker || undefined,\n      tileCount,\n      bufferType: this._terminal.buffer.active.type\n    };\n\n    // finally add the image\n    this._images.set(imageId, imgSpec);\n  }\n\n\n  /**\n   * Render method. Collects buffer information and triggers\n   * canvas updates.\n   */\n  // TODO: Should we move this to the ImageRenderer?\n  public render(range: { start: number, end: number }): void {\n    // setup image canvas in case we have none yet, but have images in store\n    if (!this._renderer.canvas && this._images.size) {\n      this._renderer.insertLayerToDom();\n      // safety measure - in case we cannot spawn a canvas at all, just exit\n      if (!this._renderer.canvas) {\n        return;\n      }\n    }\n    // rescale if needed\n    this._renderer.rescaleCanvas();\n    // exit early if we dont have any images to test for\n    if (!this._images.size) {\n      if (!this._fullyCleared) {\n        this._renderer.clearAll();\n        this._fullyCleared = true;\n        this._needsFullClear = false;\n      }\n      if (this._renderer.canvas) {\n        this._renderer.removeLayerFromDom();\n      }\n      return;\n    }\n\n    // buffer switches force a full clear\n    if (this._needsFullClear) {\n      this._renderer.clearAll();\n      this._fullyCleared = true;\n      this._needsFullClear = false;\n    }\n\n    const { start, end } = range;\n    const buffer = this._terminal._core.buffer;\n    const cols = this._terminal._core.cols;\n\n    // clear drawing area\n    this._renderer.clearLines(start, end);\n\n    // walk all cells in viewport and draw tiles found\n    for (let row = start; row <= end; ++row) {\n      const line = buffer.lines.get(row + buffer.ydisp) as IBufferLineExt;\n      if (!line) return;\n      for (let col = 0; col < cols; ++col) {\n        if (line.getBg(col) & BgFlags.HAS_EXTENDED) {\n          let e: IExtendedAttrsImage = line._extendedAttrs[col] || EMPTY_ATTRS;\n          const imageId = e.imageId;\n          if (imageId === undefined || imageId === -1) {\n            continue;\n          }\n          const imgSpec = this._images.get(imageId);\n          if (e.tileId !== -1) {\n            const startTile = e.tileId;\n            const startCol = col;\n            let count = 1;\n            /**\n             * merge tiles to the right into a single draw call, if:\n             * - not at end of line\n             * - cell has same image id\n             * - cell has consecutive tile id\n             */\n            while (\n              ++col < cols\n              && (line.getBg(col) & BgFlags.HAS_EXTENDED)\n              && (e = line._extendedAttrs[col] || EMPTY_ATTRS)\n              && (e.imageId === imageId)\n              && (e.tileId === startTile + count)\n            ) {\n              count++;\n            }\n            col--;\n            if (imgSpec) {\n              if (imgSpec.actual) {\n                this._renderer.draw(imgSpec, startTile, startCol, row, count);\n              }\n            } else if (this._opts.showPlaceholder) {\n              this._renderer.drawPlaceholder(startCol, row, count);\n            }\n            this._fullyCleared = false;\n          }\n        }\n      }\n    }\n  }\n\n  public viewportResize(metrics: { cols: number, rows: number }): void {\n    // exit early if we have nothing in storage\n    if (!this._images.size) {\n      this._viewportMetrics = metrics;\n      return;\n    }\n\n    // handle only viewport width enlargements, exit all other cases\n    // TODO: needs patch for tile counter\n    if (this._viewportMetrics.cols >= metrics.cols) {\n      this._viewportMetrics = metrics;\n      return;\n    }\n\n    // walk scrollbuffer at old col width to find all possible expansion matches\n    const buffer = this._terminal._core.buffer;\n    const rows = buffer.lines.length;\n    const oldCol = this._viewportMetrics.cols - 1;\n    for (let row = 0; row < rows; ++row) {\n      const line = buffer.lines.get(row) as IBufferLineExt;\n      if (line.getBg(oldCol) & BgFlags.HAS_EXTENDED) {\n        const e: IExtendedAttrsImage = line._extendedAttrs[oldCol] || EMPTY_ATTRS;\n        const imageId = e.imageId;\n        if (imageId === undefined || imageId === -1) {\n          continue;\n        }\n        const imgSpec = this._images.get(imageId);\n        if (!imgSpec) {\n          continue;\n        }\n        // found an image tile at oldCol, check if it qualifies for right exapansion\n        const tilesPerRow = Math.ceil((imgSpec.actual?.width || 0) / imgSpec.actualCellSize.width);\n        if ((e.tileId % tilesPerRow) + 1 >= tilesPerRow) {\n          continue;\n        }\n        // expand only if right side is empty (nothing got wrapped from below)\n        let hasData = false;\n        for (let rightCol = oldCol + 1; rightCol > metrics.cols; ++rightCol) {\n          if (line._data[rightCol * Cell.SIZE + Cell.CONTENT] & Content.HAS_CONTENT_MASK) {\n            hasData = true;\n            break;\n          }\n        }\n        if (hasData) {\n          continue;\n        }\n        // do right expansion on terminal buffer\n        const end = Math.min(metrics.cols, tilesPerRow - (e.tileId % tilesPerRow) + oldCol);\n        let lastTile = e.tileId;\n        for (let expandCol = oldCol + 1; expandCol < end; ++expandCol) {\n          this._writeToCell(line as IBufferLineExt, expandCol, imageId, ++lastTile);\n          imgSpec.tileCount++;\n        }\n      }\n    }\n    // store new viewport metrics\n    this._viewportMetrics = metrics;\n  }\n\n  /**\n   * Retrieve original canvas at buffer position.\n   */\n  public getImageAtBufferCell(x: number, y: number): HTMLCanvasElement | undefined {\n    const buffer = this._terminal._core.buffer;\n    const line = buffer.lines.get(y) as IBufferLineExt;\n    if (line && line.getBg(x) & BgFlags.HAS_EXTENDED) {\n      const e: IExtendedAttrsImage = line._extendedAttrs[x] || EMPTY_ATTRS;\n      if (e.imageId && e.imageId !== -1) {\n        const orig = this._images.get(e.imageId)?.orig;\n        if (window.ImageBitmap && orig instanceof ImageBitmap) {\n          const canvas = ImageRenderer.createCanvas(window.document, orig.width, orig.height);\n          canvas.getContext('2d')?.drawImage(orig, 0, 0, orig.width, orig.height);\n          return canvas;\n        }\n        return orig as HTMLCanvasElement;\n      }\n    }\n  }\n\n  /**\n   * Extract active single tile at buffer position.\n   */\n  public extractTileAtBufferCell(x: number, y: number): HTMLCanvasElement | undefined {\n    const buffer = this._terminal._core.buffer;\n    const line = buffer.lines.get(y) as IBufferLineExt;\n    if (line && line.getBg(x) & BgFlags.HAS_EXTENDED) {\n      const e: IExtendedAttrsImage = line._extendedAttrs[x] || EMPTY_ATTRS;\n      if (e.imageId && e.imageId !== -1 && e.tileId !== -1) {\n        const spec = this._images.get(e.imageId);\n        if (spec) {\n          return this._renderer.extractTile(spec, e.tileId);\n        }\n      }\n    }\n  }\n\n  // TODO: Do we need some blob offloading tricks here to avoid early eviction?\n  // also see https://stackoverflow.com/questions/28307789/is-there-any-limitation-on-javascript-max-blob-size\n  private _evictOldest(room: number): number {\n    const used = this._getStoredPixels();\n    let current = used;\n    while (this._pixelLimit < current + room && this._images.size) {\n      const spec = this._images.get(++this._lowestId);\n      if (spec && spec.orig) {\n        current -= spec.orig.width * spec.orig.height;\n        if (spec.actual && spec.orig !== spec.actual) {\n          current -= spec.actual.width * spec.actual.height;\n        }\n        spec.marker?.dispose();\n        this._delImg(this._lowestId);\n      }\n    }\n    return used - current;\n  }\n\n  private _writeToCell(line: IBufferLineExt, x: number, imageId: number, tileId: number): void {\n    if (line._data[x * Cell.SIZE + Cell.BG] & BgFlags.HAS_EXTENDED) {\n      const old = line._extendedAttrs[x];\n      if (old) {\n        if (old.imageId !== undefined) {\n          // found an old ExtendedAttrsImage, since we know that\n          // they are always isolated instances (single cell usage),\n          // we can re-use it and just update their id entries\n          const oldSpec = this._images.get(old.imageId);\n          if (oldSpec) {\n            // early eviction for in-viewport overwrites\n            oldSpec.tileCount--;\n          }\n          old.imageId = imageId;\n          old.tileId = tileId;\n          return;\n        }\n        // found a plain ExtendedAttrs instance, clone it to new entry\n        line._extendedAttrs[x] = new ExtendedAttrsImage(old.ext, old.urlId, imageId, tileId);\n        return;\n      }\n    }\n    // fall-through: always create new ExtendedAttrsImage entry\n    line._data[x * Cell.SIZE + Cell.BG] |= BgFlags.HAS_EXTENDED;\n    line._extendedAttrs[x] = new ExtendedAttrsImage(0, 0, imageId, tileId);\n  }\n\n  private _evictOnAlternate(): void {\n    // nullify tile count of all images on alternate buffer\n    for (const spec of this._images.values()) {\n      if (spec.bufferType === 'alternate') {\n        spec.tileCount = 0;\n      }\n    }\n    // re-count tiles on whole buffer\n    const buffer = this._terminal._core.buffer;\n    for (let y = 0; y < this._terminal.rows; ++y) {\n      const line = buffer.lines.get(y) as IBufferLineExt;\n      if (!line) {\n        continue;\n      }\n      for (let x = 0; x < this._terminal.cols; ++x) {\n        if (line._data[x * Cell.SIZE + Cell.BG] & BgFlags.HAS_EXTENDED) {\n          const imgId = line._extendedAttrs[x]?.imageId;\n          if (imgId) {\n            const spec = this._images.get(imgId);\n            if (spec) {\n              spec.tileCount++;\n            }\n          }\n        }\n      }\n    }\n    // deleted images with zero tile count\n    const zero = [];\n    for (const [id, spec] of this._images.entries()) {\n      if (spec.bufferType === 'alternate' && !spec.tileCount) {\n        spec.marker?.dispose();\n        zero.push(id);\n      }\n    }\n    for (const id of zero) {\n      this._delImg(id);\n    }\n  }\n}\n", "/**\n * Copyright (c) 2023 The xterm.js authors. All rights reserved.\n * @license MIT\n */\nimport { IImageAddonOptions, IOscHandler, IResetHandler, ITerminalExt } from './Types';\nimport { ImageRenderer } from './ImageRenderer';\nimport { ImageStorage, CELL_SIZE_DEFAULT } from './ImageStorage';\nimport Base64Decoder from 'xterm-wasm-parts/lib/base64/Base64Decoder.wasm';\nimport { HeaderParser, IHeaderFields, HeaderState } from './IIPHeaderParser';\nimport { imageType, UNSUPPORTED_TYPE } from './IIPMetrics';\n\n\n// eslint-disable-next-line\ndeclare const Buffer: any;\n\n// limit hold memory in base64 decoder\nconst KEEP_DATA = 4194304;\n\n// default IIP header values\nconst DEFAULT_HEADER: IHeaderFields = {\n  name: 'Unnamed file',\n  size: 0,\n  width: 'auto',\n  height: 'auto',\n  preserveAspectRatio: 1,\n  inline: 0\n};\n\n\nexport class IIPHandler implements <PERSON><PERSON>cHandler, IResetHandler {\n  private _aborted = false;\n  private _hp = new HeaderParser();\n  private _header: IHeaderFields = DEFAULT_HEADER;\n  private _dec = new Base64Decoder(KEEP_DATA);\n  private _metrics = UNSUPPORTED_TYPE;\n\n  constructor(\n    private readonly _opts: IImageAddonOptions,\n    private readonly _renderer: ImageRenderer,\n    private readonly _storage: ImageStorage,\n    private readonly _coreTerminal: ITerminalExt\n  ) {}\n\n  public reset(): void {}\n\n  public start(): void {\n    this._aborted = false;\n    this._header = DEFAULT_HEADER;\n    this._metrics  = UNSUPPORTED_TYPE;\n    this._hp.reset();\n  }\n\n  public put(data: Uint32Array, start: number, end: number): void {\n    if (this._aborted) return;\n\n    if (this._hp.state === HeaderState.END) {\n      if (this._dec.put(data, start, end)) {\n        this._dec.release();\n        this._aborted = true;\n      }\n    } else {\n      const dataPos = this._hp.parse(data, start, end);\n      if (dataPos === -1) {\n        this._aborted = true;\n        return;\n      }\n      if (dataPos > 0) {\n        this._header = Object.assign({}, DEFAULT_HEADER, this._hp.fields);\n        if (!this._header.inline || !this._header.size || this._header.size > this._opts.iipSizeLimit) {\n          this._aborted = true;\n          return;\n        }\n        this._dec.init(this._header.size);\n        if (this._dec.put(data, dataPos, end)) {\n          this._dec.release();\n          this._aborted = true;\n        }\n      }\n    }\n  }\n\n  public end(success: boolean): boolean | Promise<boolean> {\n    if (this._aborted) return true;\n\n    let w = 0;\n    let h = 0;\n\n    // early exit condition chain\n    let cond: number | boolean = true;\n    if (cond = success) {\n      if (cond = !this._dec.end()) {\n        this._metrics = imageType(this._dec.data8);\n        if (cond = this._metrics.mime !== 'unsupported') {\n          w = this._metrics.width;\n          h = this._metrics.height;\n          if (cond = w && h && w * h < this._opts.pixelLimit) {\n            [w, h] = this._resize(w, h).map(Math.floor);\n            cond = w && h && w * h < this._opts.pixelLimit;\n          }\n        }\n      }\n    }\n    if (!cond) {\n      this._dec.release();\n      return true;\n    }\n\n    const blob = new Blob([this._dec.data8], { type: this._metrics.mime });\n    this._dec.release();\n\n    if (!window.createImageBitmap) {\n      const url = URL.createObjectURL(blob);\n      const img = new Image();\n      return new Promise<boolean>(r => {\n        img.addEventListener('load', () => {\n          URL.revokeObjectURL(url);\n          const canvas = ImageRenderer.createCanvas(window.document, w, h);\n          canvas.getContext('2d')?.drawImage(img, 0, 0, w, h);\n          this._storage.addImage(canvas);\n          r(true);\n        });\n        img.src = url;\n        // sanity measure to avoid terminal blocking from dangling promise\n        // happens from corrupt data (onload never gets fired)\n        setTimeout(() => r(true), 1000);\n      });\n    }\n    return createImageBitmap(blob, { resizeWidth: w, resizeHeight: h })\n      .then(bm => {\n        this._storage.addImage(bm);\n        return true;\n      });\n  }\n\n  private _resize(w: number, h: number): [number, number] {\n    const cw = this._renderer.dimensions?.css.cell.width || CELL_SIZE_DEFAULT.width;\n    const ch = this._renderer.dimensions?.css.cell.height || CELL_SIZE_DEFAULT.height;\n    const width = this._renderer.dimensions?.css.canvas.width || cw * this._coreTerminal.cols;\n    const height = this._renderer.dimensions?.css.canvas.height || ch * this._coreTerminal.rows;\n\n    const rw = this._dim(this._header.width!, width, cw);\n    const rh = this._dim(this._header.height!, height, ch);\n    if (!rw && !rh) {\n      const wf = width / w;         // TODO: should this respect initial cursor offset?\n      const hf = (height - ch) / h; // TODO: fix offset issues from float cell height\n      const f = Math.min(wf, hf);\n      return f < 1 ? [w * f, h * f] : [w, h];\n    }\n    return !rw\n      ? [w * rh / h, rh]\n      : this._header.preserveAspectRatio || !rw || !rh\n        ? [rw, h * rw / w] : [rw, rh];\n  }\n\n  private _dim(s: string, total: number, cdim: number): number {\n    if (s === 'auto') return 0;\n    if (s.endsWith('%')) return parseInt(s.slice(0, -1)) * total / 100;\n    if (s.endsWith('px')) return parseInt(s.slice(0, -2));\n    return parseInt(s) * cdim;\n  }\n}\n", "/**\n * Copyright (c) 2023 The xterm.js authors. All rights reserved.\n * @license MIT\n */\n\n// eslint-disable-next-line\ndeclare const Buffer: any;\n\n\nexport interface IHeaderFields {\n  // base-64 encoded filename. Defaults to \"Unnamed file\".\n  name: string;\n  // File size in bytes. The file transfer will be canceled if this size is exceeded.\n  size: number;\n  /**\n   * Optional width and height to render:\n   * - N: N character cells.\n   * - Npx: N pixels.\n   * - N%: N percent of the session's width or height.\n   * - auto: The image's inherent size will be used to determine an appropriate dimension.\n   */\n  width?: string;\n  height?: string;\n  // Optional, defaults to 1 respecting aspect ratio (width takes precedence).\n  preserveAspectRatio?: number;\n  // Optional, defaults to 0. If set to 1, the file will be displayed inline, else downloaded\n  // (download not supported).\n  inline?: number;\n}\n\nexport const enum HeaderState {\n  START = 0,\n  ABORT = 1,\n  KEY = 2,\n  VALUE = 3,\n  END = 4\n}\n\n// field value decoders\n\n// ASCII bytes to string\nfunction toStr(data: Uint32Array): string {\n  let s = '';\n  for (let i = 0; i < data.length; ++i) {\n    s += String.fromCharCode(data[i]);\n  }\n  return s;\n}\n\n// digits to integer\nfunction toInt(data: Uint32Array): number {\n  let v = 0;\n  for (let i = 0; i < data.length; ++i) {\n    if (data[i] < 48 || data[i] > 57) {\n      throw new Error('illegal char');\n    }\n    v = v * 10 + data[i] - 48;\n  }\n  return v;\n}\n\n// check for correct size entry\nfunction toSize(data: Uint32Array): string {\n  const v = toStr(data);\n  if (!v.match(/^((auto)|(\\d+?((px)|(%)){0,1}))$/)) {\n    throw new Error('illegal size');\n  }\n  return v;\n}\n\n// name is base64 encoded utf-8\nfunction toName(data: Uint32Array): string {\n  if (typeof Buffer !== 'undefined') {\n    return Buffer.from(toStr(data), 'base64').toString();\n  }\n  const bs = atob(toStr(data));\n  const b = new Uint8Array(bs.length);\n  for (let i = 0; i < b.length; ++i) {\n    b[i] = bs.charCodeAt(i);\n  }\n  return new TextDecoder().decode(b);\n}\n\nconst DECODERS: {[key: string]: (v: Uint32Array) => any} = {\n  inline: toInt,\n  size: toInt,\n  name: toName,\n  width: toSize,\n  height: toSize,\n  preserveAspectRatio: toInt\n};\n\n\nconst FILE_MARKER = [70, 105, 108, 101];\nconst MAX_FIELDCHARS = 1024;\n\n\nexport class HeaderParser {\n  public state: HeaderState = HeaderState.START;\n  private _buffer = new Uint32Array(MAX_FIELDCHARS);\n  private _position = 0;\n  private _key = '';\n  public fields: {[key: string]: any} = {};\n\n  public reset(): void {\n    this._buffer.fill(0);\n    this.state = HeaderState.START;\n    this._position = 0;\n    this.fields = {};\n    this._key = '';\n  }\n\n  public parse(data: Uint32Array, start: number, end: number): number {\n    let state = this.state;\n    let pos = this._position;\n    const buffer = this._buffer;\n    if (state === HeaderState.ABORT || state === HeaderState.END) return -1;\n    if (state === HeaderState.START && pos > 6) return -1;\n    for (let i = start; i < end; ++i) {\n      const c = data[i];\n      switch (c) {\n        case 59: // ;\n          if (!this._storeValue(pos)) return this._a();\n          state = HeaderState.KEY;\n          pos = 0;\n          break;\n        case 61: // =\n          if (state === HeaderState.START) {\n            for (let k = 0; k < FILE_MARKER.length; ++k) {\n              if (buffer[k] !== FILE_MARKER[k]) return this._a();\n            }\n            state = HeaderState.KEY;\n            pos = 0;\n          } else if (state === HeaderState.KEY) {\n            if (!this._storeKey(pos)) return this._a();\n            state = HeaderState.VALUE;\n            pos = 0;\n          } else if (state === HeaderState.VALUE) {\n            if (pos >= MAX_FIELDCHARS) return this._a();\n            buffer[pos++] = c;\n          }\n          break;\n        case 58: // :\n          if (state === HeaderState.VALUE) {\n            if (!this._storeValue(pos)) return this._a();\n          }\n          this.state = HeaderState.END;\n          return i + 1;\n        default:\n          if (pos >= MAX_FIELDCHARS) return this._a();\n          buffer[pos++] = c;\n      }\n    }\n    this.state = state;\n    this._position = pos;\n    return -2;\n  }\n\n  private _a(): number {\n    this.state = HeaderState.ABORT;\n    return -1;\n  }\n\n  private _storeKey(pos: number): boolean {\n    const k = toStr(this._buffer.subarray(0, pos));\n    if (k) {\n      this._key = k;\n      this.fields[k] = null;\n      return true;\n    }\n    return false;\n  }\n\n  private _storeValue(pos: number): boolean {\n    if (this._key) {\n      try {\n        const v = this._buffer.slice(0, pos);\n        this.fields[this._key] = DECODERS[this._key] ? DECODERS[this._key](v) : v;\n      } catch (e) {\n        return false;\n      }\n      return true;\n    }\n    return false;\n  }\n}\n", "/**\n * Copyright (c) 2023 The xterm.js authors. All rights reserved.\n * @license MIT\n */\n\n\nexport type ImageType = 'image/png' | 'image/jpeg' | 'image/gif' | 'unsupported' | '';\n\nexport interface IMetrics {\n  mime: ImageType;\n  width: number;\n  height: number;\n}\n\nexport const UNSUPPORTED_TYPE: IMetrics = {\n  mime: 'unsupported',\n  width: 0,\n  height: 0\n};\n\nexport function imageType(d: Uint8Array): IMetrics {\n  if (d.length < 24) {\n    return UNSUPPORTED_TYPE;\n  }\n  const d32 = new Uint32Array(d.buffer, d.byteOffset, 6);\n  // PNG: 89 50 4E 47 0D 0A 1A 0A (8 first bytes == magic number for PNG)\n  // + first chunk must be IHDR\n  if (d32[0] === 0x474E5089 && d32[1] === 0x0A1A0A0D && d32[3] === 0x52444849) {\n    return {\n      mime: 'image/png',\n      width: d[16] << 24 | d[17] << 16 | d[18] << 8 | d[19],\n      height: d[20] << 24 | d[21] << 16 | d[22] << 8 | d[23]\n    };\n  }\n  // JPEG: FF D8 FF\n  if (d[0] === 0xFF && d[1] === 0xD8 && d[2] === 0xFF) {\n    const [width, height] = jpgSize(d);\n    return { mime: 'image/jpeg', width, height };\n  }\n  // GIF: GIF87a or GIF89a\n  if (d32[0] === 0x38464947 && (d[4] === 0x37 || d[4] === 0x39) && d[5] === 0x61) {\n    return {\n      mime: 'image/gif',\n      width: d[7] << 8 | d[6],\n      height: d[9] << 8 | d[8]\n    };\n  }\n  return UNSUPPORTED_TYPE;\n}\n\nfunction jpgSize(d: Uint8Array): [number, number] {\n  const len = d.length;\n  let i = 4;\n  let blockLength = d[i] << 8 | d[i + 1];\n  while (true) {\n    i += blockLength;\n    if (i >= len) {\n      // exhausted without size info\n      return [0, 0];\n    }\n    if (d[i] !== 0xFF) {\n      return [0, 0];\n    }\n    if (d[i + 1] === 0xC0 || d[i + 1] === 0xC2) {\n      if (i + 8 < len) {\n        return [\n          d[i + 7] << 8 | d[i + 8],\n          d[i + 5] << 8 | d[i + 6]\n        ];\n      }\n      return [0, 0];\n    }\n    i += 2;\n    blockLength = d[i] << 8 | d[i + 1];\n  }\n}\n", "/**\n * Copyright (c) 2020, 2023 The xterm.js authors. All rights reserved.\n * @license MIT\n */\n\nimport { ImageStorage } from './ImageStorage';\nimport { IDcs<PERSON><PERSON><PERSON>, IParams, IImageAddonOptions, ITerminalExt, AttributeData, IResetHandler, ReadonlyColorSet } from './Types';\nimport { toRGBA8888, BIG_ENDIAN, PALETTE_ANSI_256, PALETTE_VT340_COLOR } from 'sixel/lib/Colors';\nimport { RGBA8888 } from 'sixel/lib/Types';\nimport { ImageRenderer } from './ImageRenderer';\n\nimport { DecoderAsync, Decoder } from 'sixel/lib/Decoder';\n\n// always free decoder ressources after decoding if it exceeds this limit\nconst MEM_PERMA_LIMIT = 4194304; // 1024 pixels * 1024 pixels * 4 channels = 4MB\n\n// custom default palette: VT340 (lower 16 colors) + ANSI256 (up to 256) + zeroed (up to 4096)\nconst DEFAULT_PALETTE = PALETTE_ANSI_256;\nDEFAULT_PALETTE.set(PALETTE_VT340_COLOR);\n\n\nexport class <PERSON>el<PERSON>andler implements IDcsHandler, IResetHandler {\n  private _size = 0;\n  private _aborted = false;\n  private _dec: Decoder | undefined;\n\n  constructor(\n    private readonly _opts: IImageAddonOptions,\n    private readonly _storage: ImageStorage,\n    private readonly _coreTerminal: ITerminalExt\n  ) {\n    DecoderAsync({\n      memoryLimit: this._opts.pixelLimit * 4,\n      palette: DEFAULT_PALETTE,\n      paletteLimit: this._opts.sixelPaletteLimit\n    }).then(d => this._dec = d);\n  }\n\n  public reset(): void {\n    /**\n     * reset sixel decoder to defaults:\n     * - release all memory\n     * - nullify palette (4096)\n     * - apply default palette (256)\n     */\n    if (this._dec) {\n      this._dec.release();\n      // FIXME: missing interface on decoder to nullify full palette\n      (this._dec as any)._palette.fill(0);\n      this._dec.init(0, DEFAULT_PALETTE, this._opts.sixelPaletteLimit);\n    }\n  }\n\n  public hook(params: IParams): void {\n    this._size = 0;\n    this._aborted = false;\n    if (this._dec) {\n      const fillColor = params.params[1] === 1 ? 0 : extractActiveBg(\n        this._coreTerminal._core._inputHandler._curAttrData,\n        this._coreTerminal._core._themeService?.colors);\n      this._dec.init(fillColor, null, this._opts.sixelPaletteLimit);\n    }\n  }\n\n  public put(data: Uint32Array, start: number, end: number): void {\n    if (this._aborted || !this._dec) {\n      return;\n    }\n    this._size += end - start;\n    if (this._size > this._opts.sixelSizeLimit) {\n      console.warn(`SIXEL: too much data, aborting`);\n      this._aborted = true;\n      this._dec.release();\n      return;\n    }\n    try {\n      this._dec.decode(data, start, end);\n    } catch (e) {\n      console.warn(`SIXEL: error while decoding image - ${e}`);\n      this._aborted = true;\n      this._dec.release();\n    }\n  }\n\n  public unhook(success: boolean): boolean | Promise<boolean> {\n    if (this._aborted || !success || !this._dec) {\n      return true;\n    }\n\n    const width = this._dec.width;\n    const height = this._dec.height;\n\n    // partial fix for https://github.com/jerch/xterm-addon-image/issues/37\n    if (!width || ! height) {\n      if (height) {\n        this._storage.advanceCursor(height);\n      }\n      return true;\n    }\n\n    const canvas = ImageRenderer.createCanvas(undefined, width, height);\n    canvas.getContext('2d')?.putImageData(new ImageData(this._dec.data8, width, height), 0, 0);\n    if (this._dec.memoryUsage > MEM_PERMA_LIMIT) {\n      this._dec.release();\n    }\n    this._storage.addImage(canvas);\n    return true;\n  }\n}\n\n\n/**\n * Some helpers to extract current terminal colors.\n */\n\n// get currently active background color from terminal\n// also respect INVERSE setting\nfunction extractActiveBg(attr: AttributeData, colors: ReadonlyColorSet | undefined): RGBA8888 {\n  let bg = 0;\n  if (!colors) {\n    // FIXME: theme service is prolly not available yet,\n    // happens if .open() was not called yet (bug in core?)\n    return bg;\n  }\n  if (attr.isInverse()) {\n    if (attr.isFgDefault()) {\n      bg = convertLe(colors.foreground.rgba);\n    } else if (attr.isFgRGB()) {\n      const t = (attr.constructor as typeof AttributeData).toColorRGB(attr.getFgColor());\n      bg = toRGBA8888(...t);\n    } else {\n      bg = convertLe(colors.ansi[attr.getFgColor()].rgba);\n    }\n  } else {\n    if (attr.isBgDefault()) {\n      bg = convertLe(colors.background.rgba);\n    } else if (attr.isBgRGB()) {\n      const t = (attr.constructor as typeof AttributeData).toColorRGB(attr.getBgColor());\n      bg = toRGBA8888(...t);\n    } else {\n      bg = convertLe(colors.ansi[attr.getBgColor()].rgba);\n    }\n  }\n  return bg;\n}\n\n// rgba values on the color managers are always in BE, thus convert to LE\nfunction convertLe(color: number): RGBA8888 {\n  if (BIG_ENDIAN) return color;\n  return (color & 0xFF) << 24 | (color >>> 8 & 0xFF) << 16 | (color >>> 16 & 0xFF) << 8 | color >>> 24 & 0xFF;\n}\n", "/**\n * Copyright (c) 2020 The xterm.js authors. All rights reserved.\n * @license MIT\n */\n\nimport type { ITerminalAddon, IDisposable } from '@xterm/xterm';\nimport type { ImageAddon as IImageApi } from '@xterm/addon-image';\nimport { <PERSON><PERSON><PERSON><PERSON><PERSON> } from './IIPHandler';\nimport { <PERSON><PERSON>enderer } from './ImageRenderer';\nimport { ImageStorage, CELL_SIZE_DEFAULT } from './ImageStorage';\nimport { <PERSON><PERSON><PERSON>and<PERSON> } from './SixelHandler';\nimport { ITerminalExt, IImageAddonOptions, IResetHandler } from './Types';\n\n// default values of addon ctor options\nconst DEFAULT_OPTIONS: IImageAddonOptions = {\n  enableSizeReports: true,\n  pixelLimit: 16777216, // limit to 4096 * 4096 pixels\n  sixelSupport: true,\n  sixelScrolling: true,\n  sixelPaletteLimit: 256,\n  sixelSizeLimit: 25000000,\n  storageLimit: 128,\n  showPlaceholder: true,\n  iipSupport: true,\n  iipSizeLimit: 20000000\n};\n\n// max palette size supported by the sixel lib (compile time setting)\nconst MAX_SIXEL_PALETTE_SIZE = 4096;\n\n// definitions for _xtermGraphicsAttributes sequence\nconst enum GaItem {\n  COLORS = 1,\n  SIXEL_GEO = 2,\n  REGIS_GEO = 3\n}\nconst enum GaAction {\n  READ = 1,\n  SET_DEFAULT = 2,\n  SET = 3,\n  READ_MAX = 4\n}\nconst enum GaStatus {\n  SUCCESS = 0,\n  ITEM_ERROR = 1,\n  ACTION_ERROR = 2,\n  FAILURE = 3\n}\n\n\nexport class ImageAddon implements ITerminalAddon , IImageApi {\n  private _opts: IImageAddonOptions;\n  private _defaultOpts: IImageAddonOptions;\n  private _storage: ImageStorage | undefined;\n  private _renderer: ImageRenderer | undefined;\n  private _disposables: IDisposable[] = [];\n  private _terminal: ITerminalExt | undefined;\n  private _handlers: Map<String, IResetHandler> = new Map();\n\n  constructor(opts?: Partial<IImageAddonOptions>) {\n    this._opts = Object.assign({}, DEFAULT_OPTIONS, opts);\n    this._defaultOpts = Object.assign({}, DEFAULT_OPTIONS, opts);\n  }\n\n  public dispose(): void {\n    for (const obj of this._disposables) {\n      obj.dispose();\n    }\n    this._disposables.length = 0;\n    this._handlers.clear();\n  }\n\n  private _disposeLater(...args: IDisposable[]): void {\n    for (const obj of args) {\n      this._disposables.push(obj);\n    }\n  }\n\n  public activate(terminal: ITerminalExt): void {\n    this._terminal = terminal;\n\n    // internal data structures\n    this._renderer = new ImageRenderer(terminal);\n    this._storage = new ImageStorage(terminal, this._renderer, this._opts);\n\n    // enable size reports\n    if (this._opts.enableSizeReports) {\n      // const windowOptions = terminal.getOption('windowOptions');\n      // windowOptions.getWinSizePixels = true;\n      // windowOptions.getCellSizePixels = true;\n      // windowOptions.getWinSizeChars = true;\n      // terminal.setOption('windowOptions', windowOptions);\n      const windowOps = terminal.options.windowOptions || {};\n      windowOps.getWinSizePixels = true;\n      windowOps.getCellSizePixels = true;\n      windowOps.getWinSizeChars = true;\n      terminal.options.windowOptions = windowOps;\n    }\n\n    this._disposeLater(\n      this._renderer,\n      this._storage,\n\n      // DECSET/DECRST/DA1/XTSMGRAPHICS handlers\n      terminal.parser.registerCsiHandler({ prefix: '?', final: 'h' }, params => this._decset(params)),\n      terminal.parser.registerCsiHandler({ prefix: '?', final: 'l' }, params => this._decrst(params)),\n      terminal.parser.registerCsiHandler({ final: 'c' }, params => this._da1(params)),\n      terminal.parser.registerCsiHandler({ prefix: '?', final: 'S' }, params => this._xtermGraphicsAttributes(params)),\n\n      // render hook\n      terminal.onRender(range => this._storage?.render(range)),\n\n      /**\n       * reset handlers covered:\n       * - DECSTR\n       * - RIS\n       * - Terminal.reset()\n       */\n      terminal.parser.registerCsiHandler({ intermediates: '!', final: 'p' }, () => this.reset()),\n      terminal.parser.registerEscHandler({ final: 'c' }, () => this.reset()),\n      terminal._core._inputHandler.onRequestReset(() => this.reset()),\n\n      // wipe canvas and delete alternate images on buffer switch\n      terminal.buffer.onBufferChange(() => this._storage?.wipeAlternate()),\n\n      // extend images to the right on resize\n      terminal.onResize(metrics => this._storage?.viewportResize(metrics))\n    );\n\n    // SIXEL handler\n    if (this._opts.sixelSupport) {\n      const sixelHandler = new SixelHandler(this._opts, this._storage!, terminal);\n      this._handlers.set('sixel', sixelHandler);\n      this._disposeLater(\n        terminal._core._inputHandler._parser.registerDcsHandler({ final: 'q' }, sixelHandler)\n      );\n    }\n\n    // iTerm IIP handler\n    if (this._opts.iipSupport) {\n      const iipHandler = new IIPHandler(this._opts, this._renderer!, this._storage!, terminal);\n      this._handlers.set('iip', iipHandler);\n      this._disposeLater(\n        terminal._core._inputHandler._parser.registerOscHandler(1337, iipHandler)\n      );\n    }\n  }\n\n  // Note: storageLimit is skipped here to not intoduce a surprising side effect.\n  public reset(): boolean {\n    // reset options customizable by sequences to defaults\n    this._opts.sixelScrolling = this._defaultOpts.sixelScrolling;\n    this._opts.sixelPaletteLimit = this._defaultOpts.sixelPaletteLimit;\n    // also clear image storage\n    this._storage?.reset();\n    // reset protocol handlers\n    for (const handler of this._handlers.values()) {\n      handler.reset();\n    }\n    return false;\n  }\n\n  public get storageLimit(): number {\n    return this._storage?.getLimit() || -1;\n  }\n\n  public set storageLimit(limit: number) {\n    this._storage?.setLimit(limit);\n    this._opts.storageLimit = limit;\n  }\n\n  public get storageUsage(): number {\n    if (this._storage) {\n      return this._storage.getUsage();\n    }\n    return -1;\n  }\n\n  public get showPlaceholder(): boolean {\n    return this._opts.showPlaceholder;\n  }\n\n  public set showPlaceholder(value: boolean) {\n    this._opts.showPlaceholder = value;\n    this._renderer?.showPlaceholder(value);\n  }\n\n  public getImageAtBufferCell(x: number, y: number): HTMLCanvasElement | undefined {\n    return this._storage?.getImageAtBufferCell(x, y);\n  }\n\n  public extractTileAtBufferCell(x: number, y: number): HTMLCanvasElement | undefined {\n    return this._storage?.extractTileAtBufferCell(x, y);\n  }\n\n  private _report(s: string): void {\n    this._terminal?._core.coreService.triggerDataEvent(s);\n  }\n\n  private _decset(params: (number | number[])[]): boolean {\n    for (let i = 0; i < params.length; ++i) {\n      switch (params[i]) {\n        case 80:\n          this._opts.sixelScrolling = false;\n          break;\n      }\n    }\n    return false;\n  }\n\n  private _decrst(params: (number | number[])[]): boolean {\n    for (let i = 0; i < params.length; ++i) {\n      switch (params[i]) {\n        case 80:\n          this._opts.sixelScrolling = true;\n          break;\n      }\n    }\n    return false;\n  }\n\n  // overload DA to return something more appropriate\n  private _da1(params: (number | number[])[]): boolean {\n    if (params[0]) {\n      return true;\n    }\n    // reported features:\n    // 62 - VT220\n    // 4 - SIXEL support\n    // 9 - charsets\n    // 22 - ANSI colors\n    if (this._opts.sixelSupport) {\n      this._report(`\\x1b[?62;4;9;22c`);\n      return true;\n    }\n    return false;\n  }\n\n  /**\n   * Implementation of xterm's graphics attribute sequence.\n   *\n   * Supported features:\n   * - read/change palette limits (max 4096 by sixel lib)\n   * - read SIXEL canvas geometry (reports current window canvas or\n   *   squared pixelLimit if canvas > pixel limit)\n   *\n   * Everything else is deactivated.\n   */\n  private _xtermGraphicsAttributes(params: (number | number[])[]): boolean {\n    if (params.length < 2) {\n      return true;\n    }\n    if (params[0] === GaItem.COLORS) {\n      switch (params[1]) {\n        case GaAction.READ:\n          this._report(`\\x1b[?${params[0]};${GaStatus.SUCCESS};${this._opts.sixelPaletteLimit}S`);\n          return true;\n        case GaAction.SET_DEFAULT:\n          this._opts.sixelPaletteLimit = this._defaultOpts.sixelPaletteLimit;\n          this._report(`\\x1b[?${params[0]};${GaStatus.SUCCESS};${this._opts.sixelPaletteLimit}S`);\n          // also reset protocol handlers for now\n          for (const handler of this._handlers.values()) {\n            handler.reset();\n          }\n          return true;\n        case GaAction.SET:\n          if (params.length > 2 && !(params[2] instanceof Array) && params[2] <= MAX_SIXEL_PALETTE_SIZE) {\n            this._opts.sixelPaletteLimit = params[2];\n            this._report(`\\x1b[?${params[0]};${GaStatus.SUCCESS};${this._opts.sixelPaletteLimit}S`);\n          } else {\n            this._report(`\\x1b[?${params[0]};${GaStatus.ACTION_ERROR}S`);\n          }\n          return true;\n        case GaAction.READ_MAX:\n          this._report(`\\x1b[?${params[0]};${GaStatus.SUCCESS};${MAX_SIXEL_PALETTE_SIZE}S`);\n          return true;\n        default:\n          this._report(`\\x1b[?${params[0]};${GaStatus.ACTION_ERROR}S`);\n          return true;\n      }\n    }\n    if (params[0] === GaItem.SIXEL_GEO) {\n      switch (params[1]) {\n        // we only implement read and read_max here\n        case GaAction.READ:\n          let width = this._renderer?.dimensions?.css.canvas.width;\n          let height = this._renderer?.dimensions?.css.canvas.height;\n          if (!width || !height) {\n            // for some reason we have no working image renderer\n            // --> fallback to default cell size\n            const cellSize = CELL_SIZE_DEFAULT;\n            width = (this._terminal?.cols || 80) * cellSize.width;\n            height = (this._terminal?.rows || 24) * cellSize.height;\n          }\n          if (width * height < this._opts.pixelLimit) {\n            this._report(`\\x1b[?${params[0]};${GaStatus.SUCCESS};${width.toFixed(0)};${height.toFixed(0)}S`);\n          } else {\n            // if we overflow pixelLimit report that squared instead\n            const x = Math.floor(Math.sqrt(this._opts.pixelLimit));\n            this._report(`\\x1b[?${params[0]};${GaStatus.SUCCESS};${x};${x}S`);\n          }\n          return true;\n        case GaAction.READ_MAX:\n          // read_max returns pixelLimit as square area\n          const x = Math.floor(Math.sqrt(this._opts.pixelLimit));\n          this._report(`\\x1b[?${params[0]};${GaStatus.SUCCESS};${x};${x}S`);\n          return true;\n        default:\n          this._report(`\\x1b[?${params[0]};${GaStatus.ACTION_ERROR}S`);\n          return true;\n      }\n    }\n    // exit with error on ReGIS or any other requests\n    this._report(`\\x1b[?${params[0]};${GaStatus.ITEM_ERROR}S`);\n    return true;\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;y0BAYaA,EAAA,WAAa,IAAI,WAAW,IAAI,YAAY,CAAC,UAAU,CAAC,EAAE,MAAM,EAAE,CAAC,IAAM,IAClFA,EAAA,YACF,QAAQ,KAAK,6EAA6E,EAI5F,SAAgBC,GAAIC,EAAW,CAC7B,OAAOA,EAAI,GACb,CAFAF,EAAA,IAAAC,GAIA,SAAgBE,GAAMD,EAAW,CAC/B,OAAQA,IAAM,EAAK,GACrB,CAFAF,EAAA,MAAAG,GAIA,SAAgBC,GAAKF,EAAW,CAC9B,OAAQA,IAAM,GAAM,GACtB,CAFAF,EAAA,KAAAI,GAIA,SAAgBC,GAAMH,EAAW,CAC/B,OAAQA,IAAM,GAAM,GACtB,CAFAF,EAAA,MAAAK,GAQA,SAAgBC,EAAW,EAAWC,EAAWC,EAAWC,EAAY,IAAG,CACzE,QAASA,EAAI,MAAS,IAAMD,EAAI,MAAS,IAAMD,EAAI,MAAS,EAAK,EAAI,OAAW,CAClF,CAFAP,EAAA,WAAAM,EAQA,SAAgBI,GAAaC,EAAe,CAC1C,MAAO,CAACA,EAAQ,IAAOA,GAAS,EAAK,IAAOA,GAAS,GAAM,IAAMA,IAAU,EAAE,CAC/E,CAFAX,EAAA,aAAAU,GASA,SAAgBE,GAAkBD,EAAiBE,EAAmB,CACpE,IAAMC,EAAIb,GAAIU,CAAK,EACbJ,EAAIJ,GAAMQ,CAAK,EACfH,EAAIJ,GAAKO,CAAK,EAEhBI,EAAM,OAAO,iBACbC,EAAM,GAGV,QAASC,EAAI,EAAGA,EAAIJ,EAAQ,OAAQ,EAAEI,EAAG,CACvC,IAAMC,EAAKJ,EAAID,EAAQI,CAAC,EAAE,CAAC,EACrBE,EAAKZ,EAAIM,EAAQI,CAAC,EAAE,CAAC,EACrBG,EAAKZ,EAAIK,EAAQI,CAAC,EAAE,CAAC,EACrBI,EAAIH,EAAKA,EAAKC,EAAKA,EAAKC,EAAKA,EACnC,GAAI,CAACC,EAAG,OAAOJ,EACXI,EAAIN,IACNA,EAAMM,EACNL,EAAMC,GAIV,OAAOD,CACT,CAtBAhB,EAAA,kBAAAY,GA4BA,SAASU,GAAMC,EAAaC,EAAcC,EAAa,CACrD,OAAO,KAAK,IAAIF,EAAK,KAAK,IAAIE,EAAOD,CAAI,CAAC,CAC5C,CAEA,SAASE,GAAIC,EAAYC,EAAYC,EAAS,CAC5C,OAAIA,EAAI,IAAGA,GAAK,GACZA,EAAI,IAAGA,GAAK,GACTA,EAAI,EAAI,EACXD,GAAMD,EAAKC,GAAM,EAAIC,EACrBA,EAAI,EAAI,EACNF,EACAE,EAAI,EAAI,EACND,GAAMD,EAAKC,IAAO,EAAIC,EAAI,GAC1BD,CACV,CAEA,SAASE,GAASC,EAAWC,EAAWC,EAAS,CAC/C,GAAI,CAACA,EAAG,CACN,IAAMC,EAAI,KAAK,MAAMF,EAAI,GAAG,EAC5B,OAAO1B,EAAW4B,EAAGA,EAAGA,CAAC,EAE3B,IAAMP,EAAKK,EAAI,GAAMA,GAAK,EAAIC,GAAKD,EAAIC,EAAID,EAAIC,EACzCL,EAAK,EAAII,EAAIL,EACnB,OAAOrB,EACLgB,GAAM,EAAG,IAAK,KAAK,MAAMI,GAAIC,EAAIC,EAAIG,EAAI,EAAI,CAAC,EAAI,GAAG,CAAC,EACtDT,GAAM,EAAG,IAAK,KAAK,MAAMI,GAAIC,EAAIC,EAAIG,CAAC,EAAI,GAAG,CAAC,EAC9CT,GAAM,EAAG,IAAK,KAAK,MAAMI,GAAIC,EAAIC,EAAIG,EAAI,EAAI,CAAC,EAAI,GAAG,CAAC,CAAC,CAE3D,CAKA,SAAgBI,EAAa,EAAW5B,EAAWC,EAAS,CAC1D,OAAQ,WAAa,KAAK,MAAMA,EAAI,IAAM,GAAG,GAAK,GAAK,KAAK,MAAMD,EAAI,IAAM,GAAG,GAAK,EAAI,KAAK,MAAM,EAAI,IAAM,GAAG,KAAO,CACzH,CAFAP,EAAA,aAAAmC,EAQA,SAAgBC,GAAaL,EAAWC,EAAWC,EAAS,CAE1D,OAAOH,IAAUC,EAAI,IAAM,KAAO,IAAKC,EAAI,IAAKC,EAAI,GAAG,CACzD,CAHAjC,EAAA,aAAAoC,GAqCapC,EAAA,oBAAsB,IAAI,YAAY,CACjDmC,EAAc,EAAI,EAAI,CAAC,EACvBA,EAAa,GAAI,GAAI,EAAE,EACvBA,EAAa,GAAI,GAAI,EAAE,EACvBA,EAAa,GAAI,GAAI,EAAE,EACvBA,EAAa,GAAI,GAAI,EAAE,EACvBA,EAAa,GAAI,GAAI,EAAE,EACvBA,EAAa,GAAI,GAAI,EAAE,EACvBA,EAAa,GAAI,GAAI,EAAE,EACvBA,EAAa,GAAI,GAAI,EAAE,EACvBA,EAAa,GAAI,GAAI,EAAE,EACvBA,EAAa,GAAI,GAAI,EAAE,EACvBA,EAAa,GAAI,GAAI,EAAE,EACvBA,EAAa,GAAI,GAAI,EAAE,EACvBA,EAAa,GAAI,GAAI,EAAE,EACvBA,EAAa,GAAI,GAAI,EAAE,EACvBA,EAAa,GAAI,GAAI,EAAE,EACxB,EA0BYnC,EAAA,mBAAqB,IAAI,YAAY,CAChDmC,EAAc,EAAI,EAAI,CAAC,EACvBA,EAAa,GAAI,GAAI,EAAE,EACvBA,EAAa,GAAI,GAAI,EAAE,EACvBA,EAAa,GAAI,GAAI,EAAE,EACvBA,EAAc,EAAI,EAAI,CAAC,EACvBA,EAAa,GAAI,GAAI,EAAE,EACvBA,EAAa,GAAI,GAAI,EAAE,EACvBA,EAAa,GAAI,GAAI,EAAE,EACvBA,EAAc,EAAI,EAAI,CAAC,EACvBA,EAAa,GAAI,GAAI,EAAE,EACvBA,EAAa,GAAI,GAAI,EAAE,EACvBA,EAAa,GAAI,GAAI,EAAE,EACvBA,EAAc,EAAI,EAAI,CAAC,EACvBA,EAAa,GAAI,GAAI,EAAE,EACvBA,EAAa,GAAI,GAAI,EAAE,EACvBA,EAAa,GAAI,GAAI,EAAE,EACxB,EAOYnC,EAAA,kBAAoB,IAAK,CAEpC,IAAMqC,EAAgB,CACpB/B,EAAW,EAAG,EAAG,CAAC,EAClBA,EAAW,IAAK,EAAG,CAAC,EACpBA,EAAW,EAAG,IAAK,CAAC,EACpBA,EAAW,IAAK,IAAK,CAAC,EACtBA,EAAW,EAAG,EAAG,GAAG,EACpBA,EAAW,IAAK,EAAG,GAAG,EACtBA,EAAW,EAAG,IAAK,GAAG,EACtBA,EAAW,IAAK,IAAK,GAAG,EACxBA,EAAW,IAAK,IAAK,GAAG,EACxBA,EAAW,IAAK,EAAG,CAAC,EACpBA,EAAW,EAAG,IAAK,CAAC,EACpBA,EAAW,IAAK,IAAK,CAAC,EACtBA,EAAW,GAAI,GAAI,GAAG,EACtBA,EAAW,IAAK,EAAG,GAAG,EACtBA,EAAW,EAAG,IAAK,GAAG,EACtBA,EAAW,IAAK,IAAK,GAAG,GAGpBe,EAAI,CAAC,EAAG,GAAI,IAAK,IAAK,IAAK,GAAG,EACpC,QAASP,EAAI,EAAGA,EAAI,EAAG,EAAEA,EACvB,QAASP,EAAI,EAAGA,EAAI,EAAG,EAAEA,EACvB,QAASC,EAAI,EAAGA,EAAI,EAAG,EAAEA,EACvB6B,EAAE,KAAK/B,EAAWe,EAAEP,CAAC,EAAGO,EAAEd,CAAC,EAAGc,EAAEb,CAAC,CAAC,CAAC,EAKzC,QAAS0B,EAAI,EAAGA,GAAK,IAAKA,GAAK,GAC7BG,EAAE,KAAK/B,EAAW4B,EAAGA,EAAGA,CAAC,CAAC,EAE5B,OAAO,IAAI,YAAYG,CAAC,CAC1B,GAAE,EASWrC,EAAA,mBAA+BM,EAAW,EAAG,EAAG,EAAG,GAAG,EACtDN,EAAA,mBAA+BM,EAAW,IAAK,IAAK,IAAK,GAAG,kGC2BzE,SAASgC,EAAKC,EAAS,CACrB,GAAI,OAAO,OAAW,IAAa,OAAO,OAAO,KAAKA,EAAG,QAAQ,EACjE,IAAMC,EAAK,KAAKD,CAAC,EACXE,EAAI,IAAI,WAAWD,EAAG,MAAM,EAClC,QAAS,EAAI,EAAG,EAAIC,EAAE,OAAQ,EAAE,EAAGA,EAAE,CAAC,EAAID,EAAG,WAAW,CAAC,EACzD,OAAOC,CACT,CA8CA,SAAgBC,GAAkCC,EAAM,CACtD,GAAKA,EAAY,EAAG,CAIlB,GAAM,CAAE,EAAAC,EAAG,EAAAL,EAAG,EAAAM,CAAC,EAAKF,EAEhBG,EACAC,EACEC,EAAI,YACV,OAAIJ,IAAC,EACCL,EAAU,IAAMO,IAAUA,EAAQR,EAAKO,CAAC,GACrC,IAAM,QAAQ,QAAQC,IAAUA,EAAQR,EAAKO,CAAC,EAAE,EAErDD,IAAC,EACCL,EAAU,IAAMQ,IAAQA,EAAM,IAAIC,EAAE,OAAOF,IAAUA,EAAQR,EAAKO,CAAC,EAAE,GAClE,IAAME,EACT,QAAQ,QAAQA,CAAG,EACnBC,EAAE,QAAQF,IAAUA,EAAQR,EAAKO,CAAC,EAAE,EAAE,KAAKI,GAAKF,EAAME,CAAmB,EAE3EV,EACMW,GAA4B,IAAIF,EAAE,SAASD,IAAQA,EAAM,IAAIC,EAAE,OAAOF,IAAUA,EAAQR,EAAKO,CAAC,EAAE,GAAIK,CAAC,EACvGA,GAA4BH,EAChCC,EAAE,YAAYD,EAAKG,CAAC,EACpBF,EAAE,YAAYF,IAAUA,EAAQR,EAAKO,CAAC,GAAIK,CAAC,EAAE,KAAKT,IAAMM,EAAMN,EAAE,SAAWA,EAAE,QAA4B,EAG/G,GAAI,OAAO,SAAa,IAAa,MAAM,IAAI,MAAM,mBAAmB,EACxE,SAAS,IAAIE,CAAG,CAClB,CA7BAQ,GAAA,OAAAT,KC/VA,IAAAU,GAAAC,EAAAC,IAAA,cACA,OAAO,eAAeA,GAAS,aAAc,CAAE,MAAO,EAAK,CAAC,EAK5D,IAAMC,GAAW,KAIXC,MAAiBD,GAAS,QAAuD,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,02BAA02B,CAA8C,EAEp/BE,EAAM,IAAI,WAAW,mEACtB,MAAM,EAAE,EACR,IAAIC,GAAMA,EAAG,WAAW,CAAC,CAAC,CAAC,EAE1BC,EAAI,IAAI,YAAY,IAAI,EAC9BA,EAAE,KAAK,UAAU,EACjB,QAASC,EAAI,EAAGA,EAAIH,EAAI,OAAQ,EAAEG,EAC9BD,EAAEF,EAAIG,CAAC,CAAC,EAAIA,GAAK,EACrB,QAASA,EAAI,EAAGA,EAAIH,EAAI,OAAQ,EAAEG,EAC9BD,EAAE,IAAMF,EAAIG,CAAC,CAAC,EAAIA,GAAK,GAAMA,GAAK,EAAK,MAAS,EACpD,QAASA,EAAI,EAAGA,EAAIH,EAAI,OAAQ,EAAEG,EAC9BD,EAAE,IAAMF,EAAIG,CAAC,CAAC,EAAKA,GAAK,GAAM,GAAMA,GAAK,EAAK,MAAS,GAC3D,QAASA,EAAI,EAAGA,EAAIH,EAAI,OAAQ,EAAEG,EAC9BD,EAAE,IAAMF,EAAIG,CAAC,CAAC,EAAIA,GAAK,GAC3B,IAAMC,GAAQ,IAAI,WAAW,CAAC,EAYxBC,GAAN,KAAoB,CAChB,YAAYC,EAAU,CAClB,KAAK,SAAWA,CACpB,CAKA,IAAI,OAAQ,CACR,OAAO,KAAK,MAAQ,KAAK,GAAG,SAAS,EAAG,KAAK,KAAK,IAAuB,CAAC,EAAIF,EAClF,CAMA,SAAU,CACD,KAAK,QAEN,KAAK,KAAK,OAAO,WAAa,KAAK,SACnC,KAAK,MAAQ,KAAK,KAAO,KAAK,GAAK,KAAK,KAAO,MAG/C,KAAK,KAAK,IAAuB,EAAI,EACrC,KAAK,KAAK,IAAuB,EAAI,EACrC,KAAK,KAAK,IAAuB,EAAI,GAE7C,CAQA,KAAKG,EAAM,CACP,IAAIC,EAAI,KAAK,KACPC,GAAS,KAAK,KAAKF,EAAO,CAAC,EAAI,MAA6B,EAC7D,KAAK,MAOD,KAAK,KAAK,OAAO,WAAaE,IACnC,KAAK,KAAK,KAAK,KAAK,MAAMA,EAAQ,KAAK,KAAK,OAAO,YAAc,KAAK,CAAC,EACvED,EAAI,IAAI,YAAY,KAAK,KAAK,OAAQ,CAAC,EACvC,KAAK,GAAK,IAAI,WAAW,KAAK,KAAK,OAAQ,KAA4B,CAAC,IATxE,KAAK,KAAO,IAAI,YAAY,OAAO,CAAE,QAAS,KAAK,KAAKC,EAAQ,KAAK,CAAE,CAAC,EACxE,KAAK,MAAQV,GAAW,CAAE,IAAK,CAAE,OAAQ,KAAK,IAAK,CAAE,CAAC,EACtDS,EAAI,IAAI,YAAY,KAAK,KAAK,OAAQ,CAAC,EACvCA,EAAE,IAAIN,EAAG,GAAgB,EACzB,KAAK,GAAK,IAAI,WAAW,KAAK,KAAK,OAAQ,KAA4B,CAAC,GAO5EM,EAAE,IAA0B,EAAID,EAChCC,EAAE,IAA0B,EAAI,KAAK,KAAKD,EAAO,CAAC,EAAI,EACtDC,EAAE,IAAuB,EAAI,EAC7BA,EAAE,IAAuB,EAAI,EAC7BA,EAAE,IAAuB,EAAI,EAC7B,KAAK,KAAOA,CAChB,CAMA,IAAIE,EAAMC,EAAOC,EAAK,CAClB,GAAI,CAAC,KAAK,MACN,MAAO,GACX,IAAMJ,EAAI,KAAK,KACf,OAAII,EAAMD,EAAQH,EAAE,IAAuB,EAAIA,EAAE,IAA0B,EAChE,GACX,KAAK,GAAG,IAAIE,EAAK,SAASC,EAAOC,CAAG,EAAGJ,EAAE,IAAuB,CAAC,EACjEA,EAAE,IAAuB,GAAKI,EAAMD,EAG7BH,EAAE,IAAuB,EAAIA,EAAE,IAAuB,GAAK,OAAS,KAAK,MAAM,QAAQ,IAAI,EAAI,EAC1G,CAOA,KAAM,CACF,OAAO,KAAK,MAAQ,KAAK,MAAM,QAAQ,IAAI,EAAI,CACnD,CACJ,EACAX,GAAQ,QAAUQ,mGCzHLQ,GAAA,OAAS,CACpB,WAAY,MACZ,aAAc,KACd,UAAW,MACX,MAAO,wtdCCT,IAAAC,EAAA,IACAC,EAAA,KAIA,SAASC,GAAaC,EAAS,CAC7B,GAAI,OAAO,OAAW,IACpB,OAAO,OAAO,KAAKA,EAAG,QAAQ,EAEhC,IAAMC,EAAa,KAAKD,CAAC,EACnBE,EAAS,IAAI,WAAWD,EAAW,MAAM,EAC/C,QAAS,EAAI,EAAG,EAAIC,EAAO,OAAQ,EAAE,EACnCA,EAAO,CAAC,EAAID,EAAW,WAAW,CAAC,EAErC,OAAOC,CACT,CAEA,IAAMC,GAAaJ,GAAaD,EAAA,OAAO,KAAK,EACxCM,EAGEC,GAAc,IAAI,YAIlBC,GAAN,KAAmB,CAAnB,aAAA,CACS,KAAA,YAAeC,GAAkB,EACjC,KAAA,YAAeC,GAAoB,CAO5C,CANS,YAAYD,EAAa,CAC9B,OAAO,KAAK,YAAYA,CAAK,CAC/B,CACO,YAAYC,EAAY,CAC7B,OAAO,KAAK,YAAYA,CAAI,CAC9B,GAKIC,GAA2C,CAC/C,YAAa,KAAO,MACpB,WAAYZ,EAAA,mBACZ,UAAWA,EAAA,mBACX,QAASA,EAAA,oBACT,aAAcC,EAAA,OAAO,aACrB,SAAU,IAQZ,SAAgBY,GAAaC,EAAsB,CACjD,IAAMC,EAAU,IAAIN,GACdO,EAAY,CAChB,IAAK,CACH,YAAaD,EAAQ,YAAY,KAAKA,CAAO,EAC7C,YAAaA,EAAQ,YAAY,KAAKA,CAAO,IAGjD,OAAO,YAAY,YAAYR,GAAeD,GAAYU,CAAS,EAChE,KAAMC,IACLV,EAAcA,GAAeU,EAAK,OAC3B,IAAIC,EAAQJ,EAAMG,EAAK,UAAYA,EAAMF,CAAO,EACxD,CACL,CAbAI,EAAA,aAAAN,GAgDA,IAAaK,EAAb,KAAoB,CAwGlB,YACEJ,EACAM,EACAC,EAAwB,CAGxB,GAvGM,KAAA,cAAgBpB,EAAA,OAAO,UAAY,EAEnC,KAAA,QAAuBO,GACvB,KAAA,YAAwB,CAAA,EACxB,KAAA,UAAY,EACZ,KAAA,UAAYP,EAAA,OAAO,UACnB,KAAA,YAAc,EACd,KAAA,eAAiB,EA+FvB,KAAK,MAAQ,OAAO,OAAO,CAAA,EAAIW,GAAiBE,CAAI,EAChD,KAAK,MAAM,aAAeb,EAAA,OAAO,aACnC,MAAM,IAAI,MAAM,+CAA+CA,EAAA,OAAO,YAAY,EAAE,EAEtF,GAAKmB,EASHC,EAAU,YAAc,KAAK,aAAa,KAAK,IAAI,EACnDA,EAAU,YAAc,KAAK,YAAY,KAAK,IAAI,MAVpC,CACd,IAAMC,EAASf,IAAgBA,EAAc,IAAI,YAAY,OAAOD,EAAU,GAC9Ec,EAAY,IAAI,YAAY,SAASE,EAAQ,CAC3C,IAAK,CACH,YAAa,KAAK,aAAa,KAAK,IAAI,EACxC,YAAa,KAAK,YAAY,KAAK,IAAI,GAE1C,EAKH,KAAK,UAAYF,EACjB,KAAK,MAAQ,KAAK,UAAU,QAC5B,KAAK,OAAS,IAAI,WAAW,KAAK,MAAM,OAAO,OAAQ,KAAK,MAAM,kBAAiB,EAAInB,EAAA,OAAO,UAAU,EACxG,KAAK,QAAU,IAAI,YAAY,KAAK,MAAM,OAAO,OAAQ,KAAK,MAAM,kBAAiB,EAAI,EAAE,EAC3F,KAAK,SAAW,IAAI,YAAY,KAAK,MAAM,OAAO,OAAQ,KAAK,MAAM,oBAAmB,EAAIA,EAAA,OAAO,YAAY,EAC/G,KAAK,SAAS,IAAI,KAAK,MAAM,OAAO,EACpC,KAAK,MAAQ,IAAI,YAAY,KAAK,MAAM,OAAO,OAAQ,KAAK,MAAM,eAAc,CAAE,EAClF,KAAK,MAAM,KAAKD,EAAA,mBAAoB,EAAG,KAAK,MAAM,aAAc,CAAC,CACnE,CApHA,IAAY,YAAU,CAAe,OAAO,KAAK,QAAQ,CAAC,CAAG,CAC7D,IAAY,WAAS,CAAa,OAAO,KAAK,QAAQ,CAAC,CAAG,CAC1D,IAAY,cAAY,CAAa,OAAO,KAAK,QAAQ,CAAC,CAAG,CAC7D,IAAY,eAAa,CAAa,OAAO,KAAK,QAAQ,CAAC,CAAG,CAC9D,IAAY,QAAM,CAAa,OAAO,KAAK,QAAQ,CAAC,EAAI,KAAK,QAAQ,CAAC,EAAI,EAAI,CAAG,CACjF,IAAY,SAAO,CAAa,OAAO,KAAK,QAAQ,CAAC,CAAG,CACxD,IAAY,QAAM,CAAa,OAAO,KAAK,QAAQ,CAAC,CAAG,CACvD,IAAY,OAAK,CAAgB,OAAO,KAAK,QAAQ,EAAE,CAAG,CAC1D,IAAY,eAAa,CAAa,OAAO,KAAK,QAAQ,EAAE,CAAG,CAEvD,YAAYW,EAAe,CACjC,GAAIA,IAAI,EAAmB,CACzB,IAAMY,EAAS,KAAK,MAAQ,KAAK,OACjC,GAAIA,EAAS,KAAK,QAAQ,OAAQ,CAChC,GAAI,KAAK,MAAM,aAAeA,EAAS,EAAI,KAAK,MAAM,YACpD,WAAK,QAAO,EACN,IAAI,MAAM,4BAA4B,EAE9C,KAAK,QAAU,IAAI,YAAYA,CAAM,EAEvC,KAAK,UAAY,KAAK,eACbZ,IAAI,EACb,GAAI,KAAK,SAAW,EAAG,CAErB,IAAMY,EAAS,KAAK,IAAI,KAAK,aAActB,EAAA,OAAO,SAAS,EAAI,KAAK,cACpE,GAAIsB,EAAS,KAAK,QAAQ,OAAQ,CAChC,GAAI,KAAK,MAAM,aAAeA,EAAS,EAAI,KAAK,MAAM,YACpD,WAAK,QAAO,EACN,IAAI,MAAM,4BAA4B,EAE9C,KAAK,QAAU,IAAI,YAAYA,CAAM,QAInC,KAAK,QAAQ,OAAS,QACxB,KAAK,QAAU,IAAI,YAAY,KAAK,GAI1C,MAAO,EACT,CAEQ,SAASC,EAAgBC,EAAwB,CACvD,IAAMF,EAASC,EAASC,EACxB,GAAIF,EAAS,KAAK,QAAQ,OAAQ,CAChC,GAAI,KAAK,MAAM,aAAeA,EAAS,EAAI,KAAK,MAAM,YACpD,WAAK,QAAO,EACN,IAAI,MAAM,4BAA4B,EAG9C,IAAMG,EAAY,IAAI,YAAY,KAAK,KAAKH,EAAS,KAAK,EAAI,KAAK,EACnEG,EAAU,IAAI,KAAK,OAAO,EAC1B,KAAK,QAAUA,EAEnB,CAEQ,aAAahB,EAAa,CAChC,IAAMiB,EAAM,KAAK,cACbH,EAAS,KAAK,YAClB,GAAI,KAAK,QAAK,EAAmB,CAC/B,IAAII,EAAY,KAAK,OAAS,KAAK,eAC/BC,EAAI,EACR,KAAOA,EAAI,GAAKD,EAAY,GAC1B,KAAK,QAAQ,IAAI,KAAK,MAAM,SAASD,EAAME,EAAGF,EAAME,EAAInB,CAAK,EAAGc,EAASd,EAAQmB,CAAC,EAClFA,IACAD,IAEF,KAAK,aAAelB,EAAQmB,EAC5B,KAAK,gBAAkBA,UACd,KAAK,QAAK,EAAmB,CACtC,KAAK,SAASL,EAAQd,EAAQ,CAAC,EAC/B,KAAK,UAAY,KAAK,IAAI,KAAK,UAAWA,CAAK,EAC/C,KAAK,UAAY,KAAK,IAAI,KAAK,UAAWA,CAAK,EAC/C,QAASoB,EAAI,EAAGA,EAAI,EAAG,EAAEA,EACvB,KAAK,QAAQ,IAAI,KAAK,MAAM,SAASH,EAAMG,EAAGH,EAAMG,EAAIpB,CAAK,EAAGc,EAASd,EAAQoB,CAAC,EAEpF,KAAK,YAAY,KAAKpB,CAAK,EAC3B,KAAK,aAAeA,EAAQ,EAC5B,KAAK,gBAAkB,EAEzB,MAAO,EACT,CA0CA,IAAW,OAAK,CACd,OAAO,KAAK,QAAK,EACb,KAAK,OACL,KAAK,IAAI,KAAK,UAAW,KAAK,MAAM,cAAa,CAAE,CACzD,CAOA,IAAW,QAAM,CACf,OAAO,KAAK,QAAK,EACb,KAAK,QACL,KAAK,MAAM,cAAa,EACtB,KAAK,YAAY,OAAS,EAAI,KAAK,MAAM,eAAc,EACvD,KAAK,YAAY,OAAS,CAClC,CAKA,IAAW,SAAO,CAChB,OAAO,KAAK,SAAS,SAAS,EAAG,KAAK,aAAa,CACrD,CAWA,IAAW,aAAW,CACpB,OAAO,KAAK,QAAQ,WAAa,KAAK,MAAM,OAAO,OAAO,WAAa,EAAI,KAAK,YAAY,MAC9F,CAKA,IAAW,YAAU,CACnB,MAAO,CACL,MAAO,KAAK,MACZ,OAAQ,KAAK,OACb,KAAM,KAAK,MACX,MAAO,KAAK,OACZ,SAAU,CAAC,CAAC,KAAK,UACjB,aAAc,KAAK,cACnB,UAAW,KAAK,WAChB,SAAU,KAAK,YACf,iBAAkB,CAChB,UAAW,KAAK,QAAQ,CAAC,EACzB,YAAa,KAAK,QAAQ,CAAC,EAC3B,MAAO,KAAK,aACZ,OAAQ,KAAK,eAGnB,CAOO,KACLqB,EAAsB,KAAK,MAAM,UACjCC,EAA8B,KAAK,MAAM,QACzCC,EAAuB,KAAK,MAAM,aAClCC,EAAoB,KAAK,MAAM,SAAQ,CAEvC,KAAK,MAAM,KAAK,KAAK,MAAM,WAAYH,EAAWE,EAAcC,EAAW,EAAI,CAAC,EAC5EF,GACF,KAAK,SAAS,IAAIA,EAAQ,SAAS,EAAG/B,EAAA,OAAO,YAAY,CAAC,EAE5D,KAAK,YAAY,OAAS,EAC1B,KAAK,UAAY,EACjB,KAAK,UAAYA,EAAA,OAAO,UACxB,KAAK,YAAc,EACnB,KAAK,eAAiB,CACxB,CAMO,OAAOkC,EAAsBC,EAAgB,EAAGC,EAAcF,EAAK,OAAM,CAC9E,IAAIG,EAAIF,EACR,KAAOE,EAAID,GAAK,CACd,IAAME,EAAS,KAAK,IAAIF,EAAMC,EAAGrC,EAAA,OAAO,UAAU,EAClD,KAAK,OAAO,IAAIkC,EAAK,SAASG,EAAGA,GAAKC,CAAM,CAAC,EAC7C,KAAK,MAAM,OAAO,EAAGA,CAAM,EAE/B,CAOO,aAAaJ,EAAcC,EAAgB,EAAGC,EAAcF,EAAK,OAAM,CAC5E,IAAIG,EAAIF,EACR,KAAOE,EAAID,GAAK,CACd,IAAME,EAAS,KAAK,IAAIF,EAAMC,EAAGrC,EAAA,OAAO,UAAU,EAClD,QAAS6B,EAAI,EAAGU,EAAIF,EAAGR,EAAIS,EAAQ,EAAET,EAAG,EAAEU,EACxC,KAAK,OAAOV,CAAC,EAAIK,EAAK,WAAWK,CAAC,EAEpCF,GAAKC,EACL,KAAK,MAAM,OAAO,EAAGA,CAAM,EAE/B,CAMA,IAAW,QAAM,CACf,GAAI,KAAK,QAAK,GAAqB,CAAC,KAAK,OAAS,CAAC,KAAK,OACtD,OAAO/B,GAIT,IAAMiC,EAAe,KAAK,MAAM,cAAa,EAE7C,GAAI,KAAK,QAAK,EAAmB,CAC/B,IAAIb,EAAY,KAAK,OAAS,KAAK,eACnC,GAAIA,EAAY,EAAG,CACjB,IAAMD,EAAM,KAAK,cACbH,EAAS,KAAK,YACdK,EAAI,EACR,KAAOA,EAAI,GAAKD,EAAY,GAC1B,KAAK,QAAQ,IAAI,KAAK,MAAM,SAASD,EAAME,EAAGF,EAAME,EAAIY,CAAY,EAAGjB,EAASiB,EAAeZ,CAAC,EAChGA,IACAD,IAEEA,GACF,KAAK,QAAQ,KAAK,KAAK,WAAYJ,EAASiB,EAAeZ,CAAC,EAGhE,OAAO,KAAK,QAAQ,SAAS,EAAG,KAAK,MAAQ,KAAK,MAAM,EAG1D,GAAI,KAAK,QAAK,EAAmB,CAC/B,GAAI,KAAK,YAAc,KAAK,UAAW,CACrC,IAAIa,EAAS,GACb,GAAID,EACF,GAAIA,IAAiB,KAAK,UACxBC,EAAS,OACJ,CACL,IAAMf,EAAM,KAAK,cACbH,EAAS,KAAK,YAClB,KAAK,SAASA,EAAQiB,EAAe,CAAC,EACtC,QAASX,EAAI,EAAGA,EAAI,EAAG,EAAEA,EACvB,KAAK,QAAQ,IAAI,KAAK,MAAM,SAASH,EAAMG,EAAGH,EAAMG,EAAIW,CAAY,EAAGjB,EAASiB,EAAeX,CAAC,EAItG,GAAI,CAACY,EACH,OAAO,KAAK,QAAQ,SAAS,EAAG,KAAK,MAAQ,KAAK,MAAM,EAM5D,IAAMC,EAAQ,IAAI,YAAY,KAAK,MAAQ,KAAK,MAAM,EACtDA,EAAM,KAAK,KAAK,UAAU,EAC1B,IAAIC,EAAc,EACdR,EAAQ,EACZ,QAASN,EAAI,EAAGA,EAAI,KAAK,YAAY,OAAQ,EAAEA,EAAG,CAChD,IAAMe,EAAK,KAAK,YAAYf,CAAC,EAC7B,QAASQ,EAAI,EAAGA,EAAI,EAAG,EAAEA,EACvBK,EAAM,IAAI,KAAK,QAAQ,SAASP,EAAOA,GAASS,CAAE,EAAGD,CAAW,EAChEA,GAAe,KAAK,MAIxB,GAAIH,EAAc,CAChB,IAAMd,EAAM,KAAK,cAEXmB,EAAgB,KAAK,MAAM,eAAc,EAC/C,QAAShB,EAAI,EAAGA,EAAIgB,EAAe,EAAEhB,EACnCa,EAAM,IAAI,KAAK,MAAM,SAAShB,EAAMG,EAAGH,EAAMG,EAAIW,CAAY,EAAGG,EAAc,KAAK,MAAQd,CAAC,EAGhG,OAAOa,EAIT,OAAOnC,EACT,CAMA,IAAW,OAAK,CACd,OAAO,IAAI,kBAAkB,KAAK,OAAO,OAAQ,EAAG,KAAK,MAAQ,KAAK,OAAS,CAAC,CAClF,CAcO,SAAO,CACZ,KAAK,QAAUA,GACf,KAAK,YAAY,OAAS,EAC1B,KAAK,UAAY,EACjB,KAAK,UAAYP,EAAA,OAAO,UAGxB,KAAK,MAAM,KAAKD,EAAA,mBAAoB,EAAG,KAAK,MAAM,aAAc,CAAC,CACnE,GAxWFmB,EAAA,QAAAD,EA2XC,SAAgB6B,GACfZ,EACArB,EAAsB,CAEtB,IAAMkC,EAAM,IAAI9B,EAAQJ,CAAI,EAC5B,OAAAkC,EAAI,KAAI,EACR,OAAOb,GAAS,SAAWa,EAAI,aAAab,CAAI,EAAIa,EAAI,OAAOb,CAAI,EAC5D,CACL,MAAOa,EAAI,MACX,OAAQA,EAAI,OACZ,OAAQA,EAAI,OACZ,MAAOA,EAAI,MAEf,CAbC7B,EAAA,OAAA4B,GAoBM,eAAeE,GACpBd,EACArB,EAAsB,CAEtB,IAAMkC,EAAM,MAAMnC,GAAaC,CAAI,EACnC,OAAAkC,EAAI,KAAI,EACR,OAAOb,GAAS,SAAWa,EAAI,aAAab,CAAI,EAAIa,EAAI,OAAOb,CAAI,EAC5D,CACL,MAAOa,EAAI,MACX,OAAQA,EAAI,OACZ,OAAQA,EAAI,OACZ,MAAOA,EAAI,MAEf,CAbA7B,EAAA,YAAA8B,KCpfA,IAAAC,GAA2B,OCSpB,IAAMC,GAAN,KAAmB,CAIzB,aAAc,CAEb,KAAK,UAAY,CAAC,EAElB,KAAK,uBAAyB,SAAU,EAAQ,CAC/C,WAAW,IAAM,CAChB,MAAI,EAAE,MACDC,EAAiB,mBAAmB,CAAC,EAClC,IAAIA,EAAiB,EAAE,QAAU;AAAA;AAAA,EAAS,EAAE,KAAK,EAGlD,IAAI,MAAM,EAAE,QAAU;AAAA;AAAA,EAAS,EAAE,KAAK,EAGvC,CACP,EAAG,CAAC,CACL,CACD,CAEA,YAAYC,EAAsD,CACjE,YAAK,UAAU,KAAKA,CAAQ,EAErB,IAAM,CACZ,KAAK,gBAAgBA,CAAQ,CAC9B,CACD,CAEQ,KAAK,EAAc,CAC1B,KAAK,UAAU,QAASA,GAAa,CACpCA,EAAS,CAAC,CACX,CAAC,CACF,CAEQ,gBAAgBA,EAAuC,CAC9D,KAAK,UAAU,OAAO,KAAK,UAAU,QAAQA,CAAQ,EAAG,CAAC,CAC1D,CAEA,0BAA0BC,EAAmD,CAC5E,KAAK,uBAAyBA,CAC/B,CAEA,2BAA8C,CAC7C,OAAO,KAAK,sBACb,CAEA,kBAAkB,EAAc,CAC/B,KAAK,uBAAuB,CAAC,EAC7B,KAAK,KAAK,CAAC,CACZ,CAGA,0BAA0B,EAAc,CACvC,KAAK,uBAAuB,CAAC,CAC9B,CACD,EAEaC,GAAe,IAAIJ,GA6LzB,IAAMK,EAAN,MAAMC,UAAyB,KAAM,CAG3C,YAAYC,EAAc,CACzB,MAAMA,CAAG,EACT,KAAK,KAAO,mBACb,CAEA,OAAc,UAAUC,EAA8B,CACrD,GAAIA,aAAeF,EAClB,OAAOE,EAGR,IAAMC,EAAS,IAAIH,EACnB,OAAAG,EAAO,QAAUD,EAAI,QACrBC,EAAO,MAAQD,EAAI,MACZC,CACR,CAEA,OAAc,mBAAmBD,EAAqC,CACrE,OAAOA,EAAI,OAAS,mBACrB,CACD,ECjPO,SAASE,GAAyBC,EAAqBC,EAAiCC,EAAW,EAAGC,EAAWH,EAAM,OAAgB,CAC7I,IAAII,EAAIF,EACJG,EAAIF,EACR,KAAOC,EAAIC,GAAG,CACb,IAAMC,EAAI,KAAK,OAAOF,EAAIC,GAAK,CAAC,EAC5BJ,EAAUD,EAAMM,CAAC,CAAC,EACrBF,EAAIE,EAAI,EAERD,EAAIC,CAEN,CACA,OAAOF,EAAI,CACZ,CA4CO,IAAMG,EAAN,MAAMA,CAAmB,CAM/B,YAA6BC,EAAsB,CAAtB,YAAAA,EAH7B,KAAQ,2BAA6B,CAIrC,CAMA,mBAAmBC,EAAgD,CAClE,GAAIF,EAAgB,iBAAkB,CACrC,GAAI,KAAK,wBACR,QAAWG,KAAQ,KAAK,OACvB,GAAI,KAAK,uBAAuBA,CAAI,GAAK,CAACD,EAAUC,CAAI,EACvD,MAAM,IAAI,MAAM,8FAA8F,EAIjH,KAAK,uBAAyBD,CAC/B,CAEA,IAAME,EAAMC,GAAsB,KAAK,OAAQH,EAAW,KAAK,0BAA0B,EACzF,YAAK,2BAA6BE,EAAM,EACjCA,IAAQ,GAAK,OAAY,KAAK,OAAOA,CAAG,CAChD,CACD,EA7BaJ,EACE,iBAAmB,GAD3B,IAAMM,GAANN,EC8hBA,IAAUO,OAAV,CACC,SAASC,EAAWC,EAAgC,CAC1D,OAAOA,EAAS,CACjB,CAFOF,EAAS,WAAAC,EAIT,SAASE,EAAkBD,EAAgC,CACjE,OAAOA,GAAU,CAClB,CAFOF,EAAS,kBAAAG,EAIT,SAASC,EAAcF,EAAgC,CAC7D,OAAOA,EAAS,CACjB,CAFOF,EAAS,cAAAI,EAIT,SAASC,EAA2BH,EAAgC,CAC1E,OAAOA,IAAW,CACnB,CAFOF,EAAS,2BAAAK,EAIHL,EAAA,YAAc,EACdA,EAAA,SAAW,GACXA,EAAA,yBAA2B,IAnBxBA,KAAA,IA6BV,SAASM,GAA6BC,EAAuCC,EAAuD,CAC1I,MAAO,CAACC,EAAGC,IAAMF,EAAWD,EAASE,CAAC,EAAGF,EAASG,CAAC,CAAC,CACrD,CAiBO,IAAMC,GAAuC,CAACC,EAAGC,IAAMD,EAAIC,EA4F3D,IAAMC,EAAN,MAAMA,CAAoB,CAGhC,YAKiBC,EACf,CADe,aAAAA,CAEjB,CAEA,QAAQC,EAA4B,CACnC,KAAK,QAAQC,IAAUD,EAAQC,CAAI,EAAU,GAAO,CACrD,CAEA,SAAe,CACd,IAAMC,EAAc,CAAC,EACrB,YAAK,QAAQD,IAAUC,EAAO,KAAKD,CAAI,EAAU,GAAO,EACjDC,CACR,CAEA,OAAOC,EAAsD,CAC5D,OAAO,IAAIL,EAAiBM,GAAM,KAAK,QAAQH,GAAQE,EAAUF,CAAI,EAAIG,EAAGH,CAAI,EAAI,EAAI,CAAC,CAC1F,CAEA,IAAaI,EAAwD,CACpE,OAAO,IAAIP,EAA0BM,GAAM,KAAK,QAAQH,GAAQG,EAAGC,EAAMJ,CAAI,CAAC,CAAC,CAAC,CACjF,CAEA,KAAKE,EAA0C,CAC9C,IAAID,EAAS,GACb,YAAK,QAAQD,IAAUC,EAASC,EAAUF,CAAI,EAAU,CAACC,EAAS,EAC3DA,CACR,CAEA,UAAUC,EAAgD,CACzD,IAAID,EACJ,YAAK,QAAQD,GACRE,EAAUF,CAAI,GACjBC,EAASD,EACF,IAED,EACP,EACMC,CACR,CAEA,SAASC,EAAgD,CACxD,IAAID,EACJ,YAAK,QAAQD,IACRE,EAAUF,CAAI,IACjBC,EAASD,GAEH,GACP,EACMC,CACR,CAEA,cAAcI,EAA0C,CACvD,IAAIJ,EACAK,EAAQ,GACZ,YAAK,QAAQN,KACRM,GAASC,GAAc,cAAcF,EAAWL,EAAMC,CAAO,CAAC,KACjEK,EAAQ,GACRL,EAASD,GAEH,GACP,EACMC,CACR,CACD,EAvEaJ,EACW,MAAQ,IAAIA,EAAwBW,GAAa,CAAE,CAAC,EADrE,IAAMC,GAANZ,ECzvBA,SAASa,GAA+CC,EAAWC,EAA4C,CACrH,IAAMC,EAAyB,OAAO,OAAO,IAAI,EACjD,QAAWC,KAAWH,EAAM,CAC3B,IAAMI,EAAMH,EAAQE,CAAO,EACvBE,EAASH,EAAOE,CAAG,EAClBC,IACJA,EAASH,EAAOE,CAAG,EAAI,CAAC,GAEzBC,EAAO,KAAKF,CAAO,CACpB,CACA,OAAOD,CACR,CAhCA,IAAAI,GAAAC,GAmFaC,GAAN,KAAsC,CAG5C,YAAYC,EAAqBC,EAAsB,CAAtB,WAAAA,EAFjC,KAAQ,KAAO,IAAI,IAsDnB,KAACJ,IAA8B,aAnD9B,QAAWK,KAASF,EACnB,KAAK,IAAIE,CAAK,CAEhB,CAEA,IAAI,MAAe,CAClB,OAAO,KAAK,KAAK,IAClB,CAEA,IAAIA,EAAgB,CACnB,IAAMC,EAAM,KAAK,MAAMD,CAAK,EAC5B,YAAK,KAAK,IAAIC,EAAKD,CAAK,EACjB,IACR,CAEA,OAAOA,EAAmB,CACzB,OAAO,KAAK,KAAK,OAAO,KAAK,MAAMA,CAAK,CAAC,CAC1C,CAEA,IAAIA,EAAmB,CACtB,OAAO,KAAK,KAAK,IAAI,KAAK,MAAMA,CAAK,CAAC,CACvC,CAEA,CAAC,SAAoC,CACpC,QAAWE,KAAS,KAAK,KAAK,OAAO,EACpC,KAAM,CAACA,EAAOA,CAAK,CAErB,CAEA,MAA4B,CAC3B,OAAO,KAAK,OAAO,CACpB,CAEA,CAAC,QAA8B,CAC9B,QAAWA,KAAS,KAAK,KAAK,OAAO,EACpC,MAAMA,CAER,CAEA,OAAc,CACb,KAAK,KAAK,MAAM,CACjB,CAEA,QAAQC,EAAwDC,EAAqB,CACpF,KAAK,KAAK,QAAQF,GAASC,EAAW,KAAKC,EAASF,EAAOA,EAAO,IAAI,CAAC,CACxE,CAEA,EAACN,GAAA,OAAO,SAIPD,GAAA,OAAO,YAJPC,GAAe,GAAyB,CACxC,OAAO,KAAK,OAAO,CACpB,CAGD,ECRO,IAAMS,EAAN,KAAmB,CAAnB,cAEN,KAAQ,IAAM,IAAI,IAElB,IAAIC,EAAQC,EAAgB,CAC3B,IAAIC,EAAS,KAAK,IAAI,IAAIF,CAAG,EAExBE,IACJA,EAAS,IAAI,IACb,KAAK,IAAI,IAAIF,EAAKE,CAAM,GAGzBA,EAAO,IAAID,CAAK,CACjB,CAEA,OAAOD,EAAQC,EAAgB,CAC9B,IAAMC,EAAS,KAAK,IAAI,IAAIF,CAAG,EAE1BE,IAILA,EAAO,OAAOD,CAAK,EAEfC,EAAO,OAAS,GACnB,KAAK,IAAI,OAAOF,CAAG,EAErB,CAEA,QAAQA,EAAQG,EAA8B,CAC7C,IAAMD,EAAS,KAAK,IAAI,IAAIF,CAAG,EAE1BE,GAILA,EAAO,QAAQC,CAAE,CAClB,CAEA,IAAIH,EAAwB,CAC3B,IAAME,EAAS,KAAK,IAAI,IAAIF,CAAG,EAC/B,OAAKE,GACG,IAAI,GAGb,CACD,ECzKO,SAASE,GAA4DC,EAAOC,EAAkC,CACpH,IAAMC,EAAQ,KACVC,EAAU,GACVC,EAEJ,OAAO,UAAY,CAClB,GAAID,EACH,OAAOC,EAIR,GADAD,EAAU,GACNF,EACH,GAAI,CACHG,EAASJ,EAAG,MAAME,EAAO,SAAS,CACnC,QAAE,CACDD,EAAiB,CAClB,MAEAG,EAASJ,EAAG,MAAME,EAAO,SAAS,EAGnC,OAAOE,CACR,CACD,CC1BO,IAAUC,QAAV,CAEC,SAASC,EAAYC,EAAkC,CAC7D,OAAOA,GAAS,OAAOA,GAAU,UAAY,OAAOA,EAAM,OAAO,QAAQ,GAAM,UAChF,CAFOF,GAAS,GAAAC,EAIhB,IAAME,EAAwB,OAAO,OAAO,CAAC,CAAC,EACvC,SAASC,GAA8B,CAC7C,OAAOD,CACR,CAFOH,GAAS,MAAAI,EAIT,SAAUC,EAAUC,EAAyB,CACnD,MAAMA,CACP,CAFON,GAAU,OAAAK,EAIV,SAASE,EAAQC,EAAiD,CACxE,OAAIP,EAAGO,CAAiB,EAChBA,EAEAH,EAAOG,CAAiB,CAEjC,CANOR,GAAS,KAAAO,EAQT,SAASE,EAAQC,EAAuD,CAC9E,OAAOA,GAAYP,CACpB,CAFOH,GAAS,KAAAS,EAIT,SAAUE,EAAWC,EAA8B,CACzD,QAASC,EAAID,EAAM,OAAS,EAAGC,GAAK,EAAGA,IACtC,MAAMD,EAAMC,CAAC,CAEf,CAJOb,GAAU,QAAAW,EAMV,SAASG,EAAWJ,EAAmD,CAC7E,MAAO,CAACA,GAAYA,EAAS,OAAO,QAAQ,EAAE,EAAE,KAAK,EAAE,OAAS,EACjE,CAFOV,GAAS,QAAAc,EAIT,SAASC,EAASL,EAAsC,CAC9D,OAAOA,EAAS,OAAO,QAAQ,EAAE,EAAE,KAAK,EAAE,KAC3C,CAFOV,GAAS,MAAAe,EAIT,SAASC,EAAQN,EAAuBO,EAAkD,CAChG,IAAIJ,EAAI,EACR,QAAWP,KAAWI,EACrB,GAAIO,EAAUX,EAASO,GAAG,EACzB,MAAO,GAGT,MAAO,EACR,CAROb,GAAS,KAAAgB,EAYT,SAASE,EAAQR,EAAuBO,EAA6C,CAC3F,QAAWX,KAAWI,EACrB,GAAIO,EAAUX,CAAO,EACpB,OAAOA,CAKV,CARON,GAAS,KAAAkB,EAYT,SAAUC,EAAUT,EAAuBO,EAA2C,CAC5F,QAAWX,KAAWI,EACjBO,EAAUX,CAAO,IACpB,MAAMA,EAGT,CANON,GAAU,OAAAmB,EAQV,SAAUC,EAAUV,EAAuBW,EAA6C,CAC9F,IAAIC,EAAQ,EACZ,QAAWhB,KAAWI,EACrB,MAAMW,EAAGf,EAASgB,GAAO,CAE3B,CALOtB,GAAU,IAAAoB,EAOV,SAAUG,EAAcb,EAAuBW,EAAuD,CAC5G,IAAIC,EAAQ,EACZ,QAAWhB,KAAWI,EACrB,MAAOW,EAAGf,EAASgB,GAAO,CAE5B,CALOtB,GAAU,QAAAuB,EAOV,SAAUC,KAAaC,EAAuC,CACpE,QAAWf,KAAYe,EACtB,MAAOf,CAET,CAJOV,GAAU,OAAAwB,EAMV,SAASE,EAAahB,EAAuBiB,EAAmDC,EAAoB,CAC1H,IAAIC,EAAQD,EACZ,QAAWtB,KAAWI,EACrBmB,EAAQF,EAAQE,EAAOvB,CAAO,EAE/B,OAAOuB,CACR,CANO7B,GAAS,OAAA0B,EAWT,SAAUI,EAASC,EAAuBtB,EAAcuB,EAAKD,EAAI,OAAqB,CAW5F,IAVItB,EAAO,IACVA,GAAQsB,EAAI,QAGTC,EAAK,EACRA,GAAMD,EAAI,OACAC,EAAKD,EAAI,SACnBC,EAAKD,EAAI,QAGHtB,EAAOuB,EAAIvB,IACjB,MAAMsB,EAAItB,CAAI,CAEhB,CAdOT,GAAU,MAAA8B,EAoBV,SAASG,EAAWvB,EAAuBwB,EAAiB,OAAO,kBAAuC,CAChH,IAAMC,EAAgB,CAAC,EAEvB,GAAID,IAAW,EACd,MAAO,CAACC,EAAUzB,CAAQ,EAG3B,IAAM0B,EAAW1B,EAAS,OAAO,QAAQ,EAAE,EAE3C,QAASG,EAAI,EAAGA,EAAIqB,EAAQrB,IAAK,CAChC,IAAMwB,GAAOD,EAAS,KAAK,EAE3B,GAAIC,GAAK,KACR,MAAO,CAACF,EAAUnC,GAAS,MAAM,CAAC,EAGnCmC,EAAS,KAAKE,GAAK,KAAK,CACzB,CAEA,MAAO,CAACF,EAAU,CAAE,CAAC,OAAO,QAAQ,GAAI,CAAE,OAAOC,CAAU,CAAE,CAAC,CAC/D,CApBOpC,GAAS,QAAAiC,EAsBhB,eAAsBK,EAAgB5B,EAA0C,CAC/E,IAAM6B,EAAc,CAAC,EACrB,cAAiBC,KAAQ9B,EACxB6B,EAAO,KAAKC,CAAI,EAEjB,OAAO,QAAQ,QAAQD,CAAM,CAC9B,CANAvC,GAAsB,aAAAsC,IAlJNtC,KAAA,ICejB,IAAMyC,GAAoB,GACtBC,EAA+C,KAiCtCC,EAAN,MAAMA,CAAgD,CAAtD,cAGN,KAAiB,kBAAoB,IAAI,IAEjC,kBAAkBC,EAAgC,CACzD,IAAIC,EAAM,KAAK,kBAAkB,IAAID,CAAC,EACtC,OAAKC,IACJA,EAAM,CAAE,OAAQ,KAAM,OAAQ,KAAM,YAAa,GAAO,MAAOD,EAAG,IAAKD,EAAkB,KAAM,EAC/F,KAAK,kBAAkB,IAAIC,EAAGC,CAAG,GAE3BA,CACR,CAEA,gBAAgBD,EAAsB,CACrC,IAAME,EAAO,KAAK,kBAAkBF,CAAC,EAChCE,EAAK,SACTA,EAAK,OACJ,IAAI,MAAM,EAAE,MAEf,CAEA,UAAUC,EAAoBC,EAAkC,CAC/D,IAAMF,EAAO,KAAK,kBAAkBC,CAAK,EACzCD,EAAK,OAASE,CACf,CAEA,eAAeC,EAAsB,CACpC,KAAK,kBAAkB,OAAOA,CAAC,CAChC,CAEA,gBAAgBC,EAA+B,CAC9C,KAAK,kBAAkBA,CAAU,EAAE,YAAc,EAClD,CAEQ,cAAcJ,EAAsBK,EAA4D,CACvG,IAAMC,EAAaD,EAAM,IAAIL,CAAI,EACjC,GAAIM,EACH,OAAOA,EAGR,IAAMC,EAASP,EAAK,OAAS,KAAK,cAAc,KAAK,kBAAkBA,EAAK,MAAM,EAAGK,CAAK,EAAIL,EAC9F,OAAAK,EAAM,IAAIL,EAAMO,CAAM,EACfA,CACR,CAEA,uBAAuC,CACtC,IAAMC,EAAkB,IAAI,IAM5B,MAJgB,CAAC,GAAG,KAAK,kBAAkB,QAAQ,CAAC,EAClD,OAAO,CAAC,CAAC,CAAEC,CAAC,IAAMA,EAAE,SAAW,MAAQ,CAAC,KAAK,cAAcA,EAAGD,CAAe,EAAE,WAAW,EAC1F,QAAQ,CAAC,CAACE,CAAC,IAAMA,CAAC,CAGrB,CAEA,0BAA0BC,EAAc,GAAIC,EAA+F,CAC1I,IAAIC,EACJ,GAAID,EACHC,EAAuBD,MACjB,CACN,IAAMJ,EAAkB,IAAI,IAEtBM,EAAiB,CAAC,GAAG,KAAK,kBAAkB,OAAO,CAAC,EACxD,OAAQC,GAASA,EAAK,SAAW,MAAQ,CAAC,KAAK,cAAcA,EAAMP,CAAe,EAAE,WAAW,EAEjG,GAAIM,EAAe,SAAW,EAC7B,OAED,IAAME,EAAiB,IAAI,IAAIF,EAAe,IAAIG,GAAKA,EAAE,KAAK,CAAC,EAO/D,GAJAJ,EAAuBC,EAAe,OAAO,GACrC,EAAE,EAAE,QAAUE,EAAe,IAAI,EAAE,MAAM,EAChD,EAEGH,EAAqB,SAAW,EACnC,MAAM,IAAI,MAAM,oCAAoC,CAEtD,CAEA,GAAI,CAACA,EACJ,OAGD,SAASK,EAAkBC,EAAmC,CAC7D,SAASC,EAAaC,EAAiBC,EAAoC,CAC1E,KAAOD,EAAM,OAAS,GAAKC,EAAc,KAAKC,GAAU,OAAOA,GAAW,SAAWA,IAAWF,EAAM,CAAC,EAAIA,EAAM,CAAC,EAAE,MAAME,CAAM,CAAC,GAChIF,EAAM,MAAM,CAEd,CAEA,IAAMG,EAAQL,EAAQ,OAAQ,MAAM;AAAA,CAAI,EAAE,IAAIM,GAAKA,EAAE,KAAK,EAAE,QAAQ,MAAO,EAAE,CAAC,EAAE,OAAO,GAAK,IAAM,EAAE,EACpG,OAAAL,EAAaI,EAAO,CAAC,QAAS,2BAA4B,4CAA4C,CAAC,EAChGA,EAAM,QAAQ,CACtB,CAEA,IAAME,EAAmB,IAAIC,EAC7B,QAAWR,KAAWN,EAAsB,CAC3C,IAAMe,EAAiBV,EAAkBC,CAAO,EAChD,QAASU,EAAI,EAAGA,GAAKD,EAAe,OAAQC,IAC3CH,EAAiB,IAAIE,EAAe,MAAM,EAAGC,CAAC,EAAE,KAAK;AAAA,CAAI,EAAGV,CAAO,CAErE,CAGAN,EAAqB,KAAKiB,GAAUC,GAAKA,EAAE,IAAKC,EAAgB,CAAC,EAEjE,IAAIC,EAAU,GAEVJ,EAAI,EACR,QAAWV,KAAWN,EAAqB,MAAM,EAAGF,CAAW,EAAG,CACjEkB,IACA,IAAMD,EAAiBV,EAAkBC,CAAO,EAC1Ce,EAA2B,CAAC,EAElC,QAASL,EAAI,EAAGA,EAAID,EAAe,OAAQC,IAAK,CAC/C,IAAIM,EAAOP,EAAeC,CAAC,EAE3BM,EAAO,gBADQT,EAAiB,IAAIE,EAAe,MAAM,EAAGC,EAAI,CAAC,EAAE,KAAK;AAAA,CAAI,CAAC,EAC/C,IAAI,IAAIhB,EAAqB,MAAM,cAAcsB,CAAI,GAEnF,IAAMC,EAAaV,EAAiB,IAAIE,EAAe,MAAM,EAAGC,CAAC,EAAE,KAAK;AAAA,CAAI,CAAC,EACvEQ,EAAgBC,GAAQ,CAAC,GAAGF,CAAU,EAAE,IAAItC,GAAKoB,EAAkBpB,CAAC,EAAE+B,CAAC,CAAC,EAAGpB,GAAKA,CAAC,EACvF,OAAO4B,EAAcT,EAAeC,CAAC,CAAC,EACtC,OAAW,CAACU,EAAMC,CAAG,IAAK,OAAO,QAAQH,CAAa,EACrDH,EAAyB,QAAQ,wBAAwBM,EAAI,MAAM,8BAA8BD,CAAI,EAAE,EAGxGL,EAAyB,QAAQC,CAAI,CACtC,CAEAF,GAAW;AAAA;AAAA;AAAA,0CAAiDJ,CAAC,IAAIhB,EAAqB,MAAM,KAAKM,EAAQ,MAAM,YAAY,IAAI;AAAA,EAA0Be,EAAyB,KAAK;AAAA,CAAI,CAAC;AAAA;AAAA;AAAA,CAC7L,CAEA,OAAIrB,EAAqB,OAASF,IACjCsB,GAAW;AAAA;AAAA;AAAA,UAAiBpB,EAAqB,OAASF,CAAW;AAAA;AAAA,GAG/D,CAAE,MAAOE,EAAsB,QAASoB,CAAQ,CACxD,CACD,EA5IapC,EACG,IAAM,EADf,IAAM4C,GAAN5C,EA8IA,SAAS6C,GAAqBC,EAA0C,CAC9E/C,EAAoB+C,CACrB,CAEA,GAAIhD,GAAmB,CACtB,IAAMiD,EAA4B,4BAClCF,GAAqB,IAAI,KAAoC,CAC5D,gBAAgBvC,EAAsB,CACrC,IAAM0C,EAAQ,IAAI,MAAM,+BAA+B,EAAE,MACzD,WAAW,IAAM,CACV1C,EAAUyC,CAAyB,GACxC,QAAQ,IAAIC,CAAK,CAEnB,EAAG,GAAI,CACR,CAEA,UAAU5C,EAAoBC,EAAkC,CAC/D,GAAID,GAASA,IAAU6C,EAAW,KACjC,GAAI,CACF7C,EAAc2C,CAAyB,EAAI,EAC7C,MAAQ,CAER,CAEF,CAEA,eAAexC,EAA+B,CAC7C,GAAIA,GAAcA,IAAe0C,EAAW,KAC3C,GAAI,CACF1C,EAAmBwC,CAAyB,EAAI,EAClD,MAAQ,CAER,CAEF,CACA,gBAAgBxC,EAA+B,CAAE,CAClD,CAAC,CACF,CAEO,SAAS2C,GAAuC5C,EAAS,CAC/D,OAAAP,GAAmB,gBAAgBO,CAAC,EAC7BA,CACR,CAEO,SAAS6C,GAAe5C,EAA+B,CAC7DR,GAAmB,eAAeQ,CAAU,CAC7C,CAEA,SAAS6C,EAAsBhD,EAAoBC,EAAkC,CACpFN,GAAmB,UAAUK,EAAOC,CAAM,CAC3C,CAiDO,SAASgD,GAA+BC,EAAuC,CACrF,GAAIC,GAAS,GAAGD,CAAG,EAAG,CACrB,IAAME,EAAgB,CAAC,EAEvB,QAAWC,KAAKH,EACf,GAAIG,EACH,GAAI,CACHA,EAAE,QAAQ,CACX,OAASC,EAAG,CACXF,EAAO,KAAKE,CAAC,CACd,CAIF,GAAIF,EAAO,SAAW,EACrB,MAAMA,EAAO,CAAC,EACR,GAAIA,EAAO,OAAS,EAC1B,MAAM,IAAI,eAAeA,EAAQ,6CAA6C,EAG/E,OAAO,MAAM,QAAQF,CAAG,EAAI,CAAC,EAAIA,CAClC,SAAWA,EACV,OAAAA,EAAI,QAAQ,EACLA,CAET,CAyBO,SAASK,GAAaC,EAA6B,CACzD,IAAMC,EAAOC,GAAgB,CAC5B,QAASC,GAAyB,IAAM,CACvCC,GAAeH,CAAI,EACnBD,EAAG,CACJ,CAAC,CACF,CAAC,EACD,OAAOC,CACR,CASO,IAAMI,EAAN,MAAMA,CAAuC,CAOnD,aAAc,CAHd,KAAiB,WAAa,IAAI,IAClC,KAAQ,YAAc,GAGrBH,GAAgB,IAAI,CACrB,CAOO,SAAgB,CAClB,KAAK,cAITE,GAAe,IAAI,EACnB,KAAK,YAAc,GACnB,KAAK,MAAM,EACZ,CAKA,IAAW,YAAsB,CAChC,OAAO,KAAK,WACb,CAKO,OAAc,CACpB,GAAI,KAAK,WAAW,OAAS,EAI7B,GAAI,CACHE,GAAQ,KAAK,UAAU,CACxB,QAAE,CACD,KAAK,WAAW,MAAM,CACvB,CACD,CAKO,IAA2BC,EAAS,CAC1C,GAAI,CAACA,EACJ,OAAOA,EAER,GAAKA,IAAqC,KACzC,MAAM,IAAI,MAAM,yCAAyC,EAG1D,OAAAC,EAAsBD,EAAG,IAAI,EACzB,KAAK,YACHF,EAAgB,0BACpB,QAAQ,KAAK,IAAI,MAAM,qHAAqH,EAAE,KAAK,EAGpJ,KAAK,WAAW,IAAIE,CAAC,EAGfA,CACR,CAMO,OAA8BA,EAAY,CAChD,GAAKA,EAGL,IAAKA,IAAqC,KACzC,MAAM,IAAI,MAAM,wCAAwC,EAEzD,KAAK,WAAW,OAAOA,CAAC,EACxBA,EAAE,QAAQ,EACX,CAKO,cAAqCA,EAAY,CAClDA,GAGD,KAAK,WAAW,IAAIA,CAAC,IACxB,KAAK,WAAW,OAAOA,CAAC,EACxBC,EAAsBD,EAAG,IAAI,EAE/B,CACD,EAlGaF,EAEL,yBAA2B,GAF5B,IAAMI,GAANJ,EAyGeK,EAAf,KAAiD,CAWvD,aAAc,CAFd,KAAmB,OAAS,IAAID,GAG/BP,GAAgB,IAAI,EACpBM,EAAsB,KAAK,OAAQ,IAAI,CACxC,CAEO,SAAgB,CACtBJ,GAAe,IAAI,EAEnB,KAAK,OAAO,QAAQ,CACrB,CAKU,UAAiCG,EAAS,CACnD,GAAKA,IAAgC,KACpC,MAAM,IAAI,MAAM,yCAAyC,EAE1D,OAAO,KAAK,OAAO,IAAIA,CAAC,CACzB,CACD,EA/BsBG,EAOL,KAAO,OAAO,OAAoB,CAAE,SAAU,CAAE,CAAE,CAAC,EAgC7D,IAAMC,EAAN,KAAsE,CAI5E,aAAc,CAFd,KAAQ,YAAc,GAGrBT,GAAgB,IAAI,CACrB,CAEA,IAAI,OAAuB,CAC1B,OAAO,KAAK,YAAc,OAAY,KAAK,MAC5C,CAEA,IAAI,MAAMU,EAAsB,CAC3B,KAAK,aAAeA,IAAU,KAAK,SAIvC,KAAK,QAAQ,QAAQ,EACjBA,GACHJ,EAAsBI,EAAO,IAAI,EAElC,KAAK,OAASA,EACf,CAKA,OAAc,CACb,KAAK,MAAQ,MACd,CAEA,SAAgB,CACf,KAAK,YAAc,GACnBR,GAAe,IAAI,EACnB,KAAK,QAAQ,QAAQ,EACrB,KAAK,OAAS,MACf,CAMA,cAA8B,CAC7B,IAAMS,EAAW,KAAK,OACtB,YAAK,OAAS,OACVA,GACHL,EAAsBK,EAAU,IAAI,EAE9BA,CACR,CACD,ERliBA,IAAMC,GAAqB,KACrBC,GAAqB,GAQdC,EAAN,MAAMC,UAAsBC,CAAkC,CAkDnE,YAAoBC,EAAyB,CAC3C,MAAM,EADY,eAAAA,EA7CpB,KAAQ,gBAAkB,KAAK,UAAU,IAAIC,CAAmB,EA+C9D,KAAK,SAAW,KAAK,UAAU,MAAM,KACrC,KAAK,UAAU,MAAM,KAAQC,GAA8B,CACzD,KAAK,UAAU,KAAK,KAAK,UAAU,MAAOA,CAAM,EAChD,KAAK,MAAM,CACb,EACI,KAAK,UAAU,MAAM,eACvB,KAAK,MAAM,EAGb,KAAK,gBAAgB,MAAQ,KAAK,UAAU,MAAM,eAAe,eAAeC,GAAU,CACpFA,IAAW,aACb,KAAK,cAAc,EACnB,KAAK,gBAAgB,YAAY,EAAG,KAAK,UAAU,IAAI,EAE3D,CAAC,EACD,KAAK,UAAUC,GAAa,IAAM,CAChC,KAAK,mBAAmB,EACpB,KAAK,UAAU,OAAS,KAAK,WAC/B,KAAK,UAAU,MAAM,KAAO,KAAK,SACjC,KAAK,SAAW,QAEd,KAAK,gBAAkB,KAAK,kBAC9B,KAAK,eAAe,YAAc,KAAK,gBACvC,KAAK,gBAAkB,QAEzB,KAAK,eAAiB,OACtB,KAAK,OAAS,OACd,KAAK,KAAO,OACZ,KAAK,oBAAoB,MAAM,EAC/B,KAAK,mBAAqB,OAC1B,KAAK,aAAe,MACtB,CAAC,CAAC,CACJ,CAzEA,OAAc,aAAaC,EAAqCC,EAAeC,EAAmC,CAUhH,IAAMC,GAAUH,GAAiB,UAAU,cAAc,QAAQ,EACjE,OAAAG,EAAO,MAAQF,EAAQ,EACvBE,EAAO,OAASD,EAAS,EAClBC,CACT,CAGA,OAAc,gBAAgBC,EAA+BH,EAAeC,EAAgBG,EAAiC,CAC3H,GAAI,OAAO,WAAc,WAAY,CACnC,IAAMC,EAAUF,EAAI,gBAAgBH,EAAOC,CAAM,EACjD,OAAIG,GACFC,EAAQ,KAAK,IAAI,IAAI,kBAAkBD,EAAQ,EAAGJ,EAAQC,EAAS,CAAC,CAAC,EAEhEI,CACT,CACA,OAAOD,EACH,IAAI,UAAU,IAAI,kBAAkBA,EAAQ,EAAGJ,EAAQC,EAAS,CAAC,EAAGD,EAAOC,CAAM,EACjF,IAAI,UAAUD,EAAOC,CAAM,CACjC,CAGA,OAAc,kBAAkBK,EAA0D,CACxF,OAAI,OAAO,mBAAsB,WACxB,QAAQ,QAAQ,MAAS,EAE3B,kBAAkBA,CAAG,CAC9B,CA0CO,gBAAgBC,EAAsB,CACvCA,EACE,CAAC,KAAK,cAAgB,KAAK,SAAS,SAAW,IACjD,KAAK,mBAAmB,KAAK,IAAI,KAAK,SAAS,OAAS,EAAGjB,EAAkB,CAAC,GAGhF,KAAK,oBAAoB,MAAM,EAC/B,KAAK,mBAAqB,OAC1B,KAAK,aAAe,QAEtB,KAAK,gBAAgB,YAAY,EAAG,KAAK,UAAU,IAAI,CACzD,CAMA,IAAW,YAA4C,CACrD,OAAO,KAAK,gBAAgB,UAC9B,CAKA,IAAW,UAAsB,CAC/B,MAAO,CACL,MAAO,KAAK,YAAY,IAAI,KAAK,OAAS,GAC1C,OAAQ,KAAK,YAAY,IAAI,KAAK,QAAU,EAC9C,CACF,CAKO,WAAWkB,EAAeC,EAAmB,CAClD,KAAK,MAAM,UACT,EACAD,GAAS,KAAK,YAAY,IAAI,KAAK,QAAU,GAC7C,KAAK,YAAY,IAAI,OAAO,OAAS,GACpC,EAAEC,EAAMD,IAAU,KAAK,YAAY,IAAI,KAAK,QAAU,EACzD,CACF,CAKO,UAAiB,CACtB,KAAK,MAAM,UAAU,EAAG,EAAG,KAAK,QAAQ,OAAS,EAAG,KAAK,QAAQ,QAAU,CAAC,CAC9E,CAKO,KAAKE,EAAqBC,EAAgBC,EAAaC,EAAaC,EAAgB,EAAS,CAClG,GAAI,CAAC,KAAK,KACR,OAEF,GAAM,CAAE,MAAAd,EAAO,OAAAC,CAAO,EAAI,KAAK,SAG/B,GAAID,IAAU,IAAMC,IAAW,GAC7B,OAGF,KAAK,cAAcS,EAASV,EAAOC,CAAM,EACzC,IAAMK,EAAMI,EAAQ,OACdK,EAAO,KAAK,KAAKT,EAAI,MAAQN,CAAK,EAElCgB,EAAML,EAASI,EAAQf,EACvBiB,EAAK,KAAK,MAAMN,EAASI,CAAI,EAAId,EACjCiB,EAAKN,EAAMZ,EACXmB,EAAKN,EAAMZ,EAGXmB,EAAaN,EAAQd,EAAQgB,EAAKV,EAAI,MAAQA,EAAI,MAAQU,EAAKF,EAAQd,EACvEqB,EAAcJ,EAAKhB,EAASK,EAAI,OAASA,EAAI,OAASW,EAAKhB,EAMjE,KAAK,KAAK,UACRK,EACA,KAAK,MAAMU,CAAE,EAAG,KAAK,MAAMC,CAAE,EAAG,KAAK,KAAKG,CAAU,EAAG,KAAK,KAAKC,CAAW,EAC5E,KAAK,MAAMH,CAAE,EAAG,KAAK,MAAMC,CAAE,EAAG,KAAK,KAAKC,CAAU,EAAG,KAAK,KAAKC,CAAW,CAC9E,CACF,CAKO,YAAYX,EAAqBC,EAA+C,CACrF,GAAM,CAAE,MAAAX,EAAO,OAAAC,CAAO,EAAI,KAAK,SAE/B,GAAID,IAAU,IAAMC,IAAW,GAC7B,OAEF,KAAK,cAAcS,EAASV,EAAOC,CAAM,EACzC,IAAMK,EAAMI,EAAQ,OACdK,EAAO,KAAK,KAAKT,EAAI,MAAQN,CAAK,EAClCgB,EAAML,EAASI,EAAQf,EACvBiB,EAAK,KAAK,MAAMN,EAASI,CAAI,EAAId,EACjCmB,EAAapB,EAAQgB,EAAKV,EAAI,MAAQA,EAAI,MAAQU,EAAKhB,EACvDqB,EAAcJ,EAAKhB,EAASK,EAAI,OAASA,EAAI,OAASW,EAAKhB,EAE3DC,EAASV,EAAc,aAAa,KAAK,SAAU4B,EAAYC,CAAW,EAC1ElB,EAAMD,EAAO,WAAW,IAAI,EAClC,GAAIC,EACF,OAAAA,EAAI,UACFG,EACA,KAAK,MAAMU,CAAE,EAAG,KAAK,MAAMC,CAAE,EAAG,KAAK,MAAMG,CAAU,EAAG,KAAK,MAAMC,CAAW,EAC9E,EAAG,EAAG,KAAK,MAAMD,CAAU,EAAG,KAAK,MAAMC,CAAW,CACtD,EACOnB,CAEX,CAKO,gBAAgBU,EAAaC,EAAaC,EAAgB,EAAS,CACxE,GAAI,KAAK,KAAM,CACb,GAAM,CAAE,MAAAd,EAAO,OAAAC,CAAO,EAAI,KAAK,SAY/B,GATID,IAAU,IAAMC,IAAW,KAI1B,KAAK,aAECA,GAAU,KAAK,aAAc,QACtC,KAAK,mBAAmBA,EAAS,CAAC,EAFlC,KAAK,mBAAmB,KAAK,IAAIA,EAAS,EAAGX,EAAkB,CAAC,EAI9D,CAAC,KAAK,cAAc,OACxB,KAAK,KAAK,UACR,KAAK,oBAAsB,KAAK,aAChCsB,EAAMZ,EACLa,EAAMZ,EAAU,EAAI,EAAI,EACzBD,EAAQc,EACRb,EACAW,EAAMZ,EACNa,EAAMZ,EACND,EAAQc,EACRb,CACF,CACF,CACF,CAMO,eAAsB,CACtB,KAAK,SAGN,KAAK,OAAO,QAAU,KAAK,WAAY,IAAI,OAAO,OAAS,KAAK,OAAO,SAAW,KAAK,WAAY,IAAI,OAAO,UAChH,KAAK,OAAO,MAAQ,KAAK,WAAY,IAAI,OAAO,OAAS,EACzD,KAAK,OAAO,OAAS,KAAK,WAAY,IAAI,OAAO,QAAU,EAE/D,CAKQ,cAAcqB,EAAkBC,EAAsBC,EAA6B,CACzF,GAAID,IAAiBD,EAAK,eAAe,OAASE,IAAkBF,EAAK,eAAe,OACtF,OAEF,GAAM,CAAE,MAAOG,EAAe,OAAQC,CAAe,EAAIJ,EAAK,aAC9D,GAAIC,IAAiBE,GAAiBD,IAAkBE,EAAgB,CACtEJ,EAAK,OAASA,EAAK,KACnBA,EAAK,eAAe,MAAQG,EAC5BH,EAAK,eAAe,OAASI,EAC7B,MACF,CACA,IAAMxB,EAASV,EAAc,aAC3B,KAAK,SACL,KAAK,KAAK8B,EAAK,KAAM,MAAQC,EAAeE,CAAa,EACzD,KAAK,KAAKH,EAAK,KAAM,OAASE,EAAgBE,CAAc,CAC9D,EACMvB,EAAMD,EAAO,WAAW,IAAI,EAC9BC,IACFA,EAAI,UAAUmB,EAAK,KAAO,EAAG,EAAGpB,EAAO,MAAOA,EAAO,MAAM,EAC3DoB,EAAK,OAASpB,EACdoB,EAAK,eAAe,MAAQC,EAC5BD,EAAK,eAAe,OAASE,EAEjC,CAKQ,OAAc,CACpB,KAAK,eAAiB,KAAK,UAAU,MAAM,eAC3C,KAAK,gBAAkB,KAAK,eAAe,YAAY,KAAK,KAAK,cAAc,EAC/E,KAAK,eAAe,YAAeG,GAAkB,CACnD,KAAK,mBAAmB,EACxB,KAAK,iBAAiB,KAAK,KAAK,eAAgBA,CAAQ,CAC1D,CACF,CAEO,kBAAyB,CAE1B,KAAK,UAAY,KAAK,UAAU,MAAM,cACnC,KAAK,SACR,KAAK,OAASnC,EAAc,aAC1B,KAAK,SAAU,KAAK,YAAY,IAAI,OAAO,OAAS,EACpD,KAAK,YAAY,IAAI,OAAO,QAAU,CACxC,EACA,KAAK,OAAO,UAAU,IAAI,mBAAmB,EAC7C,KAAK,UAAU,MAAM,cAAc,YAAY,KAAK,MAAM,EAC1D,KAAK,KAAO,KAAK,OAAO,WAAW,KAAM,CAAE,MAAO,GAAM,eAAgB,EAAK,CAAC,EAC9E,KAAK,SAAS,GAGhB,QAAQ,KAAK,oFAAoF,CAErG,CAEO,oBAA2B,CAC5B,KAAK,SACP,KAAK,KAAO,OACZ,KAAK,OAAO,OAAO,EACnB,KAAK,OAAS,OAElB,CAEQ,mBAAmBS,EAAiBX,GAA0B,CACpE,KAAK,oBAAoB,MAAM,EAC/B,KAAK,mBAAqB,OAG1B,IAAMsC,EAAS,GACTC,EAAYrC,EAAc,aAAa,KAAK,SAAUoC,EAAQ3B,CAAM,EACpEE,EAAM0B,EAAU,WAAW,KAAM,CAAE,MAAO,EAAM,CAAC,EACvD,GAAI,CAAC1B,EAAK,OACV,IAAME,EAAUb,EAAc,gBAAgBW,EAAKyB,EAAQ3B,CAAM,EAC3D6B,EAAM,IAAI,YAAYzB,EAAQ,KAAK,MAAM,EACzC0B,KAAQ,eAAW,EAAG,EAAG,CAAC,EAC1BC,KAAQ,eAAW,IAAK,IAAK,GAAG,EACtCF,EAAI,KAAKC,CAAK,EACd,QAASE,EAAI,EAAGA,EAAIhC,EAAQ,EAAEgC,EAAG,CAC/B,IAAMC,EAAQD,EAAI,EACZE,EAASF,EAAIL,EACnB,QAASQ,EAAI,EAAGA,EAAIR,EAAQQ,GAAK,EAC/BN,EAAIK,EAASC,EAAIF,CAAK,EAAIF,CAE9B,CACA7B,EAAI,aAAaE,EAAS,EAAG,CAAC,EAG9B,IAAML,EAAS,OAAO,MAAQ4B,EAAS,EAAK,EAAEA,EAAS,IAAMvC,GAC7D,KAAK,aAAeG,EAAc,aAAa,KAAK,SAAUQ,EAAOC,CAAM,EAC3E,IAAMoC,EAAO,KAAK,aAAa,WAAW,KAAM,CAAE,MAAO,EAAM,CAAC,EAChE,GAAI,CAACA,EAAM,CACT,KAAK,aAAe,OACpB,MACF,CACA,QAASC,EAAI,EAAGA,EAAItC,EAAOsC,GAAKV,EAC9BS,EAAK,UAAUR,EAAWS,EAAG,CAAC,EAEhC9C,EAAc,kBAAkB,KAAK,YAAY,EAAE,KAAK+C,GAAU,KAAK,mBAAqBA,CAAM,CACpG,CAEA,IAAW,UAAiC,CAC1C,OAAO,KAAK,UAAU,MAAM,qBAAqB,OAAO,QAC1D,CACF,ES9WO,IAAMC,EAA+B,CAC1C,MAAO,EACP,OAAQ,EACV,EAQMC,EAAN,MAAMC,CAAkD,CAqDtD,YACEC,EAAc,EACdC,EAAgB,EACTC,EAAU,GACVC,EAAS,GAChB,CAFO,aAAAD,EACA,YAAAC,EAxDT,KAAQ,KAAe,EA4CvB,KAAQ,OAAiB,EAcvB,KAAK,KAAOH,EACZ,KAAK,OAASC,CAChB,CA3DA,IAAW,KAAc,CACvB,OAAI,KAAK,OAEJ,KAAK,KAAO,WACZ,KAAK,gBAAkB,GAGrB,KAAK,IACd,CACA,IAAW,IAAIG,EAAe,CAAE,KAAK,KAAOA,CAAO,CAEnD,IAAW,gBAAiC,CAE1C,OAAI,KAAK,UAGD,KAAK,KAAO,YAA6B,EACnD,CACA,IAAW,eAAeA,EAAuB,CAC/C,KAAK,MAAQ,WACb,KAAK,MAASA,GAAS,GAAM,SAC/B,CAEA,IAAW,gBAAyB,CAClC,OAAO,KAAK,KAAQ,QACtB,CACA,IAAW,eAAeA,EAAe,CACvC,KAAK,MAAQ,UACb,KAAK,MAAQA,EAAS,QACxB,CAEA,IAAW,wBAAiC,CAC1C,IAAMC,GAAO,KAAK,KAAO,aAA4B,GACrD,OAAIA,EAAM,EACDA,EAAM,WAERA,CACT,CACA,IAAW,uBAAuBD,EAAe,CAC/C,KAAK,MAAQ,UACb,KAAK,MAASA,GAAS,GAAM,UAC/B,CAGA,IAAW,OAAgB,CACzB,OAAO,KAAK,MACd,CACA,IAAW,MAAMA,EAAe,CAC9B,KAAK,OAASA,CAChB,CAYO,OAA6B,CASlC,OAAO,IAAIL,EAAmB,KAAK,KAAM,KAAK,OAAQ,KAAK,QAAS,KAAK,MAAM,CACjF,CAEO,SAAmB,CACxB,OAAO,KAAK,iBAAmB,GAAuB,KAAK,SAAW,GAAK,KAAK,UAAY,EAC9F,CACF,EACMO,EAAc,IAAIR,EAUXS,GAAN,KAA0C,CAgB/C,YACUC,EACAC,EACAC,EACR,CAHQ,eAAAF,EACA,eAAAC,EACA,WAAAC,EAjBV,KAAQ,QAAmC,IAAI,IAE/C,KAAQ,QAAU,EAElB,KAAQ,UAAY,EAEpB,KAAQ,cAAgB,GAExB,KAAQ,gBAAkB,GAE1B,KAAQ,YAAsB,KAS5B,GAAI,CACF,KAAK,SAAS,KAAK,MAAM,YAAY,CACvC,OAASC,EAAQ,CACf,QAAQ,MAAMA,EAAE,OAAO,EACvB,QAAQ,KAAK,0BAA0B,KAAK,SAAS,CAAC,KAAK,CAC7D,CACA,KAAK,iBAAmB,CACtB,KAAM,KAAK,UAAU,KACrB,KAAM,KAAK,UAAU,IACvB,CACF,CAEO,SAAgB,CACrB,KAAK,MAAM,CACb,CAEO,OAAc,CACnB,QAAWC,KAAQ,KAAK,QAAQ,OAAO,EACrCA,EAAK,QAAQ,QAAQ,EAIvB,KAAK,QAAQ,MAAM,EACnB,KAAK,UAAU,SAAS,CAC1B,CAEO,UAAmB,CACxB,OAAO,KAAK,YAAc,EAAI,GAChC,CAEO,SAASR,EAAqB,CACnC,GAAIA,EAAQ,IAAOA,EAAQ,IACzB,MAAM,WAAW,mEAAmE,EAEtF,KAAK,YAAeA,EAAQ,EAAI,MAAa,EAC7C,KAAK,aAAa,CAAC,CACrB,CAEO,UAAmB,CACxB,OAAO,KAAK,iBAAiB,EAAI,EAAI,GACvC,CAEQ,kBAA2B,CACjC,IAAIS,EAAe,EACnB,QAAWD,KAAQ,KAAK,QAAQ,OAAO,EACjCA,EAAK,OACPC,GAAgBD,EAAK,KAAK,MAAQA,EAAK,KAAK,OACxCA,EAAK,QAAUA,EAAK,SAAWA,EAAK,OACtCC,GAAgBD,EAAK,OAAO,MAAQA,EAAK,OAAO,SAItD,OAAOC,CACT,CAEQ,QAAQC,EAAkB,CAChC,IAAMF,EAAO,KAAK,QAAQ,IAAIE,CAAE,EAChC,KAAK,QAAQ,OAAOA,CAAE,EAElBF,GAAQ,OAAO,aAAeA,EAAK,gBAAgB,aACrDA,EAAK,KAAK,MAAM,CAEpB,CAKO,eAAsB,CAE3B,IAAMG,EAAO,CAAC,EACd,OAAW,CAACD,EAAIF,CAAI,IAAK,KAAK,QAAQ,QAAQ,EACxCA,EAAK,aAAe,cACtBA,EAAK,QAAQ,QAAQ,EACrBG,EAAK,KAAKD,CAAE,GAGhB,QAAWA,KAAMC,EACf,KAAK,QAAQD,CAAE,EAGjB,KAAK,gBAAkB,GACvB,KAAK,cAAgB,EACvB,CAOO,cAAcE,EAAsB,CACzC,GAAI,KAAK,MAAM,eAAgB,CAC7B,IAAIC,EAAW,KAAK,UAAU,UAC1BA,EAAS,QAAU,IAAMA,EAAS,SAAW,MAC/CA,EAAWpB,GAEb,IAAMqB,EAAO,KAAK,KAAKF,EAASC,EAAS,MAAM,EAC/C,QAASE,EAAI,EAAGA,EAAID,EAAM,EAAEC,EAC1B,KAAK,UAAU,MAAM,cAAc,SAAS,CAEhD,CACF,CAKO,SAASC,EAA4C,CAE1D,KAAK,aAAaA,EAAI,MAAQA,EAAI,MAAM,EAGxC,IAAIH,EAAW,KAAK,UAAU,UAC1BA,EAAS,QAAU,IAAMA,EAAS,SAAW,MAC/CA,EAAWpB,GAEb,IAAMwB,EAAO,KAAK,KAAKD,EAAI,MAAQH,EAAS,KAAK,EAC3CC,EAAO,KAAK,KAAKE,EAAI,OAASH,EAAS,MAAM,EAE7Cf,EAAU,EAAE,KAAK,QAEjBoB,EAAS,KAAK,UAAU,MAAM,OAC9BC,EAAW,KAAK,UAAU,KAC1BC,EAAW,KAAK,UAAU,KAC1BC,EAAUH,EAAO,EACjBI,EAAUJ,EAAO,EACnBK,EAASF,EACTG,EAAY,EAEX,KAAK,MAAM,iBACdN,EAAO,EAAI,EACXA,EAAO,EAAI,EACXK,EAAS,GAGX,KAAK,UAAU,MAAM,cAAc,iBAAiB,UAAUL,EAAO,CAAC,EACtE,QAASO,EAAM,EAAGA,EAAMX,EAAM,EAAEW,EAAK,CACnC,IAAMC,EAAOR,EAAO,MAAM,IAAIA,EAAO,EAAIA,EAAO,KAAK,EACrD,QAASS,EAAM,EAAGA,EAAMV,GAClB,EAAAM,EAASI,GAAOR,GADQ,EAAEQ,EAE9B,KAAK,aAAaD,EAAwBH,EAASI,EAAK7B,EAAS2B,EAAMR,EAAOU,CAAG,EACjFH,IAEF,GAAI,KAAK,MAAM,eACTC,EAAMX,EAAO,GAAG,KAAK,UAAU,MAAM,cAAc,SAAS,UAE5D,EAAEI,EAAO,GAAKE,EAAU,MAE9BF,EAAO,EAAIK,CACb,CACA,KAAK,UAAU,MAAM,cAAc,iBAAiB,UAAUL,EAAO,CAAC,EAGlE,KAAK,MAAM,eACbA,EAAO,EAAIK,GAEXL,EAAO,EAAIG,EACXH,EAAO,EAAII,GAIb,IAAMX,EAAO,CAAC,EACd,OAAW,CAACD,EAAIF,CAAI,IAAK,KAAK,QAAQ,QAAQ,EACxCA,EAAK,UAAY,IACnBA,EAAK,QAAQ,QAAQ,EACrBG,EAAK,KAAKD,CAAE,GAGhB,QAAWA,KAAMC,EACf,KAAK,QAAQD,CAAE,EAKjB,IAAMkB,EAAY,KAAK,UAAU,eAAe,CAAC,EACjDA,GAAW,UAAU,IAAM,CACZ,KAAK,QAAQ,IAAI9B,CAAO,GAEnC,KAAK,QAAQA,CAAO,CAExB,CAAC,EAIG,KAAK,UAAU,OAAO,OAAO,OAAS,aACxC,KAAK,kBAAkB,EAIzB,IAAM+B,EAAsB,CAC1B,KAAMb,EACN,aAAcH,EACd,OAAQG,EACR,eAAgB,CAAE,GAAGH,CAAS,EAC9B,OAAQe,GAAa,OACrB,UAAAJ,EACA,WAAY,KAAK,UAAU,OAAO,OAAO,IAC3C,EAGA,KAAK,QAAQ,IAAI1B,EAAS+B,CAAO,CACnC,CAQO,OAAOC,EAA6C,CAEzD,GAAI,CAAC,KAAK,UAAU,QAAU,KAAK,QAAQ,OACzC,KAAK,UAAU,iBAAiB,EAE5B,CAAC,KAAK,UAAU,QAClB,OAMJ,GAFA,KAAK,UAAU,cAAc,EAEzB,CAAC,KAAK,QAAQ,KAAM,CACjB,KAAK,gBACR,KAAK,UAAU,SAAS,EACxB,KAAK,cAAgB,GACrB,KAAK,gBAAkB,IAErB,KAAK,UAAU,QACjB,KAAK,UAAU,mBAAmB,EAEpC,MACF,CAGI,KAAK,kBACP,KAAK,UAAU,SAAS,EACxB,KAAK,cAAgB,GACrB,KAAK,gBAAkB,IAGzB,GAAM,CAAE,MAAAC,EAAO,IAAAC,CAAI,EAAIF,EACjBZ,EAAS,KAAK,UAAU,MAAM,OAC9BD,EAAO,KAAK,UAAU,MAAM,KAGlC,KAAK,UAAU,WAAWc,EAAOC,CAAG,EAGpC,QAASP,EAAMM,EAAON,GAAOO,EAAK,EAAEP,EAAK,CACvC,IAAMC,EAAOR,EAAO,MAAM,IAAIO,EAAMP,EAAO,KAAK,EAChD,GAAI,CAACQ,EAAM,OACX,QAASC,EAAM,EAAGA,EAAMV,EAAM,EAAEU,EAC9B,GAAID,EAAK,MAAMC,CAAG,EAAI,UAAsB,CAC1C,IAAIpB,EAAyBmB,EAAK,eAAeC,CAAG,GAAKzB,EACnDJ,EAAUS,EAAE,QAClB,GAAIT,IAAY,QAAaA,IAAY,GACvC,SAEF,IAAM+B,EAAU,KAAK,QAAQ,IAAI/B,CAAO,EACxC,GAAIS,EAAE,SAAW,GAAI,CACnB,IAAM0B,EAAY1B,EAAE,OACd2B,EAAWP,EACbQ,EAAQ,EAOZ,KACE,EAAER,EAAMV,GACJS,EAAK,MAAMC,CAAG,EAAI,YAClBpB,EAAImB,EAAK,eAAeC,CAAG,GAAKzB,IAChCK,EAAE,UAAYT,GACdS,EAAE,SAAW0B,EAAYE,GAE7BA,IAEFR,IACIE,EACEA,EAAQ,QACV,KAAK,UAAU,KAAKA,EAASI,EAAWC,EAAUT,EAAKU,CAAK,EAErD,KAAK,MAAM,iBACpB,KAAK,UAAU,gBAAgBD,EAAUT,EAAKU,CAAK,EAErD,KAAK,cAAgB,EACvB,CACF,CAEJ,CACF,CAEO,eAAeC,EAA+C,CAEnE,GAAI,CAAC,KAAK,QAAQ,KAAM,CACtB,KAAK,iBAAmBA,EACxB,MACF,CAIA,GAAI,KAAK,iBAAiB,MAAQA,EAAQ,KAAM,CAC9C,KAAK,iBAAmBA,EACxB,MACF,CAGA,IAAMlB,EAAS,KAAK,UAAU,MAAM,OAC9BJ,EAAOI,EAAO,MAAM,OACpBmB,EAAS,KAAK,iBAAiB,KAAO,EAC5C,QAASZ,EAAM,EAAGA,EAAMX,EAAM,EAAEW,EAAK,CACnC,IAAMC,EAAOR,EAAO,MAAM,IAAIO,CAAG,EACjC,GAAIC,EAAK,MAAMW,CAAM,EAAI,UAAsB,CAC7C,IAAM9B,EAAyBmB,EAAK,eAAeW,CAAM,GAAKnC,EACxDJ,EAAUS,EAAE,QAClB,GAAIT,IAAY,QAAaA,IAAY,GACvC,SAEF,IAAM+B,EAAU,KAAK,QAAQ,IAAI/B,CAAO,EACxC,GAAI,CAAC+B,EACH,SAGF,IAAMS,EAAc,KAAK,MAAMT,EAAQ,QAAQ,OAAS,GAAKA,EAAQ,eAAe,KAAK,EACzF,GAAKtB,EAAE,OAAS+B,EAAe,GAAKA,EAClC,SAGF,IAAIC,EAAU,GACd,QAASC,EAAWH,EAAS,EAAGG,EAAWJ,EAAQ,KAAM,EAAEI,EACzD,GAAId,EAAK,MAAMc,EAAW,EAAY,CAAY,EAAI,QAA0B,CAC9ED,EAAU,GACV,KACF,CAEF,GAAIA,EACF,SAGF,IAAMP,EAAM,KAAK,IAAII,EAAQ,KAAME,EAAe/B,EAAE,OAAS+B,EAAeD,CAAM,EAC9EI,EAAWlC,EAAE,OACjB,QAASmC,EAAYL,EAAS,EAAGK,EAAYV,EAAK,EAAEU,EAClD,KAAK,aAAahB,EAAwBgB,EAAW5C,EAAS,EAAE2C,CAAQ,EACxEZ,EAAQ,WAEZ,CACF,CAEA,KAAK,iBAAmBO,CAC1B,CAKO,qBAAqBO,EAAWC,EAA0C,CAE/E,IAAMlB,EADS,KAAK,UAAU,MAAM,OAChB,MAAM,IAAIkB,CAAC,EAC/B,GAAIlB,GAAQA,EAAK,MAAMiB,CAAC,EAAI,UAAsB,CAChD,IAAMpC,EAAyBmB,EAAK,eAAeiB,CAAC,GAAKzC,EACzD,GAAIK,EAAE,SAAWA,EAAE,UAAY,GAAI,CACjC,IAAMsC,EAAO,KAAK,QAAQ,IAAItC,EAAE,OAAO,GAAG,KAC1C,GAAI,OAAO,aAAesC,aAAgB,YAAa,CACrD,IAAMC,EAASC,EAAc,aAAa,OAAO,SAAUF,EAAK,MAAOA,EAAK,MAAM,EAClF,OAAAC,EAAO,WAAW,IAAI,GAAG,UAAUD,EAAM,EAAG,EAAGA,EAAK,MAAOA,EAAK,MAAM,EAC/DC,CACT,CACA,OAAOD,CACT,CACF,CACF,CAKO,wBAAwBF,EAAWC,EAA0C,CAElF,IAAMlB,EADS,KAAK,UAAU,MAAM,OAChB,MAAM,IAAIkB,CAAC,EAC/B,GAAIlB,GAAQA,EAAK,MAAMiB,CAAC,EAAI,UAAsB,CAChD,IAAMpC,EAAyBmB,EAAK,eAAeiB,CAAC,GAAKzC,EACzD,GAAIK,EAAE,SAAWA,EAAE,UAAY,IAAMA,EAAE,SAAW,GAAI,CACpD,IAAMC,EAAO,KAAK,QAAQ,IAAID,EAAE,OAAO,EACvC,GAAIC,EACF,OAAO,KAAK,UAAU,YAAYA,EAAMD,EAAE,MAAM,CAEpD,CACF,CACF,CAIQ,aAAayC,EAAsB,CACzC,IAAMC,EAAO,KAAK,iBAAiB,EAC/BC,EAAUD,EACd,KAAO,KAAK,YAAcC,EAAUF,GAAQ,KAAK,QAAQ,MAAM,CAC7D,IAAMxC,EAAO,KAAK,QAAQ,IAAI,EAAE,KAAK,SAAS,EAC1CA,GAAQA,EAAK,OACf0C,GAAW1C,EAAK,KAAK,MAAQA,EAAK,KAAK,OACnCA,EAAK,QAAUA,EAAK,OAASA,EAAK,SACpC0C,GAAW1C,EAAK,OAAO,MAAQA,EAAK,OAAO,QAE7CA,EAAK,QAAQ,QAAQ,EACrB,KAAK,QAAQ,KAAK,SAAS,EAE/B,CACA,OAAOyC,EAAOC,CAChB,CAEQ,aAAaxB,EAAsBiB,EAAW7C,EAAiBC,EAAsB,CAC3F,GAAI2B,EAAK,MAAMiB,EAAI,EAAY,CAAO,EAAI,UAAsB,CAC9D,IAAMQ,EAAMzB,EAAK,eAAeiB,CAAC,EACjC,GAAIQ,EAAK,CACP,GAAIA,EAAI,UAAY,OAAW,CAI7B,IAAMC,EAAU,KAAK,QAAQ,IAAID,EAAI,OAAO,EACxCC,GAEFA,EAAQ,YAEVD,EAAI,QAAUrD,EACdqD,EAAI,OAASpD,EACb,MACF,CAEA2B,EAAK,eAAeiB,CAAC,EAAI,IAAIjD,EAAmByD,EAAI,IAAKA,EAAI,MAAOrD,EAASC,CAAM,EACnF,MACF,CACF,CAEA2B,EAAK,MAAMiB,EAAI,EAAY,CAAO,GAAK,UACvCjB,EAAK,eAAeiB,CAAC,EAAI,IAAIjD,EAAmB,EAAG,EAAGI,EAASC,CAAM,CACvE,CAEQ,mBAA0B,CAEhC,QAAWS,KAAQ,KAAK,QAAQ,OAAO,EACjCA,EAAK,aAAe,cACtBA,EAAK,UAAY,GAIrB,IAAMU,EAAS,KAAK,UAAU,MAAM,OACpC,QAAS0B,EAAI,EAAGA,EAAI,KAAK,UAAU,KAAM,EAAEA,EAAG,CAC5C,IAAMlB,EAAOR,EAAO,MAAM,IAAI0B,CAAC,EAC/B,GAAKlB,GAGL,QAASiB,EAAI,EAAGA,EAAI,KAAK,UAAU,KAAM,EAAEA,EACzC,GAAIjB,EAAK,MAAMiB,EAAI,EAAY,CAAO,EAAI,UAAsB,CAC9D,IAAMU,EAAQ3B,EAAK,eAAeiB,CAAC,GAAG,QACtC,GAAIU,EAAO,CACT,IAAM7C,EAAO,KAAK,QAAQ,IAAI6C,CAAK,EAC/B7C,GACFA,EAAK,WAET,CACF,EAEJ,CAEA,IAAMG,EAAO,CAAC,EACd,OAAW,CAACD,EAAIF,CAAI,IAAK,KAAK,QAAQ,QAAQ,EACxCA,EAAK,aAAe,aAAe,CAACA,EAAK,YAC3CA,EAAK,QAAQ,QAAQ,EACrBG,EAAK,KAAKD,CAAE,GAGhB,QAAWA,KAAMC,EACf,KAAK,QAAQD,CAAE,CAEnB,CACF,ECplBA,IAAA4C,GAA0B,QCkC1B,SAASC,GAAMC,EAA2B,CACxC,IAAIC,EAAI,GACR,QAASC,EAAI,EAAGA,EAAIF,EAAK,OAAQ,EAAEE,EACjCD,GAAK,OAAO,aAAaD,EAAKE,CAAC,CAAC,EAElC,OAAOD,CACT,CAGA,SAASE,GAAMH,EAA2B,CACxC,IAAII,EAAI,EACR,QAASF,EAAI,EAAGA,EAAIF,EAAK,OAAQ,EAAEE,EAAG,CACpC,GAAIF,EAAKE,CAAC,EAAI,IAAMF,EAAKE,CAAC,EAAI,GAC5B,MAAM,IAAI,MAAM,cAAc,EAEhCE,EAAIA,EAAI,GAAKJ,EAAKE,CAAC,EAAI,EACzB,CACA,OAAOE,CACT,CAGA,SAASC,GAAOL,EAA2B,CACzC,IAAMI,EAAIL,GAAMC,CAAI,EACpB,GAAI,CAACI,EAAE,MAAM,kCAAkC,EAC7C,MAAM,IAAI,MAAM,cAAc,EAEhC,OAAOA,CACT,CAGA,SAASE,GAAON,EAA2B,CACzC,GAAI,OAAO,OAAW,IACpB,OAAO,OAAO,KAAKD,GAAMC,CAAI,EAAG,QAAQ,EAAE,SAAS,EAErD,IAAMO,EAAK,KAAKR,GAAMC,CAAI,CAAC,EACrBQ,EAAI,IAAI,WAAWD,EAAG,MAAM,EAClC,QAAS,EAAI,EAAG,EAAIC,EAAE,OAAQ,EAAE,EAC9BA,EAAE,CAAC,EAAID,EAAG,WAAW,CAAC,EAExB,OAAO,IAAI,YAAY,EAAE,OAAOC,CAAC,CACnC,CAEA,IAAMC,GAAqD,CACzD,OAAQN,GACR,KAAMA,GACN,KAAMG,GACN,MAAOD,GACP,OAAQA,GACR,oBAAqBF,EACvB,EAGMO,GAAc,CAAC,GAAI,IAAK,IAAK,GAAG,EAChCC,GAAiB,KAGVC,GAAN,KAAmB,CAAnB,cACL,KAAO,MAAqB,EAC5B,KAAQ,QAAU,IAAI,YAAYD,EAAc,EAChD,KAAQ,UAAY,EACpB,KAAQ,KAAO,GACf,KAAO,OAA+B,CAAC,EAEhC,OAAc,CACnB,KAAK,QAAQ,KAAK,CAAC,EACnB,KAAK,MAAQ,EACb,KAAK,UAAY,EACjB,KAAK,OAAS,CAAC,EACf,KAAK,KAAO,EACd,CAEO,MAAMX,EAAmBa,EAAeC,EAAqB,CAClE,IAAIC,EAAQ,KAAK,MACbC,EAAM,KAAK,UACTC,EAAS,KAAK,QAEpB,GADIF,IAAU,GAAqBA,IAAU,GACzCA,IAAU,GAAqBC,EAAM,EAAG,MAAO,GACnD,QAASd,EAAIW,EAAOX,EAAIY,EAAK,EAAEZ,EAAG,CAChC,IAAMgB,EAAIlB,EAAKE,CAAC,EAChB,OAAQgB,EAAG,CACT,IAAK,IACH,GAAI,CAAC,KAAK,YAAYF,CAAG,EAAG,OAAO,KAAK,GAAG,EAC3CD,EAAQ,EACRC,EAAM,EACN,MACF,IAAK,IACH,GAAID,IAAU,EAAmB,CAC/B,QAASI,EAAI,EAAGA,EAAIT,GAAY,OAAQ,EAAES,EACxC,GAAIF,EAAOE,CAAC,IAAMT,GAAYS,CAAC,EAAG,OAAO,KAAK,GAAG,EAEnDJ,EAAQ,EACRC,EAAM,CACR,SAAWD,IAAU,EAAiB,CACpC,GAAI,CAAC,KAAK,UAAUC,CAAG,EAAG,OAAO,KAAK,GAAG,EACzCD,EAAQ,EACRC,EAAM,CACR,SAAWD,IAAU,EAAmB,CACtC,GAAIC,GAAOL,GAAgB,OAAO,KAAK,GAAG,EAC1CM,EAAOD,GAAK,EAAIE,CAClB,CACA,MACF,IAAK,IACH,OAAIH,IAAU,GACR,CAAC,KAAK,YAAYC,CAAG,EAAU,KAAK,GAAG,GAE7C,KAAK,MAAQ,EACNd,EAAI,GACb,QACE,GAAIc,GAAOL,GAAgB,OAAO,KAAK,GAAG,EAC1CM,EAAOD,GAAK,EAAIE,CACpB,CACF,CACA,YAAK,MAAQH,EACb,KAAK,UAAYC,EACV,EACT,CAEQ,IAAa,CACnB,YAAK,MAAQ,EACN,EACT,CAEQ,UAAUA,EAAsB,CACtC,IAAMG,EAAIpB,GAAM,KAAK,QAAQ,SAAS,EAAGiB,CAAG,CAAC,EAC7C,OAAIG,GACF,KAAK,KAAOA,EACZ,KAAK,OAAOA,CAAC,EAAI,KACV,IAEF,EACT,CAEQ,YAAYH,EAAsB,CACxC,GAAI,KAAK,KAAM,CACb,GAAI,CACF,IAAMZ,EAAI,KAAK,QAAQ,MAAM,EAAGY,CAAG,EACnC,KAAK,OAAO,KAAK,IAAI,EAAIP,GAAS,KAAK,IAAI,EAAIA,GAAS,KAAK,IAAI,EAAEL,CAAC,EAAIA,CAC1E,MAAY,CACV,MAAO,EACT,CACA,MAAO,EACT,CACA,MAAO,EACT,CACF,EC3KO,IAAMgB,EAA6B,CACxC,KAAM,cACN,MAAO,EACP,OAAQ,CACV,EAEO,SAASC,GAAUC,EAAyB,CACjD,GAAIA,EAAE,OAAS,GACb,OAAOF,EAET,IAAMG,EAAM,IAAI,YAAYD,EAAE,OAAQA,EAAE,WAAY,CAAC,EAGrD,GAAIC,EAAI,CAAC,IAAM,YAAcA,EAAI,CAAC,IAAM,WAAcA,EAAI,CAAC,IAAM,WAC/D,MAAO,CACL,KAAM,YACN,MAAOD,EAAE,EAAE,GAAK,GAAKA,EAAE,EAAE,GAAK,GAAKA,EAAE,EAAE,GAAK,EAAIA,EAAE,EAAE,EACpD,OAAQA,EAAE,EAAE,GAAK,GAAKA,EAAE,EAAE,GAAK,GAAKA,EAAE,EAAE,GAAK,EAAIA,EAAE,EAAE,CACvD,EAGF,GAAIA,EAAE,CAAC,IAAM,KAAQA,EAAE,CAAC,IAAM,KAAQA,EAAE,CAAC,IAAM,IAAM,CACnD,GAAM,CAACE,EAAOC,CAAM,EAAIC,GAAQJ,CAAC,EACjC,MAAO,CAAE,KAAM,aAAc,MAAAE,EAAO,OAAAC,CAAO,CAC7C,CAEA,OAAIF,EAAI,CAAC,IAAM,YAAeD,EAAE,CAAC,IAAM,IAAQA,EAAE,CAAC,IAAM,KAASA,EAAE,CAAC,IAAM,GACjE,CACL,KAAM,YACN,MAAOA,EAAE,CAAC,GAAK,EAAIA,EAAE,CAAC,EACtB,OAAQA,EAAE,CAAC,GAAK,EAAIA,EAAE,CAAC,CACzB,EAEKF,CACT,CAEA,SAASM,GAAQJ,EAAiC,CAChD,IAAMK,EAAML,EAAE,OACVM,EAAI,EACJC,EAAcP,EAAEM,CAAC,GAAK,EAAIN,EAAEM,EAAI,CAAC,EACrC,OAAa,CAEX,GADAA,GAAKC,EACDD,GAAKD,EAEP,MAAO,CAAC,EAAG,CAAC,EAEd,GAAIL,EAAEM,CAAC,IAAM,IACX,MAAO,CAAC,EAAG,CAAC,EAEd,GAAIN,EAAEM,EAAI,CAAC,IAAM,KAAQN,EAAEM,EAAI,CAAC,IAAM,IACpC,OAAIA,EAAI,EAAID,EACH,CACLL,EAAEM,EAAI,CAAC,GAAK,EAAIN,EAAEM,EAAI,CAAC,EACvBN,EAAEM,EAAI,CAAC,GAAK,EAAIN,EAAEM,EAAI,CAAC,CACzB,EAEK,CAAC,EAAG,CAAC,EAEdA,GAAK,EACLC,EAAcP,EAAEM,CAAC,GAAK,EAAIN,EAAEM,EAAI,CAAC,CACnC,CACF,CF3DA,IAAME,GAAY,QAGZC,GAAgC,CACpC,KAAM,eACN,KAAM,EACN,MAAO,OACP,OAAQ,OACR,oBAAqB,EACrB,OAAQ,CACV,EAGaC,GAAN,KAAuD,CAO5D,YACmBC,EACAC,EACAC,EACAC,EACjB,CAJiB,WAAAH,EACA,eAAAC,EACA,cAAAC,EACA,mBAAAC,EAVnB,KAAQ,SAAW,GACnB,KAAQ,IAAM,IAAIC,GAClB,KAAQ,QAAyBN,GACjC,KAAQ,KAAO,IAAI,GAAAO,QAAcR,EAAS,EAC1C,KAAQ,SAAWS,CAOhB,CAEI,OAAc,CAAC,CAEf,OAAc,CACnB,KAAK,SAAW,GAChB,KAAK,QAAUR,GACf,KAAK,SAAYQ,EACjB,KAAK,IAAI,MAAM,CACjB,CAEO,IAAIC,EAAmBC,EAAeC,EAAmB,CAC9D,GAAI,MAAK,SAET,GAAI,KAAK,IAAI,QAAU,EACjB,KAAK,KAAK,IAAIF,EAAMC,EAAOC,CAAG,IAChC,KAAK,KAAK,QAAQ,EAClB,KAAK,SAAW,QAEb,CACL,IAAMC,EAAU,KAAK,IAAI,MAAMH,EAAMC,EAAOC,CAAG,EAC/C,GAAIC,IAAY,GAAI,CAClB,KAAK,SAAW,GAChB,MACF,CACA,GAAIA,EAAU,EAAG,CAEf,GADA,KAAK,QAAU,OAAO,OAAO,CAAC,EAAGZ,GAAgB,KAAK,IAAI,MAAM,EAC5D,CAAC,KAAK,QAAQ,QAAU,CAAC,KAAK,QAAQ,MAAQ,KAAK,QAAQ,KAAO,KAAK,MAAM,aAAc,CAC7F,KAAK,SAAW,GAChB,MACF,CACA,KAAK,KAAK,KAAK,KAAK,QAAQ,IAAI,EAC5B,KAAK,KAAK,IAAIS,EAAMG,EAASD,CAAG,IAClC,KAAK,KAAK,QAAQ,EAClB,KAAK,SAAW,GAEpB,CACF,CACF,CAEO,IAAIE,EAA8C,CACvD,GAAI,KAAK,SAAU,MAAO,GAE1B,IAAIC,EAAI,EACJC,EAAI,EAGJC,EAAyB,GAc7B,IAbIA,EAAOH,KACLG,EAAO,CAAC,KAAK,KAAK,IAAI,KACxB,KAAK,SAAWC,GAAU,KAAK,KAAK,KAAK,GACrCD,EAAO,KAAK,SAAS,OAAS,iBAChCF,EAAI,KAAK,SAAS,MAClBC,EAAI,KAAK,SAAS,QACdC,EAAOF,GAAKC,GAAKD,EAAIC,EAAI,KAAK,MAAM,cACtC,CAACD,EAAGC,CAAC,EAAI,KAAK,QAAQD,EAAGC,CAAC,EAAE,IAAI,KAAK,KAAK,EAC1CC,EAAOF,GAAKC,GAAKD,EAAIC,EAAI,KAAK,MAAM,cAKxC,CAACC,EACH,YAAK,KAAK,QAAQ,EACX,GAGT,IAAME,EAAO,IAAI,KAAK,CAAC,KAAK,KAAK,KAAK,EAAG,CAAE,KAAM,KAAK,SAAS,IAAK,CAAC,EAGrE,GAFA,KAAK,KAAK,QAAQ,EAEd,CAAC,OAAO,kBAAmB,CAC7B,IAAMC,EAAM,IAAI,gBAAgBD,CAAI,EAC9BE,EAAM,IAAI,MAChB,OAAO,IAAI,QAAiBC,GAAK,CAC/BD,EAAI,iBAAiB,OAAQ,IAAM,CACjC,IAAI,gBAAgBD,CAAG,EACvB,IAAMG,EAASC,EAAc,aAAa,OAAO,SAAUT,EAAGC,CAAC,EAC/DO,EAAO,WAAW,IAAI,GAAG,UAAUF,EAAK,EAAG,EAAGN,EAAGC,CAAC,EAClD,KAAK,SAAS,SAASO,CAAM,EAC7BD,EAAE,EAAI,CACR,CAAC,EACDD,EAAI,IAAMD,EAGV,WAAW,IAAME,EAAE,EAAI,EAAG,GAAI,CAChC,CAAC,CACH,CACA,OAAO,kBAAkBH,EAAM,CAAE,YAAaJ,EAAG,aAAcC,CAAE,CAAC,EAC/D,KAAKS,IACJ,KAAK,SAAS,SAASA,CAAE,EAClB,GACR,CACL,CAEQ,QAAQV,EAAWC,EAA6B,CACtD,IAAMU,EAAK,KAAK,UAAU,YAAY,IAAI,KAAK,OAASC,EAAkB,MACpEC,EAAK,KAAK,UAAU,YAAY,IAAI,KAAK,QAAUD,EAAkB,OACrEE,EAAQ,KAAK,UAAU,YAAY,IAAI,OAAO,OAASH,EAAK,KAAK,cAAc,KAC/EI,EAAS,KAAK,UAAU,YAAY,IAAI,OAAO,QAAUF,EAAK,KAAK,cAAc,KAEjFG,EAAK,KAAK,KAAK,KAAK,QAAQ,MAAQF,EAAOH,CAAE,EAC7CM,EAAK,KAAK,KAAK,KAAK,QAAQ,OAASF,EAAQF,CAAE,EACrD,GAAI,CAACG,GAAM,CAACC,EAAI,CACd,IAAMC,EAAKJ,EAAQd,EACbmB,GAAMJ,EAASF,GAAMZ,EACrBmB,EAAI,KAAK,IAAIF,EAAIC,CAAE,EACzB,OAAOC,EAAI,EAAI,CAACpB,EAAIoB,EAAGnB,EAAImB,CAAC,EAAI,CAACpB,EAAGC,CAAC,CACvC,CACA,OAAQe,EAEJ,KAAK,QAAQ,qBAAuB,CAACA,GAAM,CAACC,EAC1C,CAACD,EAAIf,EAAIe,EAAKhB,CAAC,EAAI,CAACgB,EAAIC,CAAE,EAF5B,CAACjB,EAAIiB,EAAKhB,EAAGgB,CAAE,CAGrB,CAEQ,KAAKI,EAAWC,EAAeC,EAAsB,CAC3D,OAAIF,IAAM,OAAe,EACrBA,EAAE,SAAS,GAAG,EAAU,SAASA,EAAE,MAAM,EAAG,EAAE,CAAC,EAAIC,EAAQ,IAC3DD,EAAE,SAAS,IAAI,EAAU,SAASA,EAAE,MAAM,EAAG,EAAE,CAAC,EAC7C,SAASA,CAAC,EAAIE,CACvB,CACF,EGzJA,IAAAC,EAA8E,OAI9E,IAAAC,GAAsC,QAGtC,IAAMC,GAAkB,QAGlBC,GAAkB,mBACxBA,GAAgB,IAAI,qBAAmB,EAGhC,IAAMC,GAAN,KAAyD,CAK9D,YACmBC,EACAC,EACAC,EACjB,CAHiB,WAAAF,EACA,cAAAC,EACA,mBAAAC,EAPnB,KAAQ,MAAQ,EAChB,KAAQ,SAAW,MAQjB,iBAAa,CACX,YAAa,KAAK,MAAM,WAAa,EACrC,QAASJ,GACT,aAAc,KAAK,MAAM,iBAC3B,CAAC,EAAE,KAAKK,GAAK,KAAK,KAAOA,CAAC,CAC5B,CAEO,OAAc,CAOf,KAAK,OACP,KAAK,KAAK,QAAQ,EAEjB,KAAK,KAAa,SAAS,KAAK,CAAC,EAClC,KAAK,KAAK,KAAK,EAAGL,GAAiB,KAAK,MAAM,iBAAiB,EAEnE,CAEO,KAAKM,EAAuB,CAGjC,GAFA,KAAK,MAAQ,EACb,KAAK,SAAW,GACZ,KAAK,KAAM,CACb,IAAMC,EAAYD,EAAO,OAAO,CAAC,IAAM,EAAI,EAAIE,GAC7C,KAAK,cAAc,MAAM,cAAc,aACvC,KAAK,cAAc,MAAM,eAAe,MAAM,EAChD,KAAK,KAAK,KAAKD,EAAW,KAAM,KAAK,MAAM,iBAAiB,CAC9D,CACF,CAEO,IAAIE,EAAmBC,EAAeC,EAAmB,CAC9D,GAAI,OAAK,UAAY,CAAC,KAAK,MAI3B,IADA,KAAK,OAASA,EAAMD,EAChB,KAAK,MAAQ,KAAK,MAAM,eAAgB,CAC1C,QAAQ,KAAK,gCAAgC,EAC7C,KAAK,SAAW,GAChB,KAAK,KAAK,QAAQ,EAClB,MACF,CACA,GAAI,CACF,KAAK,KAAK,OAAOD,EAAMC,EAAOC,CAAG,CACnC,OAASC,EAAG,CACV,QAAQ,KAAK,uCAAuCA,CAAC,EAAE,EACvD,KAAK,SAAW,GAChB,KAAK,KAAK,QAAQ,CACpB,EACF,CAEO,OAAOC,EAA8C,CAC1D,GAAI,KAAK,UAAY,CAACA,GAAW,CAAC,KAAK,KACrC,MAAO,GAGT,IAAMC,EAAQ,KAAK,KAAK,MAClBC,EAAS,KAAK,KAAK,OAGzB,GAAI,CAACD,GAAS,CAAEC,EACd,OAAIA,GACF,KAAK,SAAS,cAAcA,CAAM,EAE7B,GAGT,IAAMC,EAASC,EAAc,aAAa,OAAWH,EAAOC,CAAM,EAClE,OAAAC,EAAO,WAAW,IAAI,GAAG,aAAa,IAAI,UAAU,KAAK,KAAK,MAAOF,EAAOC,CAAM,EAAG,EAAG,CAAC,EACrF,KAAK,KAAK,YAAchB,IAC1B,KAAK,KAAK,QAAQ,EAEpB,KAAK,SAAS,SAASiB,CAAM,EACtB,EACT,CACF,EASA,SAASR,GAAgBU,EAAqBC,EAAgD,CAC5F,IAAIC,EAAK,EACT,GAAI,CAACD,EAGH,OAAOC,EAET,GAAIF,EAAK,UAAU,EACjB,GAAIA,EAAK,YAAY,EACnBE,EAAKC,GAAUF,EAAO,WAAW,IAAI,UAC5BD,EAAK,QAAQ,EAAG,CACzB,IAAMI,EAAKJ,EAAK,YAAqC,WAAWA,EAAK,WAAW,CAAC,EACjFE,KAAK,cAAW,GAAGE,CAAC,CACtB,MACEF,EAAKC,GAAUF,EAAO,KAAKD,EAAK,WAAW,CAAC,EAAE,IAAI,UAGhDA,EAAK,YAAY,EACnBE,EAAKC,GAAUF,EAAO,WAAW,IAAI,UAC5BD,EAAK,QAAQ,EAAG,CACzB,IAAMI,EAAKJ,EAAK,YAAqC,WAAWA,EAAK,WAAW,CAAC,EACjFE,KAAK,cAAW,GAAGE,CAAC,CACtB,MACEF,EAAKC,GAAUF,EAAO,KAAKD,EAAK,WAAW,CAAC,EAAE,IAAI,EAGtD,OAAOE,CACT,CAGA,SAASC,GAAUE,EAAyB,CAC1C,OAAI,aAAmBA,GACfA,EAAQ,MAAS,IAAMA,IAAU,EAAI,MAAS,IAAMA,IAAU,GAAK,MAAS,EAAIA,IAAU,GAAK,GACzG,CCxIA,IAAMC,GAAsC,CAC1C,kBAAmB,GACnB,WAAY,SACZ,aAAc,GACd,eAAgB,GAChB,kBAAmB,IACnB,eAAgB,KAChB,aAAc,IACd,gBAAiB,GACjB,WAAY,GACZ,aAAc,GAChB,EAGMC,GAAyB,KAsBxB,IAAMC,GAAN,KAAuD,CAS5D,YAAYC,EAAoC,CAJhD,KAAQ,aAA8B,CAAC,EAEvC,KAAQ,UAAwC,IAAI,IAGlD,KAAK,MAAQ,OAAO,OAAO,CAAC,EAAGC,GAAiBD,CAAI,EACpD,KAAK,aAAe,OAAO,OAAO,CAAC,EAAGC,GAAiBD,CAAI,CAC7D,CAEO,SAAgB,CACrB,QAAWE,KAAO,KAAK,aACrBA,EAAI,QAAQ,EAEd,KAAK,aAAa,OAAS,EAC3B,KAAK,UAAU,MAAM,CACvB,CAEQ,iBAAiBC,EAA2B,CAClD,QAAWD,KAAOC,EAChB,KAAK,aAAa,KAAKD,CAAG,CAE9B,CAEO,SAASE,EAA8B,CAQ5C,GAPA,KAAK,UAAYA,EAGjB,KAAK,UAAY,IAAIC,EAAcD,CAAQ,EAC3C,KAAK,SAAW,IAAIE,GAAaF,EAAU,KAAK,UAAW,KAAK,KAAK,EAGjE,KAAK,MAAM,kBAAmB,CAMhC,IAAMG,EAAYH,EAAS,QAAQ,eAAiB,CAAC,EACrDG,EAAU,iBAAmB,GAC7BA,EAAU,kBAAoB,GAC9BA,EAAU,gBAAkB,GAC5BH,EAAS,QAAQ,cAAgBG,CACnC,CAiCA,GA/BA,KAAK,cACH,KAAK,UACL,KAAK,SAGLH,EAAS,OAAO,mBAAmB,CAAE,OAAQ,IAAK,MAAO,GAAI,EAAGI,GAAU,KAAK,QAAQA,CAAM,CAAC,EAC9FJ,EAAS,OAAO,mBAAmB,CAAE,OAAQ,IAAK,MAAO,GAAI,EAAGI,GAAU,KAAK,QAAQA,CAAM,CAAC,EAC9FJ,EAAS,OAAO,mBAAmB,CAAE,MAAO,GAAI,EAAGI,GAAU,KAAK,KAAKA,CAAM,CAAC,EAC9EJ,EAAS,OAAO,mBAAmB,CAAE,OAAQ,IAAK,MAAO,GAAI,EAAGI,GAAU,KAAK,yBAAyBA,CAAM,CAAC,EAG/GJ,EAAS,SAASK,GAAS,KAAK,UAAU,OAAOA,CAAK,CAAC,EAQvDL,EAAS,OAAO,mBAAmB,CAAE,cAAe,IAAK,MAAO,GAAI,EAAG,IAAM,KAAK,MAAM,CAAC,EACzFA,EAAS,OAAO,mBAAmB,CAAE,MAAO,GAAI,EAAG,IAAM,KAAK,MAAM,CAAC,EACrEA,EAAS,MAAM,cAAc,eAAe,IAAM,KAAK,MAAM,CAAC,EAG9DA,EAAS,OAAO,eAAe,IAAM,KAAK,UAAU,cAAc,CAAC,EAGnEA,EAAS,SAASM,GAAW,KAAK,UAAU,eAAeA,CAAO,CAAC,CACrE,EAGI,KAAK,MAAM,aAAc,CAC3B,IAAMC,EAAe,IAAIC,GAAa,KAAK,MAAO,KAAK,SAAWR,CAAQ,EAC1E,KAAK,UAAU,IAAI,QAASO,CAAY,EACxC,KAAK,cACHP,EAAS,MAAM,cAAc,QAAQ,mBAAmB,CAAE,MAAO,GAAI,EAAGO,CAAY,CACtF,CACF,CAGA,GAAI,KAAK,MAAM,WAAY,CACzB,IAAME,EAAa,IAAIC,GAAW,KAAK,MAAO,KAAK,UAAY,KAAK,SAAWV,CAAQ,EACvF,KAAK,UAAU,IAAI,MAAOS,CAAU,EACpC,KAAK,cACHT,EAAS,MAAM,cAAc,QAAQ,mBAAmB,KAAMS,CAAU,CAC1E,CACF,CACF,CAGO,OAAiB,CAEtB,KAAK,MAAM,eAAiB,KAAK,aAAa,eAC9C,KAAK,MAAM,kBAAoB,KAAK,aAAa,kBAEjD,KAAK,UAAU,MAAM,EAErB,QAAWE,KAAW,KAAK,UAAU,OAAO,EAC1CA,EAAQ,MAAM,EAEhB,MAAO,EACT,CAEA,IAAW,cAAuB,CAChC,OAAO,KAAK,UAAU,SAAS,GAAK,EACtC,CAEA,IAAW,aAAaC,EAAe,CACrC,KAAK,UAAU,SAASA,CAAK,EAC7B,KAAK,MAAM,aAAeA,CAC5B,CAEA,IAAW,cAAuB,CAChC,OAAI,KAAK,SACA,KAAK,SAAS,SAAS,EAEzB,EACT,CAEA,IAAW,iBAA2B,CACpC,OAAO,KAAK,MAAM,eACpB,CAEA,IAAW,gBAAgBC,EAAgB,CACzC,KAAK,MAAM,gBAAkBA,EAC7B,KAAK,WAAW,gBAAgBA,CAAK,CACvC,CAEO,qBAAqBC,EAAWC,EAA0C,CAC/E,OAAO,KAAK,UAAU,qBAAqBD,EAAGC,CAAC,CACjD,CAEO,wBAAwBD,EAAWC,EAA0C,CAClF,OAAO,KAAK,UAAU,wBAAwBD,EAAGC,CAAC,CACpD,CAEQ,QAAQC,EAAiB,CAC/B,KAAK,WAAW,MAAM,YAAY,iBAAiBA,CAAC,CACtD,CAEQ,QAAQZ,EAAwC,CACtD,QAASa,EAAI,EAAGA,EAAIb,EAAO,OAAQ,EAAEa,EACnC,OAAQb,EAAOa,CAAC,EAAG,CACjB,IAAK,IACH,KAAK,MAAM,eAAiB,GAC5B,KACJ,CAEF,MAAO,EACT,CAEQ,QAAQb,EAAwC,CACtD,QAASa,EAAI,EAAGA,EAAIb,EAAO,OAAQ,EAAEa,EACnC,OAAQb,EAAOa,CAAC,EAAG,CACjB,IAAK,IACH,KAAK,MAAM,eAAiB,GAC5B,KACJ,CAEF,MAAO,EACT,CAGQ,KAAKb,EAAwC,CACnD,OAAIA,EAAO,CAAC,EACH,GAOL,KAAK,MAAM,cACb,KAAK,QAAQ,kBAAkB,EACxB,IAEF,EACT,CAYQ,yBAAyBA,EAAwC,CACvE,GAAIA,EAAO,OAAS,EAClB,MAAO,GAET,GAAIA,EAAO,CAAC,IAAM,EAChB,OAAQA,EAAO,CAAC,EAAG,CACjB,IAAK,GACH,YAAK,QAAQ,SAASA,EAAO,CAAC,CAAC,MAAwB,KAAK,MAAM,iBAAiB,GAAG,EAC/E,GACT,IAAK,GACH,KAAK,MAAM,kBAAoB,KAAK,aAAa,kBACjD,KAAK,QAAQ,SAASA,EAAO,CAAC,CAAC,MAAwB,KAAK,MAAM,iBAAiB,GAAG,EAEtF,QAAWO,KAAW,KAAK,UAAU,OAAO,EAC1CA,EAAQ,MAAM,EAEhB,MAAO,GACT,IAAK,GACH,OAAIP,EAAO,OAAS,GAAK,EAAEA,EAAO,CAAC,YAAa,QAAUA,EAAO,CAAC,GAAKc,IACrE,KAAK,MAAM,kBAAoBd,EAAO,CAAC,EACvC,KAAK,QAAQ,SAASA,EAAO,CAAC,CAAC,MAAwB,KAAK,MAAM,iBAAiB,GAAG,GAEtF,KAAK,QAAQ,SAASA,EAAO,CAAC,CAAC,KAA4B,EAEtD,GACT,IAAK,GACH,YAAK,QAAQ,SAASA,EAAO,CAAC,CAAC,MAAwBc,EAAsB,GAAG,EACzE,GACT,QACE,YAAK,QAAQ,SAASd,EAAO,CAAC,CAAC,KAA4B,EACpD,EACX,CAEF,GAAIA,EAAO,CAAC,IAAM,EAChB,OAAQA,EAAO,CAAC,EAAG,CAEjB,IAAK,GACH,IAAIe,EAAQ,KAAK,WAAW,YAAY,IAAI,OAAO,MAC/CC,EAAS,KAAK,WAAW,YAAY,IAAI,OAAO,OACpD,GAAI,CAACD,GAAS,CAACC,EAAQ,CAGrB,IAAMC,EAAWC,EACjBH,GAAS,KAAK,WAAW,MAAQ,IAAME,EAAS,MAChDD,GAAU,KAAK,WAAW,MAAQ,IAAMC,EAAS,MACnD,CACA,GAAIF,EAAQC,EAAS,KAAK,MAAM,WAC9B,KAAK,QAAQ,SAAShB,EAAO,CAAC,CAAC,MAAwBe,EAAM,QAAQ,CAAC,CAAC,IAAIC,EAAO,QAAQ,CAAC,CAAC,GAAG,MAC1F,CAEL,IAAMN,EAAI,KAAK,MAAM,KAAK,KAAK,KAAK,MAAM,UAAU,CAAC,EACrD,KAAK,QAAQ,SAASV,EAAO,CAAC,CAAC,MAAwBU,CAAC,IAAIA,CAAC,GAAG,CAClE,CACA,MAAO,GACT,IAAK,GAEH,IAAMA,EAAI,KAAK,MAAM,KAAK,KAAK,KAAK,MAAM,UAAU,CAAC,EACrD,YAAK,QAAQ,SAASV,EAAO,CAAC,CAAC,MAAwBU,CAAC,IAAIA,CAAC,GAAG,EACzD,GACT,QACE,YAAK,QAAQ,SAASV,EAAO,CAAC,CAAC,KAA4B,EACpD,EACX,CAGF,YAAK,QAAQ,SAASA,EAAO,CAAC,CAAC,KAA0B,EAClD,EACT,CACF", "names": ["exports", "red", "n", "green", "blue", "alpha", "toRGBA8888", "g", "b", "a", "fromRGBA8888", "color", "nearestColorIndex", "palette", "r", "min", "idx", "i", "dr", "dg", "db", "d", "clamp", "low", "high", "value", "h2c", "t1", "t2", "c", "HLStoRGB", "h", "l", "s", "v", "normalizeRGB", "normalizeHLS", "p", "_dec", "s", "bs", "r", "InWasm", "def", "t", "d", "bytes", "mod", "W", "m", "e", "exports", "require_Base64Decoder_wasm", "__commonJSMin", "exports", "inwasm_1", "wasmDecode", "MAP", "el", "D", "i", "EMPTY", "Base64Decoder", "keepSize", "size", "m", "bytes", "data", "start", "end", "exports", "Colors_1", "wasm_1", "decodeBase64", "s", "bytestring", "result", "WASM_BYTES", "WASM_MODULE", "NULL_CANVAS", "CallbackProxy", "width", "mode", "DEFAULT_OPTIONS", "DecoderAsync", "opts", "cbProxy", "importObj", "inst", "Decoder", "exports", "_instance", "_cbProxy", "module", "pixels", "offset", "additionalPixels", "newCanvas", "adv", "remaining", "c", "i", "fillColor", "palette", "paletteLimit", "truncate", "data", "start", "end", "p", "length", "j", "currentWidth", "escape", "final", "finalOffset", "bw", "currentHeight", "decode", "dec", "decodeAsync", "import_Colors", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ErrorNoTelemetry", "listener", "newUnexpectedErrorHandler", "<PERSON><PERSON><PERSON><PERSON>", "ErrorNoTelemetry", "_ErrorNoTelemetry", "msg", "err", "result", "findLastIdxMonotonous", "array", "predicate", "startIdx", "endIdxEx", "i", "j", "k", "_MonotonousArray", "_array", "predicate", "item", "idx", "findLastIdxMonotonous", "MonotonousArray", "CompareResult", "is<PERSON><PERSON><PERSON><PERSON>", "result", "isLessThanOrEqual", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isNeitherLessOrGreaterThan", "compareBy", "selector", "comparator", "a", "b", "numberComparator", "a", "b", "_CallbackIterable", "iterate", "handler", "item", "result", "predicate", "cb", "mapFn", "comparator", "first", "CompareResult", "_callback", "CallbackIterable", "groupBy", "data", "groupFn", "result", "element", "key", "target", "_a", "_b", "SetWith<PERSON>ey", "values", "to<PERSON><PERSON>", "value", "key", "entry", "callbackfn", "thisArg", "SetMap", "key", "value", "values", "fn", "createSingleCallFunction", "fn", "fnDidRunCallback", "_this", "didCall", "result", "Iterable", "is", "thing", "_empty", "empty", "single", "element", "wrap", "iterableOrElement", "from", "iterable", "reverse", "array", "i", "isEmpty", "first", "some", "predicate", "find", "filter", "map", "fn", "index", "flatMap", "concat", "iterables", "reduce", "reducer", "initialValue", "value", "slice", "arr", "to", "consume", "atMost", "consumed", "iterator", "next", "asyncToArray", "result", "item", "TRACK_DISPOSABLES", "disposableTracker", "_DisposableTracker", "d", "val", "data", "child", "parent", "x", "disposable", "cache", "cacheValue", "result", "rootParentCache", "v", "k", "maxReported", "preComputedLeaks", "uncoveredLeakingObjs", "leakingObjects", "info", "leakingObjsSet", "o", "getStackTracePath", "leaking", "removePrefix", "array", "linesToRemove", "regexp", "lines", "p", "stackTraceStarts", "SetMap", "stackTracePath", "i", "compareBy", "l", "numberComparator", "message", "stackTraceFormattedLines", "line", "prevStarts", "continuations", "groupBy", "cont", "set", "DisposableTracker", "setDisposableTracker", "tracker", "__is_disposable_tracked__", "stack", "Disposable", "trackDisposable", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "setParentOfDisposable", "dispose", "arg", "Iterable", "errors", "d", "e", "toDisposable", "fn", "self", "trackDisposable", "createSingleCallFunction", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_DisposableStore", "dispose", "o", "setParentOfDisposable", "DisposableStore", "Disposable", "MutableDisposable", "value", "oldValue", "PLACEHOLDER_LENGTH", "PLACEHOLDER_HEIGHT", "Image<PERSON><PERSON><PERSON>", "_Image<PERSON><PERSON><PERSON>", "Disposable", "_terminal", "MutableDisposable", "parent", "option", "toDisposable", "localDocument", "width", "height", "canvas", "ctx", "buffer", "imgData", "img", "value", "start", "end", "imgSpec", "tileId", "col", "row", "count", "cols", "sx", "sy", "dx", "dy", "finalWidth", "finalHeight", "spec", "currentWidth", "currentHeight", "originalWidth", "originalHeight", "renderer", "b<PERSON><PERSON><PERSON>", "blueprint", "d32", "black", "white", "y", "shift", "offset", "x", "ctx2", "i", "bitmap", "CELL_SIZE_DEFAULT", "ExtendedAttrsImage", "_ExtendedAttrsImage", "ext", "urlId", "imageId", "tileId", "value", "val", "EMPTY_ATTRS", "ImageStorage", "_terminal", "_renderer", "_opts", "e", "spec", "storedPixels", "id", "zero", "height", "cellSize", "rows", "i", "img", "cols", "buffer", "termCols", "termRows", "originX", "originY", "offset", "tileCount", "row", "line", "col", "<PERSON><PERSON><PERSON><PERSON>", "imgSpec", "range", "start", "end", "startTile", "startCol", "count", "metrics", "oldCol", "tilesPerRow", "hasData", "rightCol", "lastTile", "expandCol", "x", "y", "orig", "canvas", "Image<PERSON><PERSON><PERSON>", "room", "used", "current", "old", "oldSpec", "imgId", "import_Base64Decoder", "toStr", "data", "s", "i", "toInt", "v", "toSize", "to<PERSON>ame", "bs", "b", "DECODERS", "FILE_MARKER", "MAX_FIELDCHARS", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "start", "end", "state", "pos", "buffer", "c", "k", "UNSUPPORTED_TYPE", "imageType", "d", "d32", "width", "height", "jpgSize", "len", "i", "blockLength", "KEEP_DATA", "DEFAULT_HEADER", "IIPHandler", "_opts", "_renderer", "_storage", "_coreTerminal", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Base64Decoder", "UNSUPPORTED_TYPE", "data", "start", "end", "dataPos", "success", "w", "h", "cond", "imageType", "blob", "url", "img", "r", "canvas", "Image<PERSON><PERSON><PERSON>", "bm", "cw", "CELL_SIZE_DEFAULT", "ch", "width", "height", "rw", "rh", "wf", "hf", "f", "s", "total", "cdim", "import_Colors", "import_Decoder", "MEM_PERMA_LIMIT", "DEFAULT_PALETTE", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_opts", "_storage", "_coreTerminal", "d", "params", "fillColor", "extractActiveBg", "data", "start", "end", "e", "success", "width", "height", "canvas", "Image<PERSON><PERSON><PERSON>", "attr", "colors", "bg", "convertLe", "t", "color", "DEFAULT_OPTIONS", "MAX_SIXEL_PALETTE_SIZE", "ImageAddon", "opts", "DEFAULT_OPTIONS", "obj", "args", "terminal", "Image<PERSON><PERSON><PERSON>", "ImageStorage", "windowOps", "params", "range", "metrics", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ii<PERSON><PERSON><PERSON><PERSON>", "IIPHandler", "handler", "limit", "value", "x", "y", "s", "i", "MAX_SIXEL_PALETTE_SIZE", "width", "height", "cellSize", "CELL_SIZE_DEFAULT"]}