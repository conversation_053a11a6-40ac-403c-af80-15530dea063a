/**
 * Copyright (c) 2014-2024 The xterm.js authors. All rights reserved.
 * @license MIT
 *
 * Copyright (c) 2012-2013, <PERSON> (MIT License)
 * @license MIT
 *
 * Originally forked from (with the author's permission):
 *   <PERSON><PERSON><PERSON>'s javascript vt100 for jslinux:
 *   http://bellard.org/jslinux/
 *   Copyright (c) 2011 F<PERSON><PERSON>
 */
/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/
var U="3.7.7",$=U,p=typeof Buffer=="function",_=typeof TextDecoder=="function"?new TextDecoder:void 0,C=typeof TextEncoder=="function"?new TextEncoder:void 0,N="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",f=Array.prototype.slice.call(N),u=(e=>{let t={};return e.forEach((r,o)=>t[r]=o),t})(f),k=/^(?:[A-Za-z\d+\/]{4})*?(?:[A-Za-z\d+\/]{2}(?:==)?|[A-Za-z\d+\/]{3}=?)?$/,n=String.fromCharCode.bind(String),B=typeof Uint8Array.from=="function"?Uint8Array.from.bind(Uint8Array):e=>new Uint8Array(Array.prototype.slice.call(e,0)),S=e=>e.replace(/=/g,"").replace(/[+\/]/g,t=>t=="+"?"-":"_"),I=e=>e.replace(/[^A-Za-z0-9\+\/]/g,""),F=e=>{let t,r,o,s,i="",l=e.length%3;for(let a=0;a<e.length;){if((r=e.charCodeAt(a++))>255||(o=e.charCodeAt(a++))>255||(s=e.charCodeAt(a++))>255)throw new TypeError("invalid character found");t=r<<16|o<<8|s,i+=f[t>>18&63]+f[t>>12&63]+f[t>>6&63]+f[t&63]}return l?i.slice(0,l-3)+"===".substring(l):i},m=typeof btoa=="function"?e=>btoa(e):p?e=>Buffer.from(e,"binary").toString("base64"):F,b=p?e=>Buffer.from(e).toString("base64"):e=>{let r=[];for(let o=0,s=e.length;o<s;o+=4096)r.push(n.apply(null,e.subarray(o,o+4096)));return m(r.join(""))},x=(e,t=!1)=>t?S(b(e)):b(e),H=e=>{if(e.length<2){var t=e.charCodeAt(0);return t<128?e:t<2048?n(192|t>>>6)+n(128|t&63):n(224|t>>>12&15)+n(128|t>>>6&63)+n(128|t&63)}else{var t=65536+(e.charCodeAt(0)-55296)*1024+(e.charCodeAt(1)-56320);return n(240|t>>>18&7)+n(128|t>>>12&63)+n(128|t>>>6&63)+n(128|t&63)}},J=/[\uD800-\uDBFF][\uDC00-\uDFFFF]|[^\x00-\x7F]/g,P=e=>e.replace(J,H),T=p?e=>Buffer.from(e,"utf8").toString("base64"):C?e=>b(C.encode(e)):e=>m(P(e)),c=(e,t=!1)=>t?S(T(e)):T(e),v=e=>c(e,!0),q=/[\xC0-\xDF][\x80-\xBF]|[\xE0-\xEF][\x80-\xBF]{2}|[\xF0-\xF7][\x80-\xBF]{3}/g,G=e=>{switch(e.length){case 4:var t=(7&e.charCodeAt(0))<<18|(63&e.charCodeAt(1))<<12|(63&e.charCodeAt(2))<<6|63&e.charCodeAt(3),r=t-65536;return n((r>>>10)+55296)+n((r&1023)+56320);case 3:return n((15&e.charCodeAt(0))<<12|(63&e.charCodeAt(1))<<6|63&e.charCodeAt(2));default:return n((31&e.charCodeAt(0))<<6|63&e.charCodeAt(1))}},D=e=>e.replace(q,G),w=e=>{if(e=e.replace(/\s+/g,""),!k.test(e))throw new TypeError("malformed base64.");e+="==".slice(2-(e.length&3));let t,r="",o,s;for(let i=0;i<e.length;)t=u[e.charAt(i++)]<<18|u[e.charAt(i++)]<<12|(o=u[e.charAt(i++)])<<6|(s=u[e.charAt(i++)]),r+=o===64?n(t>>16&255):s===64?n(t>>16&255,t>>8&255):n(t>>16&255,t>>8&255,t&255);return r},y=typeof atob=="function"?e=>atob(I(e)):p?e=>Buffer.from(e,"base64").toString("binary"):w,R=p?e=>B(Buffer.from(e,"base64")):e=>B(y(e).split("").map(t=>t.charCodeAt(0))),E=e=>R(O(e)),K=p?e=>Buffer.from(e,"base64").toString("utf8"):_?e=>_.decode(R(e)):e=>D(y(e)),O=e=>I(e.replace(/[-_]/g,t=>t=="-"?"+":"/")),h=e=>K(O(e)),M=e=>{if(typeof e!="string")return!1;let t=e.replace(/\s+/g,"").replace(/={0,2}$/,"");return!/[^\s0-9a-zA-Z\+/]/.test(t)||!/[^\s0-9a-zA-Z\-_]/.test(t)},z=e=>({value:e,enumerable:!1,writable:!0,configurable:!0}),Z=function(){let e=(t,r)=>Object.defineProperty(String.prototype,t,z(r));e("fromBase64",function(){return h(this)}),e("toBase64",function(t){return c(this,t)}),e("toBase64URI",function(){return c(this,!0)}),e("toBase64URL",function(){return c(this,!0)}),e("toUint8Array",function(){return E(this)})},V=function(){let e=(t,r)=>Object.defineProperty(Uint8Array.prototype,t,z(r));e("toBase64",function(t){return x(this,t)}),e("toBase64URI",function(){return x(this,!0)}),e("toBase64URL",function(){return x(this,!0)})},Q=()=>{Z(),V()},d={version:U,VERSION:$,atob:y,atobPolyfill:w,btoa:m,btoaPolyfill:F,fromBase64:h,toBase64:c,encode:c,encodeURI:v,encodeURL:v,utob:P,btou:D,decode:h,isValid:M,fromUint8Array:x,toUint8Array:E,extendString:Z,extendUint8Array:V,extendBuiltins:Q};var L=class{constructor(t=new A,r=new g){this._base64=t;this._provider=r}activate(t){this._terminal=t,this._disposable=t.parser.registerOscHandler(52,r=>this._setOrReportClipboard(r))}dispose(){return this._disposable?.dispose()}_readText(t,r){let o=this._base64.encodeText(r);this._terminal?.input(`\x1B]52;${t};${o}\x07`,!1)}_setOrReportClipboard(t){let r=t.split(";");if(r.length<2)return!0;let o=r[0],s=r[1];if(s==="?"){let a=this._provider.readText(o);return a instanceof Promise?a.then(j=>(this._readText(o,j),!0)):(this._readText(o,a),!0)}let i="";try{i=this._base64.decodeText(s)}catch{}let l=this._provider.writeText(o,i);return l instanceof Promise?l.then(()=>!0):!0}},g=class{async readText(t){return t!=="c"?Promise.resolve(""):navigator.clipboard.readText()}async writeText(t,r){return t!=="c"?Promise.resolve():navigator.clipboard.writeText(r)}},A=class{encodeText(t){return d.encode(t)}decodeText(t){let r=d.decode(t);return!d.isValid(t)||d.encode(r)!==t?"":r}};export{A as Base64,g as BrowserClipboardProvider,L as ClipboardAddon};
//# sourceMappingURL=addon-clipboard.mjs.map
